name: Deploy develop

run-name: ${{ github.actor }} is deploying develop on Server

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to servers       #1
        uses: appleboy/ssh-action@master

        with:
          host: ${{ secrets.MOB2O_HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.MOB2O_SSHKEY }}
          script: "cd /var/www/Mob2oDev && ./.scripts/deploy.sh"
