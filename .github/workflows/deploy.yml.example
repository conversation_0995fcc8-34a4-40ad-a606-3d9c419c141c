## YAML Template.
---
#name: learn-github-actions
#run-name: ${{ github.actor }} is learning GitHub Actions
#on:
#  push:
#    branches:
#      - develop
#jobs:
#  deploy:
#    runs-on: ubuntu-latest
#    steps:
#      - uses: actions/checkout@v2
#      - name: Deploy to servers
#        uses: appleboy/ssh-action@master
#
#        with:
#          host: ${{ secrets.DEV_HOST }}
#          username: ${{ secrets.DEV_USERNAME }}
#          port: ${{ secrets.PORT }}
#          key: ${{ secrets.DEV_SSHKEY }}
#          script: "cd /var/www/Bonsae2.0 && ./.scripts/deploy.sh"
