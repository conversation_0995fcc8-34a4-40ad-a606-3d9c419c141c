<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('step_navigations', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->bigInteger('step_id');
            $table->enum('condition_type', ['button_click', 'text_match', 'regex', 'default'])
                  ->comment('Type of condition for navigation');
            $table->json('condition_data')
                  ->nullable()
                  ->comment('Condition-specific data (button_id, text, pattern, etc.)');
            $table->string('target_step_identifier')
                  ->comment('Identifier of the target step to navigate to');
            $table->integer('priority')
                  ->default(0)
                  ->comment('Priority for condition evaluation (lower = higher priority)');
            $table->boolean('is_active')
                  ->default(true)
                  ->comment('Whether this navigation rule is active');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('organization_id');
            $table->index('step_id');
            $table->index(['step_id', 'priority']);
            $table->index(['step_id', 'is_active']);
            $table->index(['organization_id', 'step_id']);

            // Foreign key constraints
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('step_id')->references('id')->on('steps')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('step_navigations');
    }
};
