<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing steps data to new structure
        $steps = DB::table('steps')->get();

        foreach ($steps as $step) {
            $stepType = $this->determineStepType($step);
            $configuration = $this->generateConfiguration($step, $stepType);

            DB::table('steps')
                ->where('id', $step->id)
                ->update([
                    'step_type' => $stepType,
                    'configuration' => json_encode($configuration),
                    'navigation_rules' => null, // Will be set manually later if needed
                    'timeout_seconds' => null, // Default timeout
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset new fields to null
        DB::table('steps')->update([
            'step_type' => null,
            'configuration' => null,
            'navigation_rules' => null,
            'timeout_seconds' => null,
        ]);
    }

    /**
     * Determine step type from legacy boolean fields
     */
    private function determineStepType($step): string
    {
        if ($step->is_message || $step->type === 'text' || $step->type === 'message') {
            return 'message';
        } elseif ($step->is_interactive || $step->type === 'interactive') {
            return 'interactive';
        } elseif ($step->is_input || $step->type === 'input') {
            return 'input';
        } elseif ($step->is_command || $step->type === 'command') {
            return 'command';
        } else {
            // Default fallback
            return 'message';
        }
    }

    /**
     * Generate configuration based on step type and existing data
     */
    private function generateConfiguration($step, string $stepType): array
    {
        $configuration = match ($stepType) {
            'message' => [
                'text' => '',
                'media_type' => null,
                'media_url' => null,
            ],
            'interactive' => [
                'text' => '',
                'buttons' => [],
                'list_items' => [],
                'interaction_type' => 'buttons',
            ],
            'input' => [
                'prompt' => '',
                'input_type' => 'text',
                'validation_rules' => [],
                'field_mapping' => $step->input ?? '',
            ],
            'command' => [
                'command' => '',
                'parameters' => [],
            ],
            default => [],
        };

        // Try to extract text from JSON if available
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData)) {
                if (isset($jsonData['text'])) {
                    $configuration['text'] = $jsonData['text'];
                }
                if (isset($jsonData['prompt'])) {
                    $configuration['prompt'] = $jsonData['prompt'];
                }
                if (isset($jsonData['command'])) {
                    $configuration['command'] = $jsonData['command'];
                }
            }
        }

        return $configuration;
    }
};
