<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Client>
 */
class ClientFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'name' => fake()->name(),
            'phone' => fake()->phoneNumber(),
            'email' => fake()->optional()->safeEmail(),
            'profession' => fake()->optional()->jobTitle(),
            'birthdate' => fake()->optional()->date(),
            'cpf' => fake()->optional()->numerify('###########'),
            'cnpj' => fake()->optional()->numerify('##############'),
            'service' => fake()->optional()->sentence(),
            'address' => fake()->optional()->streetAddress(),
            'number' => fake()->optional()->buildingNumber(),
            'neighborhood' => fake()->optional()->citySuffix(),
            'cep' => fake()->optional()->postcode(),
            'complement' => fake()->optional()->secondaryAddress(),
            'civil_state' => fake()->optional()->randomElement(['single', 'married', 'divorced', 'widowed']),
            'description' => fake()->optional()->sentence(),
        ];
    }

    /**
     * Create a client with CPF (individual).
     */
    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'cpf' => fake()->numerify('###########'),
            'cnpj' => null,
            'civil_state' => fake()->randomElement(['single', 'married', 'divorced', 'widowed']),
        ]);
    }

    /**
     * Create a client with CNPJ (company).
     */
    public function company(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->company(),
            'cpf' => null,
            'cnpj' => fake()->numerify('##############'),
            'civil_state' => null,
            'profession' => null,
            'birthdate' => null,
        ]);
    }

    /**
     * Create a client with complete address.
     */
    public function withCompleteAddress(): static
    {
        return $this->state(fn (array $attributes) => [
            'address' => fake()->streetAddress(),
            'number' => fake()->buildingNumber(),
            'neighborhood' => fake()->citySuffix(),
            'cep' => fake()->postcode(),
            'complement' => fake()->secondaryAddress(),
        ]);
    }

    /**
     * Create a client with minimal information.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone' => fake()->phoneNumber(),
            'email' => null,
            'profession' => null,
            'birthdate' => null,
            'cpf' => null,
            'cnpj' => null,
            'service' => null,
            'address' => null,
            'number' => null,
            'neighborhood' => null,
            'cep' => null,
            'complement' => null,
            'civil_state' => null,
            'description' => null,
        ]);
    }
}
