<?php

namespace Database\Factories;

use App\Models\MessageDeliveryAttempt;
use App\Models\Message;
use App\Enums\MessageStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageDeliveryAttempt>
 */
class MessageDeliveryAttemptFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MessageDeliveryAttempt::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'message_id' => Message::factory(),
            'attempt_number' => $this->faker->numberBetween(1, 5),
            'status' => $this->faker->randomElement(MessageStatus::cases()),
            'error_message' => $this->faker->optional()->sentence(),
            'whatsapp_response_json' => [
                'message_id' => $this->faker->uuid(),
                'status' => 'sent',
                'timestamp' => Carbon::now()->toISOString(),
            ],
            'attempted_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the delivery attempt was successful.
     */
    public function successful(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => MessageStatus::is_sent,
                'error_message' => null,
                'whatsapp_response_json' => [
                    'message_id' => $this->faker->uuid(),
                    'status' => 'sent',
                    'timestamp' => Carbon::now()->toISOString(),
                    'recipient_id' => $this->faker->phoneNumber(),
                ],
            ];
        });
    }

    /**
     * Indicate that the delivery attempt failed.
     */
    public function failed(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => MessageStatus::is_fail,
                'error_message' => $this->faker->randomElement([
                    'Invalid phone number',
                    'Rate limit exceeded',
                    'Network timeout',
                    'WhatsApp API error',
                    'Recipient blocked'
                ]),
                'whatsapp_response_json' => [
                    'error' => [
                        'code' => $this->faker->numberBetween(400, 500),
                        'message' => 'Delivery failed',
                    ],
                    'timestamp' => Carbon::now()->toISOString(),
                ],
            ];
        });
    }

    /**
     * Indicate that this is a retry attempt.
     */
    public function retry(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'attempt_number' => $this->faker->numberBetween(2, 5),
                'attempted_at' => Carbon::now()->addMinutes($this->faker->numberBetween(5, 60)),
            ];
        });
    }
}
