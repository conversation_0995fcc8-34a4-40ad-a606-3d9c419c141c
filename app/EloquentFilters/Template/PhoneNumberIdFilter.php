<?php

namespace App\EloquentFilters\Template;

use EloquentBuilder;
use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class PhoneNumberIdFilter implements Filter
{
    /**
     * Apply the filter to the given query.
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function apply(Builder $builder, $value): Builder
    {
        return $builder->where('phone_number_id', $value);
    }
}