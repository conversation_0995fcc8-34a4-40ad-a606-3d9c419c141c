<?php

namespace App\EloquentFilters\TelegramMessage;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class BotIdFilter extends Filter
{
    /**
     * Apply the condition to the query.
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        return $builder->whereHas('chat', function ($query) use ($value) {
            $query->where('telegram_chats.bot_id', '=', $value);
        });
    }
}
