<?php

namespace App\EloquentFilters\Project;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class ClientFilter extends Filter
{
    /**
     * Apply the condition to the query.
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        return $builder->whereHas('client', function ($query) use ($value) {
            $query->where('clients.first_name' , 'LIKE', '%'.$value.'%');
            $query->orWhere('clients.last_name' , 'LIKE', '%'.$value.'%');
        });
    }
}
