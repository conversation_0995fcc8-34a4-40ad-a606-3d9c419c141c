<?php

namespace App\EloquentFilters\PhoneNumber;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class PhoneNumberFilter extends Filter
{
    /**
     * Apply the condition to the query.
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        return $builder->where('phone_number', 'LIKE', '%'.$value.'%');
    }
}