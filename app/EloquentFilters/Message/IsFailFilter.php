<?php

namespace App\EloquentFilters\Message;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class IsFailFilter extends Filter
{
    /**
     * Apply the condition to the query.
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        if (is_null($value)) {
            return $builder;
        }

        if ($value){
            $value = (bool) $value;
        }

        return $builder->where('is_fail', '=', $value);
    }
}
