<?php

namespace App\Repositories;

use App\Domains\ChatBot\Step as StepDomain;
use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StepFilters;
use App\Enums\StepType;
use App\Factories\ChatBot\StepFactory;
use App\Models\Step;
use EloquentBuilder;
use Fouladgar\EloquentBuilder\Exceptions\FilterException;

class StepRepository
{
    private StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory){
        $this->stepFactory = $stepFactory;
    }

    /**
     * @param StepFilters $filters
     * @param OrderBy $orderBy
     * @return array
     * @throws FilterException
     * @throws \Throwable
     */
    public function fetchAll(StepFilters $filters, OrderBy $orderBy) : array {
        $steps = [];

        $models = EloquentBuilder::to(Step::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $steps[] = $this->stepFactory->buildFromModel($model);
        }

        return [
            'data' => $steps,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, StepFilters $filters, OrderBy $orderBy) : array {
        $steps = [];

        $models = EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $steps[] = $this->stepFactory->buildFromModel($model);
        }

        return [
            'data' => $steps,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, StepFilters $filters): int {
        return EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, StepFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Step::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(StepDomain $step) : StepDomain {
        $savedStep = Step::create($step->toStoreArray());

        $step->id = $savedStep->id;

        return $step;
    }

    public function update(StepDomain $step, int $organization_id) : StepDomain {
        Step::where('id', $step->id)
            ->where('organization_id', $organization_id)
            ->update($step->toUpdateArray());

        return $step;
    }

    public function save(StepDomain $step, int $organization_id) : StepDomain {
        if($step->id){
            return $this->update($step, $organization_id);
        }
        return $this->store($step);
    }

    public function fetchById(int $id) : StepDomain {
        return $this->stepFactory->buildFromModel(
            Step::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : StepDomain {
        return $this->stepFactory->buildFromModel(
            Step::where('bot', $bot)->first()
        );
    }

    public function delete(StepDomain $step) : bool {
        return Step::find($step->id)->delete();
    }

    /**
     * Fetch steps by type
     */
    public function fetchByType(int $organization_id, StepType $stepType, OrderBy $orderBy): array
    {
        $steps = [];

        $models = Step::where('organization_id', $organization_id)
            ->where('step_type', $stepType->value)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $steps[] = $this->stepFactory->buildFromModel($model);
        }

        return [
            'data' => $steps,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Fetch steps by flow and type
     */
    public function fetchByFlowAndType(int $flowId, StepType $stepType): array
    {
        $models = Step::where('flow_id', $flowId)
            ->where('step_type', $stepType->value)
            ->orderBy('position')
            ->get();

        return $this->stepFactory->buildFromModels($models, false, true) ?? [];
    }

    /**
     * Fetch steps with timeout configured
     */
    public function fetchWithTimeout(int $organization_id): array
    {
        $models = Step::where('organization_id', $organization_id)
            ->whereNotNull('timeout_seconds')
            ->where('timeout_seconds', '>', 0)
            ->get();

        return $this->stepFactory->buildFromModels($models, false, true) ?? [];
    }

    /**
     * Fetch steps with navigation rules
     */
    public function fetchWithNavigationRules(int $organization_id): array
    {
        $models = Step::where('organization_id', $organization_id)
            ->whereNotNull('navigation_rules')
            ->get();

        return $this->stepFactory->buildFromModels($models, false, true) ?? [];
    }

    /**
     * Count steps by type
     */
    public function countByType(int $organization_id, StepType $stepType): int
    {
        return Step::where('organization_id', $organization_id)
            ->where('step_type', $stepType->value)
            ->count();
    }

    /**
     * Fetch step by identifier and flow (for navigation)
     */
    public function fetchByStepIdentifierAndFlow(string $stepIdentifier, int $flowId): ?StepDomain
    {
        $model = Step::where('step', $stepIdentifier)
            ->where('flow_id', $flowId)
            ->first();

        return $model ? $this->stepFactory->buildFromModel($model) : null;
    }
}
