<?php

namespace App\Repositories;

use App\Domains\Inventory\DepartmentUser as DepartmentUserDomain;
use App\Factories\Inventory\DepartmentUserFactory;
use App\Models\DepartmentUser;

class DepartmentUserRepository
{
    private DepartmentUserFactory $departmentUserFactory;

    public function __construct(DepartmentUserFactory $departmentUserFactory){
        $this->departmentUserFactory = $departmentUserFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $departmentUsers = [];

        $models = DepartmentUser::paginate(30);

        foreach ($models as $model){
            $departmentUsers[] = $this->departmentUserFactory->buildFromModel($model);
        }

        return [
            'data' => $departmentUsers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $departmentUsers = [];

        //Fiz a request pelo Departamento, mas talvez não fosse necessário.
        $models = DepartmentUser::whereHas("department", function($query) use ($organization_id){
            $query->where("departments.organization_id", $organization_id);
        })->paginate(30);

        foreach ($models as $model){
            $departmentUsers[] = $this->departmentUserFactory->buildFromModel($model);
        }

        return [
            'data' => $departmentUsers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(DepartmentUserDomain $departmentUser) : DepartmentUserDomain {
        $savedDepartmentUser = DepartmentUser::create($departmentUser->toStoreArray());

        $departmentUser->id = $savedDepartmentUser->id;

        return $departmentUser;
    }

    public function update(DepartmentUserDomain $departmentUser, int $organization_id) : DepartmentUserDomain {
        DepartmentUser::where('id', $departmentUser->id)
            ->where('organization_id', $organization_id)
            ->update($departmentUser->toUpdateArray());

        return $departmentUser;
    }

    public function fetchById(int $id) : DepartmentUserDomain {
        return $this->departmentUserFactory->buildFromModel(
            DepartmentUser::findOrFail($id)
        );
    }

    public function delete(DepartmentUserDomain $departmentUser) : bool {
        return DepartmentUser::find($departmentUser->id)->delete();
    }
}
