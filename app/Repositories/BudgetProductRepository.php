<?php

namespace App\Repositories;

use App\Domains\Inventory\BudgetProduct as BudgetProductDomain;
use App\Factories\Inventory\BudgetProductFactory;
use App\Models\BudgetProduct;

class BudgetProductRepository
{
    private BudgetProductFactory $budgetProductFactory;

    public function __construct(BudgetProductFactory $budgetProductFactory){
        $this->budgetProductFactory = $budgetProductFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $budgetProducts = [];

        $models = BudgetProduct::paginate(30);

        foreach ($models as $model){
            $budgetProducts[] = $this->budgetProductFactory->buildFromModel($model);
        }

        return [
            'data' => $budgetProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $budgetProducts = [];

        $models = BudgetProduct::whereHas("product", function($query) use ($organization_id){
            $query->where("products.organization_id", $organization_id);
        })->paginate(30);

        foreach ($models as $model){
            $budgetProducts[] = $this->budgetProductFactory->buildFromModel($model);
        }

        return [
            'data' => $budgetProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(BudgetProductDomain $budgetProduct) : BudgetProductDomain {
        $savedBudgetProduct = BudgetProduct::create($budgetProduct->toStoreArray());

        $budgetProduct->id = $savedBudgetProduct->id;

        return $budgetProduct;
    }

    public function update(BudgetProductDomain $budgetProduct, int $organization_id) : BudgetProductDomain {
        BudgetProduct::where('id', $budgetProduct->id)
            ->where('organization_id', $organization_id)
            ->update($budgetProduct->toUpdateArray());

        return $budgetProduct;
    }

    public function fetchById(int $id) : BudgetProductDomain {
        return $this->budgetProductFactory->buildFromModel(
            BudgetProduct::findOrFail($id)
        );
    }

    public function delete(BudgetProductDomain $budgetProduct) : bool {
        return BudgetProduct::find($budgetProduct->id)->delete();
    }
}
