<?php

namespace App\Repositories;

use App\Domains\Organization as OrganizationDomain;
use App\Factories\OrganizationFactory;
use App\Models\Organization;

class OrganizationRepository
{
    private OrganizationFactory $organizationFactory;

    public function __construct(
        OrganizationFactory $organizationFactory
    ) {
        $this->organizationFactory = $organizationFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $organizations = [];

        $models = Organization::paginate(30);

        foreach ($models as $model){
            $organizations[] = $this->organizationFactory->buildFromModel($model);
        }

        return [
            'data' => $organizations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(OrganizationDomain $organization) : OrganizationDomain {
        $savedOrganization = Organization::create($organization->toStoreArray());

        $organization->id = $savedOrganization->id;

        return $organization;
    }

    public function update(OrganizationDomain $organization) : OrganizationDomain {
        Organization::where('id', $organization->id)
            ->update($organization->toSimpleUpdateArray());

        return $organization;
    }

    public function updateAsaasIntegration(OrganizationDomain $organization) : OrganizationDomain {
        Organization::where('id', $organization->id)
            ->update($organization->toAsaasUpdateArray());

        return $organization;
    }

    public function fetchById(int $id, bool $asaas = false) : ?OrganizationDomain {
        return $this->organizationFactory->buildFromModel(
            Organization::findOrFail($id), $asaas
        );
    }

    public function delete(OrganizationDomain $organization) : bool {
        return Organization::find($organization->id)->delete();
    }

    /**
     * Fetch organization by webhook token
     */
    public function fetchByWebhookToken(string $token): ?OrganizationDomain
    {
        $model = Organization::where('whatsapp_webhook_verify_token', $token)
                            ->where('is_active', true)
                            ->where('is_suspended', false)
                            ->first();

        return $model ? $this->organizationFactory->buildFromModel($model) : null;
    }

    /**
     * Get organization subscription summary
     */
    public function getSubscriptionSummary(int $organizationId): array
    {
        $organization = $this->fetchById($organizationId, true);
        return $organization->getAsaasSubscriptionSummary();
    }
}
