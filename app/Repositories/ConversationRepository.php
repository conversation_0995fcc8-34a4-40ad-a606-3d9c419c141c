<?php

namespace App\Repositories;

use App\Domains\ChatBot\Conversation as ConversationDomain;
use App\Domains\Filters\ConversationFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\ConversationFactory;
use App\Models\Conversation;
use EloquentBuilder;

class ConversationRepository
{
    private ConversationFactory $conversationFactory;

    public function __construct(ConversationFactory $conversationFactory){
        $this->conversationFactory = $conversationFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ConversationFilters $filters, OrderBy $orderBy) : array {
        $conversations = [];

        $models = EloquentBuilder::to(Conversation::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $conversations[] = $this->conversationFactory->buildFromModel($model);
        }

        return [
            'data' => $conversations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ConversationFilters $filters, OrderBy $orderBy) : array {
        $conversations = [];

        $models = EloquentBuilder::to(Conversation::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $conversations[] = $this->conversationFactory->buildFromModel($model);
        }

        return [
            'data' => $conversations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ConversationFilters $filters): int {
        return EloquentBuilder::to(Conversation::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ConversationFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Conversation::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ConversationDomain $conversation) : ConversationDomain {
        $savedConversation = Conversation::create($conversation->toStoreArray());

        $conversation->id = $savedConversation->id;

        return $conversation;
    }

    public function update(ConversationDomain $conversation, int $organization_id) : ConversationDomain {
        Conversation::where('id', $conversation->id)
            ->where('organization_id', $organization_id)
            ->update($conversation->toUpdateArray());

        return $conversation;
    }

    public function save(ConversationDomain $conversation, int $organization_id) : ConversationDomain {
        if ($conversation->id){
            $this->update($conversation, $organization_id);
        }
        return $this->store($conversation);
    }

    public function fetchById(int $id) : ConversationDomain {
        return $this->conversationFactory->buildFromModel(
            Conversation::findOrFail($id)
        );
    }

    public function delete(ConversationDomain $conversation) : bool {
        return Conversation::find($conversation->id)->delete();
    }
}
