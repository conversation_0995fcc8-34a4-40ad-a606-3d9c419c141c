<?php

namespace App\Repositories;

use App\Domains\Inventory\GroupProduct as GroupProductDomain;
use App\Factories\Inventory\GroupProductFactory;
use App\Models\GroupProduct;

class GroupProductRepository
{
    private GroupProductFactory $groupProductFactory;

    public function __construct(GroupProductFactory $groupProductFactory){
        $this->groupProductFactory = $groupProductFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $groupProducts = [];

        $models = GroupProduct::paginate(30);

        foreach ($models as $model){
            $groupProducts[] = $this->groupProductFactory->buildFromModel($model);
        }

        return [
            'data' => $groupProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $groupProducts = [];

        $models = GroupProduct::whereHas("product", function($query) use ($organization_id){
            $query->where("products.organization_id", $organization_id);
        })->paginate(30);

        foreach ($models as $model){
            $groupProducts[] = $this->groupProductFactory->buildFromModel($model);
        }

        return [
            'data' => $groupProducts,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(GroupProductDomain $groupProduct) : GroupProductDomain {
        $savedGroupProduct = GroupProduct::create($groupProduct->toStoreArray());

        $groupProduct->id = $savedGroupProduct->id;

        return $groupProduct;
    }

    public function update(GroupProductDomain $groupProduct, int $organization_id) : GroupProductDomain {
        GroupProduct::where('id', $groupProduct->id)
            ->where('organization_id', $organization_id)
            ->update($groupProduct->toUpdateArray());

        return $groupProduct;
    }

    public function fetchById(int $id) : GroupProductDomain {
        return $this->groupProductFactory->buildFromModel(
            GroupProduct::findOrFail($id)
        );
    }

    public function delete(GroupProductDomain $groupProduct) : bool {
        return GroupProduct::find($groupProduct->id)->delete();
    }
}
