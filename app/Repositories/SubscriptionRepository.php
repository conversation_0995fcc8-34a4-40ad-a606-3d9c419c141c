<?php

namespace App\Repositories;

use App\Domains\Subscription;
use App\Factories\SubscriptionFactory;
use App\Models\Subscription as SubscriptionModel;
use Illuminate\Support\Collection;

class SubscriptionRepository
{
    public function __construct(
        private SubscriptionFactory $factory
    ) {}

    /**
     * Find Subscription by ID
     */
    public function findById(int $id): ?Subscription
    {
        $model = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->find($id);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find Subscription by organization ID
     */
    public function findByOrganizationId(int $organizationId): ?Subscription
    {
        $model = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->where('organization_id', $organizationId)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find Subscription by external reference
     */
    public function findByExternalReference(string $externalReference): ?Subscription
    {
        $model = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->where('external_reference', $externalReference)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Get all Subscriptions
     */
    public function getAll(int $limit = 100, int $offset = 0): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->limit($limit)
            ->offset($offset)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find active subscriptions
     */
    public function findActive(int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->active()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find expired subscriptions
     */
    public function findExpired(int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->expired()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find subscriptions expiring soon
     */
    public function findExpiring(int $days = 7, int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->expiringSoon($days)
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find courtesy subscriptions
     */
    public function findCourtesies(int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->courtesy()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find trial subscriptions
     */
    public function findTrials(int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->trial()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find paid subscriptions
     */
    public function findPaid(int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->paid()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find subscriptions by status
     */
    public function findByStatus(string $status, int $limit = 100): Collection
    {
        $models = SubscriptionModel::with(['organization', 'asaasSubscription'])
            ->where('status', $status)
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Save Subscription (create or update)
     */
    public function save(Subscription $subscription): Subscription
    {
        if ($subscription->id) {
            return $this->update($subscription);
        } else {
            return $this->store($subscription);
        }
    }

    /**
     * Create new Subscription
     */
    public function store(Subscription $subscription): Subscription
    {
        $model = SubscriptionModel::create($subscription->toStoreArray());
        $model->load(['organization', 'asaasSubscription']);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Update Subscription
     */
    public function update(Subscription $subscription): Subscription
    {
        $model = SubscriptionModel::findOrFail($subscription->id);
        $model->update($subscription->toUpdateArray());
        $model->load(['organization', 'asaasSubscription']);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Delete Subscription
     */
    public function delete(int $id): bool
    {
        $model = SubscriptionModel::findOrFail($id);
        return $model->delete();
    }

    /**
     * Check if organization already has a subscription
     */
    public function organizationHasSubscription(int $organizationId): bool
    {
        return SubscriptionModel::where('organization_id', $organizationId)->exists();
    }

    /**
     * Count subscriptions by status
     */
    public function countByStatus(string $status): int
    {
        return SubscriptionModel::where('status', $status)->count();
    }

    /**
     * Count total subscriptions
     */
    public function count(): int
    {
        return SubscriptionModel::count();
    }

    /**
     * Count active subscriptions
     */
    public function countActive(): int
    {
        return SubscriptionModel::active()->count();
    }

    /**
     * Count expired subscriptions
     */
    public function countExpired(): int
    {
        return SubscriptionModel::expired()->count();
    }

    /**
     * Count courtesy subscriptions
     */
    public function countCourtesies(): int
    {
        return SubscriptionModel::courtesy()->count();
    }

    /**
     * Count trial subscriptions
     */
    public function countTrials(): int
    {
        return SubscriptionModel::trial()->count();
    }

    /**
     * Count paid subscriptions
     */
    public function countPaid(): int
    {
        return SubscriptionModel::paid()->count();
    }

    /**
     * Get subscriptions with filters
     */
    public function findWithFilters(array $filters = [], int $limit = 100): Collection
    {
        $query = SubscriptionModel::with(['organization', 'asaasSubscription']);

        if (isset($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['billing_type'])) {
            $query->where('billing_type', $filters['billing_type']);
        }

        if (isset($filters['cycle'])) {
            $query->where('cycle', $filters['cycle']);
        }

        if (isset($filters['is_courtesy'])) {
            $query->where('is_courtesy', $filters['is_courtesy']);
        }

        if (isset($filters['is_trial'])) {
            $query->where('is_trial', $filters['is_trial']);
        }

        if (isset($filters['expires_from'])) {
            $query->where('expires_at', '>=', $filters['expires_from']);
        }

        if (isset($filters['expires_to'])) {
            $query->where('expires_at', '<=', $filters['expires_to']);
        }

        if (isset($filters['value_min'])) {
            $query->where('value', '>=', $filters['value_min']);
        }

        if (isset($filters['value_max'])) {
            $query->where('value', '<=', $filters['value_max']);
        }

        if (isset($filters['external_reference'])) {
            $query->where('external_reference', 'like', "%{$filters['external_reference']}%");
        }

        $models = $query->limit($limit)->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get subscription statistics
     */
    public function getStatistics(): array
    {
        return [
            'total' => $this->count(),
            'active' => $this->countActive(),
            'expired' => $this->countExpired(),
            'courtesy' => $this->countCourtesies(),
            'trial' => $this->countTrials(),
            'paid' => $this->countPaid(),
            'expiring_soon' => $this->findExpiring(7)->count(),
        ];
    }

    /**
     * Bulk update subscription status
     */
    public function bulkUpdateStatus(array $ids, string $status): bool
    {
        SubscriptionModel::whereIn('id', $ids)->update(['status' => $status]);
        return true;
    }

    /**
     * Bulk extend expiration dates
     */
    public function bulkExtendExpiration(array $ids, int $days): bool
    {
        $models = SubscriptionModel::whereIn('id', $ids)->get();

        foreach ($models as $model) {
            if ($model->is_courtesy && $model->courtesy_expires_at) {
                $model->courtesy_expires_at = $model->courtesy_expires_at->addDays($days);
            } elseif ($model->is_trial && $model->trial_expires_at) {
                $model->trial_expires_at = $model->trial_expires_at->addDays($days);
            } elseif ($model->expires_at) {
                $model->expires_at = $model->expires_at->addDays($days);
            }
            $model->save();
        }

        return true;
    }
}
