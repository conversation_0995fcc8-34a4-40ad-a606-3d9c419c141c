<?php

namespace App\Repositories;

use App\Domains\ChatBot\CampaignTagAssignment as CampaignTagAssignmentDomain;
use App\Factories\ChatBot\CampaignTagAssignmentFactory;
use App\Models\CampaignTagAssignment;

class CampaignTagAssignmentRepository
{
    private CampaignTagAssignmentFactory $factory;
    private TagRepository $tagRepository;

    public function __construct(
        CampaignTagAssignmentFactory $factory,
        TagRepository $tagRepository
    ) {
        $this->factory = $factory;
        $this->tagRepository = $tagRepository;
    }

    public function store(CampaignTagAssignmentDomain $assignment): CampaignTagAssignmentDomain
    {
        $model = CampaignTagAssignment::create($assignment->toStoreArray());
        $assignment->id = $model->id;

        // Increment tag usage count
        $this->tagRepository->incrementUsage($assignment->tag_id);

        return $assignment;
    }

    public function delete(int $campaign_id, int $tag_id): bool
    {
        $deleted = CampaignTagAssignment::where('campaign_id', $campaign_id)
                                       ->where('tag_id', $tag_id)
                                       ->delete() > 0;

        if ($deleted) {
            // Decrement tag usage count
            $this->tagRepository->decrementUsage($tag_id);
        }

        return $deleted;
    }

    public function deleteByCampaign(int $campaign_id): bool
    {
        $assignments = CampaignTagAssignment::where('campaign_id', $campaign_id)->get();
        
        foreach ($assignments as $assignment) {
            $this->tagRepository->decrementUsage($assignment->tag_id);
        }

        return CampaignTagAssignment::where('campaign_id', $campaign_id)->delete() > 0;
    }

    public function deleteByTag(int $tag_id): bool
    {
        return CampaignTagAssignment::where('tag_id', $tag_id)->delete() > 0;
    }

    public function exists(int $campaign_id, int $tag_id): bool
    {
        return CampaignTagAssignment::where('campaign_id', $campaign_id)
                                   ->where('tag_id', $tag_id)
                                   ->exists();
    }

    public function fetchByCampaign(int $campaign_id): array
    {
        $models = CampaignTagAssignment::where('campaign_id', $campaign_id)
                                      ->with(['tag'])
                                      ->get();

        return $this->factory->buildCollection($models, false, true);
    }

    public function fetchByTag(int $tag_id): array
    {
        $models = CampaignTagAssignment::where('tag_id', $tag_id)
                                      ->with(['campaign'])
                                      ->get();

        return $this->factory->buildCollection($models, true, false);
    }

    public function assignTagsToCampaign(int $campaign_id, array $tag_names, int $organization_id): array
    {
        $assignments = [];
        
        foreach ($tag_names as $tag_name) {
            // Find or create tag
            $tag = $this->tagRepository->findOrCreate($tag_name, $organization_id);
            
            if (!$this->exists($campaign_id, $tag->id)) {
                $assignment = CampaignTagAssignmentDomain::create($campaign_id, $tag->id);
                $assignments[] = $this->store($assignment);
            }
        }

        return $assignments;
    }

    public function syncCampaignTags(int $campaign_id, array $tag_names, int $organization_id): void
    {
        // Remove existing assignments
        $this->deleteByCampaign($campaign_id);

        // Add new assignments
        if (!empty($tag_names)) {
            $this->assignTagsToCampaign($campaign_id, $tag_names, $organization_id);
        }
    }
}
