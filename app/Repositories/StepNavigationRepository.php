<?php

namespace App\Repositories;

use App\Domains\ChatBot\StepNavigation as StepNavigationDomain;
use App\Domains\Filters\StepNavigationFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\StepNavigationFactory;
use App\Models\StepNavigation;
use EloquentBuilder;
use Illuminate\Support\Collection;

class StepNavigationRepository
{
    private StepNavigationFactory $stepNavigationFactory;

    public function __construct(StepNavigationFactory $stepNavigationFactory)
    {
        $this->stepNavigationFactory = $stepNavigationFactory;
    }

    /**
     * @param StepNavigationFilters $filters
     * @param OrderBy $orderBy
     * @return array
     * @throws \Throwable
     */
    public function fetchAll(StepNavigationFilters $filters, OrderBy $orderBy): array
    {
        $stepNavigations = [];

        $models = EloquentBuilder::to(StepNavigation::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $stepNavigations[] = $this->stepNavigationFactory->buildFromModel($model);
        }

        return [
            'data' => $stepNavigations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @param int $organization_id
     * @param StepNavigationFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function fetchFromOrganization(int $organization_id, StepNavigationFilters $filters, OrderBy $orderBy): array
    {
        $stepNavigations = [];

        $models = EloquentBuilder::to(StepNavigation::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $stepNavigations[] = $this->stepNavigationFactory->buildFromModel($model);
        }

        return [
            'data' => $stepNavigations,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Get navigation rules for a specific step, ordered by priority
     */
    public function fetchByStepId(int $stepId, bool $activeOnly = true): array
    {
        $query = StepNavigation::where('step_id', $stepId);

        if ($activeOnly) {
            $query->where('is_active', true);
        }

        $models = $query->orderBy('priority', 'asc')->get();

        $stepNavigations = [];
        foreach ($models as $model) {
            $stepNavigations[] = $this->stepNavigationFactory->buildFromModel($model);
        }

        return $stepNavigations;
    }

    /**
     * Get navigation rules for multiple steps
     */
    public function fetchByStepIds(array $stepIds, bool $activeOnly = true): array
    {
        $query = StepNavigation::whereIn('step_id', $stepIds);

        if ($activeOnly) {
            $query->where('is_active', true);
        }

        $models = $query->orderBy('step_id', 'asc')
                       ->orderBy('priority', 'asc')
                       ->get();

        $stepNavigations = [];
        foreach ($models as $model) {
            $stepNavigations[] = $this->stepNavigationFactory->buildFromModel($model);
        }

        return $stepNavigations;
    }

    /**
     * Get navigation rules grouped by step ID
     */
    public function fetchByStepIdsGrouped(array $stepIds, bool $activeOnly = true): array
    {
        $navigationRules = $this->fetchByStepIds($stepIds, $activeOnly);
        
        $grouped = [];
        foreach ($navigationRules as $rule) {
            $grouped[$rule->step_id][] = $rule;
        }

        return $grouped;
    }

    public function count(int $organization_id, StepNavigationFilters $filters): int
    {
        return EloquentBuilder::to(StepNavigation::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function store(StepNavigationDomain $stepNavigation): StepNavigationDomain
    {
        $savedStepNavigation = StepNavigation::create($stepNavigation->toStoreArray());

        $stepNavigation->id = $savedStepNavigation->id;

        return $stepNavigation;
    }

    public function update(StepNavigationDomain $stepNavigation, int $organization_id): StepNavigationDomain
    {
        StepNavigation::where('id', $stepNavigation->id)
            ->where('organization_id', $organization_id)
            ->update($stepNavigation->toUpdateArray());

        return $stepNavigation;
    }

    public function save(StepNavigationDomain $stepNavigation, int $organization_id): StepNavigationDomain
    {
        if ($stepNavigation->id) {
            return $this->update($stepNavigation, $organization_id);
        }
        return $this->store($stepNavigation);
    }

    public function delete(int $id, int $organization_id): bool
    {
        return StepNavigation::where('id', $id)
            ->where('organization_id', $organization_id)
            ->delete() > 0;
    }

    public function findById(int $id, int $organization_id): ?StepNavigationDomain
    {
        $model = StepNavigation::where('id', $id)
            ->where('organization_id', $organization_id)
            ->first();

        return $this->stepNavigationFactory->buildFromModel($model);
    }

    /**
     * Store multiple navigation rules for a step
     */
    public function storeMultiple(array $stepNavigations): array
    {
        $saved = [];
        
        foreach ($stepNavigations as $stepNavigation) {
            $saved[] = $this->store($stepNavigation);
        }

        return $saved;
    }

    /**
     * Delete all navigation rules for a step
     */
    public function deleteByStepId(int $stepId, int $organizationId): bool
    {
        return StepNavigation::where('step_id', $stepId)
            ->where('organization_id', $organizationId)
            ->delete() > 0;
    }

    /**
     * Replace all navigation rules for a step
     */
    public function replaceForStep(int $stepId, int $organizationId, array $newNavigationRules): array
    {
        // Delete existing rules
        $this->deleteByStepId($stepId, $organizationId);

        // Store new rules
        return $this->storeMultiple($newNavigationRules);
    }

    /**
     * Find the best matching navigation rule for user input
     */
    public function findMatchingRule(int $stepId, string $userInput, ?array $context = null): ?StepNavigationDomain
    {
        $rules = $this->fetchByStepId($stepId, true);

        foreach ($rules as $rule) {
            if ($rule->matchesCondition($userInput, $context)) {
                return $rule;
            }
        }

        return null;
    }

    /**
     * Activate/deactivate a navigation rule
     */
    public function toggleActive(int $id, int $organizationId, bool $isActive): bool
    {
        return StepNavigation::where('id', $id)
            ->where('organization_id', $organizationId)
            ->update(['is_active' => $isActive]) > 0;
    }

    /**
     * Update priority of a navigation rule
     */
    public function updatePriority(int $id, int $organizationId, int $priority): bool
    {
        return StepNavigation::where('id', $id)
            ->where('organization_id', $organizationId)
            ->update(['priority' => $priority]) > 0;
    }
}
