<?php

namespace App\Repositories;

use App\Domains\Auth\PasswordResetToken as PasswordResetTokenDomain;
use App\Factories\Auth\PasswordResetTokenFactory;
use App\Models\PasswordResetToken;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class PasswordResetTokenRepository
{
    private PasswordResetTokenFactory $factory;

    public function __construct(PasswordResetTokenFactory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * Store a new password reset token
     */
    public function store(PasswordResetTokenDomain $token): PasswordResetTokenDomain
    {
        $savedToken = PasswordResetToken::create($token->toStoreArray());
        $token->id = $savedToken->id;
        $token->created_at = $savedToken->created_at;
        $token->updated_at = $savedToken->updated_at;

        return $token;
    }

    /**
     * Update a password reset token
     */
    public function update(PasswordResetTokenDomain $token): PasswordResetTokenDomain
    {
        PasswordResetToken::where('id', $token->id)
            ->update($token->toUpdateArray());

        return $token;
    }

    /**
     * Find token by email and plain token
     */
    public function findByEmailAndToken(string $email, string $plainToken): ?PasswordResetTokenDomain
    {
        $tokens = PasswordResetToken::where('email', $email)
            ->where('expires_at', '>', Carbon::now())
            ->whereNull('used_at')
            ->get();

        foreach ($tokens as $tokenModel) {
            $tokenDomain = $this->factory->buildFromModel($tokenModel);
            if ($tokenDomain && $tokenDomain->verifyToken($plainToken)) {
                return $tokenDomain;
            }
        }

        return null;
    }

    /**
     * Find token by ID
     */
    public function findById(int $id): ?PasswordResetTokenDomain
    {
        $model = PasswordResetToken::find($id);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find valid tokens by email
     */
    public function findValidTokensByEmail(string $email): Collection
    {
        $models = PasswordResetToken::where('email', $email)
            ->where('expires_at', '>', Carbon::now())
            ->whereNull('used_at')
            ->get();

        return $models->map(function ($model) {
            return $this->factory->buildFromModel($model);
        });
    }

    /**
     * Delete expired tokens
     */
    public function deleteExpiredTokens(): int
    {
        return PasswordResetToken::where('expires_at', '<', Carbon::now())->delete();
    }

    /**
     * Delete used tokens older than specified days
     */
    public function deleteUsedTokens(int $olderThanDays = 7): int
    {
        return PasswordResetToken::whereNotNull('used_at')
            ->where('used_at', '<', Carbon::now()->subDays($olderThanDays))
            ->delete();
    }

    /**
     * Delete all tokens for a specific email
     */
    public function deleteTokensByEmail(string $email): int
    {
        return PasswordResetToken::where('email', $email)->delete();
    }

    /**
     * Count recent attempts by IP
     */
    public function countRecentAttemptsByIp(string $ipAddress, int $minutes = 60): int
    {
        return PasswordResetToken::where('ip_address', $ipAddress)
            ->where('created_at', '>', Carbon::now()->subMinutes($minutes))
            ->count();
    }

    /**
     * Count recent attempts by email
     */
    public function countRecentAttemptsByEmail(string $email, int $minutes = 60): int
    {
        return PasswordResetToken::where('email', $email)
            ->where('created_at', '>', Carbon::now()->subMinutes($minutes))
            ->count();
    }
}
