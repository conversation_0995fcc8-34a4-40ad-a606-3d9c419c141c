<?php

namespace App\Repositories;

use App\Domains\ChatBot\MessageDeliveryAttempt as MessageDeliveryAttemptDomain;
use App\Factories\ChatBot\MessageDeliveryAttemptFactory;
use App\Models\MessageDeliveryAttempt;
use App\Enums\MessageStatus;

class MessageDeliveryAttemptRepository
{
    private MessageDeliveryAttemptFactory $factory;

    public function __construct(MessageDeliveryAttemptFactory $factory)
    {
        $this->factory = $factory;
    }

    public function store(MessageDeliveryAttemptDomain $attempt): MessageDeliveryAttemptDomain
    {
        $model = MessageDeliveryAttempt::create($attempt->toStoreArray());
        $attempt->id = $model->id;
        $attempt->created_at = $model->created_at;
        $attempt->updated_at = $model->updated_at;

        return $attempt;
    }

    public function fetchByMessage(int $message_id, int $limit = 10): array
    {
        $models = MessageDeliveryAttempt::where('message_id', $message_id)
                                       ->orderBy('attempt_number', 'desc')
                                       ->limit($limit)
                                       ->get();

        return $this->factory->buildCollection($models);
    }

    public function fetchById(int $id): MessageDeliveryAttemptDomain
    {
        $model = MessageDeliveryAttempt::with(['message'])->findOrFail($id);
        return $this->factory->buildFromModel($model, true);
    }

    public function getLatestAttempt(int $message_id): ?MessageDeliveryAttemptDomain
    {
        $model = MessageDeliveryAttempt::where('message_id', $message_id)
                                      ->orderBy('attempt_number', 'desc')
                                      ->first();

        return $model ? $this->factory->buildFromModel($model) : null;
    }

    public function getSuccessfulAttempts(int $message_id): array
    {
        $models = MessageDeliveryAttempt::where('message_id', $message_id)
                                       ->where('status', MessageStatus::is_sent->value)
                                       ->orderBy('attempted_at', 'desc')
                                       ->get();

        return $this->factory->buildCollection($models);
    }

    public function getFailedAttempts(int $message_id): array
    {
        $models = MessageDeliveryAttempt::where('message_id', $message_id)
                                       ->where('status', MessageStatus::is_failed->value)
                                       ->orderBy('attempted_at', 'desc')
                                       ->get();

        return $this->factory->buildCollection($models);
    }

    public function getAttemptsByStatus(MessageStatus $status, int $limit = 100): array
    {
        $models = MessageDeliveryAttempt::where('status', $status->value)
                                       ->with(['message'])
                                       ->orderBy('attempted_at', 'desc')
                                       ->limit($limit)
                                       ->get();

        return $this->factory->buildCollection($models, true);
    }

    public function getRecentFailedAttempts(int $hours = 24, int $limit = 100): array
    {
        $models = MessageDeliveryAttempt::where('status', MessageStatus::is_failed->value)
                                       ->where('attempted_at', '>=', now()->subHours($hours))
                                       ->with(['message'])
                                       ->orderBy('attempted_at', 'desc')
                                       ->limit($limit)
                                       ->get();

        return $this->factory->buildCollection($models, true);
    }

    public function getAttemptsStatistics(int $message_id): array
    {
        $attempts = MessageDeliveryAttempt::where('message_id', $message_id)->get();
        
        return [
            'total_attempts' => $attempts->count(),
            'successful_attempts' => $attempts->where('status', MessageStatus::is_sent->value)->count(),
            'failed_attempts' => $attempts->where('status', MessageStatus::is_failed->value)->count(),
            'first_attempt_at' => $attempts->min('attempted_at'),
            'last_attempt_at' => $attempts->max('attempted_at'),
            'average_time_between_attempts' => $this->calculateAverageTimeBetweenAttempts($attempts),
        ];
    }

    private function calculateAverageTimeBetweenAttempts($attempts): ?int
    {
        if ($attempts->count() < 2) {
            return null;
        }

        $sortedAttempts = $attempts->sortBy('attempted_at');
        $totalMinutes = 0;
        $intervals = 0;

        for ($i = 1; $i < $sortedAttempts->count(); $i++) {
            $previous = $sortedAttempts->values()[$i - 1];
            $current = $sortedAttempts->values()[$i];
            
            $totalMinutes += \Carbon\Carbon::parse($previous->attempted_at)
                                          ->diffInMinutes(\Carbon\Carbon::parse($current->attempted_at));
            $intervals++;
        }

        return $intervals > 0 ? round($totalMinutes / $intervals) : null;
    }

    public function deleteOldAttempts(int $days = 90): int
    {
        return MessageDeliveryAttempt::where('attempted_at', '<', now()->subDays($days))->delete();
    }
}
