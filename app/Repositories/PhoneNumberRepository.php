<?php

namespace App\Repositories;

use App\Domains\Filters\PhoneNumberFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\ChatBot\PhoneNumber as PhoneNumberDomain;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Models\PhoneNumber;
use EloquentBuilder;

class PhoneNumberRepository
{
    private PhoneNumberFactory $phoneNumberFactory;

    public function __construct(PhoneNumberFactory $phoneNumberFactory){
        $this->phoneNumberFactory = $phoneNumberFactory;
    }

    /**
     * @return array
     */
        public function fetchAll(PhoneNumberFilters $filters, OrderBy $orderBy) : array {
        $phoneNumbers = [];

        $models = EloquentBuilder::to(PhoneNumber::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $phoneNumbers[] = $this->phoneNumberFactory->buildFromModel($model);
        }

        return [
            'data' => $phoneNumbers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, PhoneNumberFilters $filters, OrderBy $orderBy) : array {
        $phoneNumbers = [];

        $models = EloquentBuilder::to(PhoneNumber::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $phoneNumbers[] = $this->phoneNumberFactory->buildFromModel($model);
        }

        return [
            'data' => $phoneNumbers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, PhoneNumberFilters $filters): int {
        return EloquentBuilder::to(PhoneNumber::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, PhoneNumberFilters $filters, string $column): float|int {
        return EloquentBuilder::to(PhoneNumber::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(PhoneNumberDomain $phoneNumber) : PhoneNumberDomain {
        $savedPhoneNumber = PhoneNumber::create($phoneNumber->toStoreArray());

        $phoneNumber->id = $savedPhoneNumber->id;

        return $phoneNumber;
    }

    public function update(PhoneNumberDomain $phoneNumber, int $organization_id) : PhoneNumberDomain {
        PhoneNumber::where('id', $phoneNumber->id)
            ->where('organization_id', $organization_id)
            ->update($phoneNumber->toUpdateArray());

        return $phoneNumber;
    }

    public function fetchById(int $id) : PhoneNumberDomain {
        return $this->phoneNumberFactory->buildFromModel(
            PhoneNumber::findOrFail($id)
        );
    }

    public function delete(PhoneNumberDomain $phoneNumber) : bool {
        return PhoneNumber::find($phoneNumber->id)->delete();
    }

    /**
     * Find phone number by WhatsApp phone number ID
     */
    public function findByWhatsAppPhoneNumberId(string $whatsappPhoneNumberId): ?PhoneNumberDomain
    {
        $model = PhoneNumber::where('whatsapp_phone_number_id', $whatsappPhoneNumberId)
            ->with(['organization', 'flow'])
            ->first();

        return $model ? $this->phoneNumberFactory->buildFromModel($model) : null;
    }

    /**
     * Find phone number by phone number string
     */
    public function findByPhoneNumber(string $phoneNumber): ?PhoneNumberDomain
    {
        $model = PhoneNumber::where('phone_number', $phoneNumber)
            ->with(['organization', 'flow'])
            ->first();

        return $model ? $this->phoneNumberFactory->buildFromModel($model) : null;
    }

    /**
     * Fetch phone number by WhatsApp phone number ID with active organization
     */
    public function fetchByWhatsAppPhoneNumberId(string $phoneNumberId): ?PhoneNumberDomain
    {
        $model = PhoneNumber::where('whatsapp_phone_number_id', $phoneNumberId)
                           ->where('is_active', true)
                           ->with(['organization' => function($query) {
                               $query->where('is_active', true)
                                     ->where('is_suspended', false);
                           }])
                           ->first();

        return $model ? $this->phoneNumberFactory->buildFromModel($model, true) : null;
    }

    /**
     * Save phone number (store or update)
     */
    public function save(PhoneNumberDomain $phoneNumber): PhoneNumberDomain
    {
        if ($phoneNumber->id) {
            return $this->update($phoneNumber, $phoneNumber->organization_id);
        }
        return $this->store($phoneNumber);
    }
}
