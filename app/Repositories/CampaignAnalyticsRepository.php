<?php

namespace App\Repositories;

use App\Domains\ChatBot\CampaignAnalytics as CampaignAnalyticsDomain;
use App\Factories\ChatBot\CampaignAnalyticsFactory;
use App\Models\CampaignAnalytics;
use App\Models\MessageEngagementEvent;
use App\Enums\EngagementEventType;

class CampaignAnalyticsRepository
{
    private CampaignAnalyticsFactory $factory;

    public function __construct(CampaignAnalyticsFactory $factory)
    {
        $this->factory = $factory;
    }

    public function store(CampaignAnalyticsDomain $analytics): CampaignAnalyticsDomain
    {
        $model = CampaignAnalytics::updateOrCreate(
            ['campaign_id' => $analytics->campaign_id],
            $analytics->toStoreArray()
        );
        
        $analytics->id = $model->id;
        $analytics->created_at = $model->created_at;
        $analytics->updated_at = $model->updated_at;

        return $analytics;
    }

    public function fetchByCampaign(int $campaign_id): ?CampaignAnalyticsDomain
    {
        $model = CampaignAnalytics::where('campaign_id', $campaign_id)->first();
        return $model ? $this->factory->buildFromModel($model) : null;
    }

    public function fetchById(int $id): CampaignAnalyticsDomain
    {
        $model = CampaignAnalytics::with(['campaign'])->findOrFail($id);
        return $this->factory->buildFromModel($model, true);
    }

    public function calculateAndStore(int $campaign_id): CampaignAnalyticsDomain
    {
        // Get message statistics
        $messageStats = $this->getMessageStatistics($campaign_id);
        
        // Get engagement statistics
        $engagementStats = $this->getEngagementStatistics($campaign_id);
        
        // Calculate delivery time
        $avgDeliveryTime = $this->calculateAverageDeliveryTime($campaign_id);

        // Create analytics domain
        $analytics = new CampaignAnalyticsDomain(
            campaign_id: $campaign_id,
            total_messages: $messageStats['total'],
            sent_count: $messageStats['sent'],
            delivered_count: $engagementStats['delivered'],
            failed_count: $messageStats['failed'],
            read_count: $engagementStats['read'],
            response_count: $engagementStats['replied'],
            avg_delivery_time: $avgDeliveryTime,
            calculated_at: now()
        );

        // Calculate rates
        $analytics->calculateRates();

        // Store and return
        return $this->store($analytics);
    }

    private function getMessageStatistics(int $campaign_id): array
    {
        $stats = \App\Models\Message::where('campaign_id', $campaign_id)
                                   ->selectRaw('
                                       COUNT(*) as total,
                                       SUM(CASE WHEN is_sent = 1 THEN 1 ELSE 0 END) as sent,
                                       SUM(CASE WHEN is_fail = 1 THEN 1 ELSE 0 END) as failed
                                   ')
                                   ->first();

        return [
            'total' => $stats->total ?? 0,
            'sent' => $stats->sent ?? 0,
            'failed' => $stats->failed ?? 0,
        ];
    }

    private function getEngagementStatistics(int $campaign_id): array
    {
        $delivered = MessageEngagementEvent::where('campaign_id', $campaign_id)
                                          ->where('event_type', EngagementEventType::DELIVERED)
                                          ->distinct('message_id')
                                          ->count();

        $read = MessageEngagementEvent::where('campaign_id', $campaign_id)
                                     ->where('event_type', EngagementEventType::READ)
                                     ->distinct('message_id')
                                     ->count();

        $replied = MessageEngagementEvent::where('campaign_id', $campaign_id)
                                        ->where('event_type', EngagementEventType::REPLIED)
                                        ->distinct('message_id')
                                        ->count();

        return [
            'delivered' => $delivered,
            'read' => $read,
            'replied' => $replied,
        ];
    }

    private function calculateAverageDeliveryTime(int $campaign_id): ?float
    {
        // This would calculate the average time between message creation and delivery
        // For now, return a mock value
        return rand(30, 300) / 10; // Random value between 3-30 minutes
    }

    public function getTopPerformingCampaigns(int $organization_id, int $limit = 10): array
    {
        $models = CampaignAnalytics::whereHas('campaign', function($query) use ($organization_id) {
                                      $query->where('organization_id', $organization_id);
                                  })
                                  ->where('total_messages', '>', 0)
                                  ->orderBy('delivery_rate', 'desc')
                                  ->orderBy('read_rate', 'desc')
                                  ->limit($limit)
                                  ->with(['campaign'])
                                  ->get();

        return $this->factory->buildCollection($models, true);
    }

    public function getOrganizationSummary(int $organization_id): array
    {
        $stats = CampaignAnalytics::whereHas('campaign', function($query) use ($organization_id) {
                                     $query->where('organization_id', $organization_id);
                                 })
                                 ->selectRaw('
                                     COUNT(*) as total_campaigns,
                                     SUM(total_messages) as total_messages,
                                     SUM(sent_count) as total_sent,
                                     SUM(delivered_count) as total_delivered,
                                     SUM(read_count) as total_read,
                                     SUM(response_count) as total_responses,
                                     AVG(delivery_rate) as avg_delivery_rate,
                                     AVG(read_rate) as avg_read_rate,
                                     AVG(response_rate) as avg_response_rate
                                 ')
                                 ->first();

        return [
            'total_campaigns' => $stats->total_campaigns ?? 0,
            'total_messages' => $stats->total_messages ?? 0,
            'total_sent' => $stats->total_sent ?? 0,
            'total_delivered' => $stats->total_delivered ?? 0,
            'total_read' => $stats->total_read ?? 0,
            'total_responses' => $stats->total_responses ?? 0,
            'avg_delivery_rate' => round($stats->avg_delivery_rate ?? 0, 2),
            'avg_read_rate' => round($stats->avg_read_rate ?? 0, 2),
            'avg_response_rate' => round($stats->avg_response_rate ?? 0, 2),
            'overall_engagement_score' => $this->calculateOverallEngagementScore($stats),
        ];
    }

    private function calculateOverallEngagementScore($stats): float
    {
        if (!$stats->avg_read_rate && !$stats->avg_response_rate) {
            return 0.0;
        }

        $score = ($stats->avg_read_rate * 0.4) + ($stats->avg_response_rate * 0.6);
        return round($score, 2);
    }

    public function deleteOldAnalytics(int $days = 90): int
    {
        return CampaignAnalytics::where('calculated_at', '<', now()->subDays($days))->delete();
    }
}
