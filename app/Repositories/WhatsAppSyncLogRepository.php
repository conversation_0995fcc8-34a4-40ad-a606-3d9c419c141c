<?php

namespace App\Repositories;

use App\Domains\ChatBot\WhatsAppSyncLog as WhatsAppSyncLogDomain;
use App\Factories\ChatBot\WhatsAppSyncLogFactory;
use App\Models\WhatsAppSyncLog;
use App\Enums\SyncType;
use App\Enums\SyncStatus;

class WhatsAppSyncLogRepository
{
    private WhatsAppSyncLogFactory $factory;

    public function __construct(WhatsAppSyncLogFactory $factory)
    {
        $this->factory = $factory;
    }

    public function store(WhatsAppSyncLogDomain $log): WhatsAppSyncLogDomain
    {
        $model = WhatsAppSyncLog::create($log->toStoreArray());
        $log->id = $model->id;
        $log->created_at = $model->created_at;
        $log->updated_at = $model->updated_at;

        return $log;
    }

    public function fetchById(int $id): WhatsAppSyncLogDomain
    {
        $model = WhatsAppSyncLog::findOrFail($id);
        return $this->factory->buildFromModel($model);
    }

    public function fetchByEntity(SyncType $sync_type, int $entity_id, int $limit = 10): array
    {
        $models = WhatsAppSyncLog::where('sync_type', $sync_type)
                                 ->where('entity_id', $entity_id)
                                 ->orderBy('synced_at', 'desc')
                                 ->limit($limit)
                                 ->get();

        return $this->factory->buildCollection($models);
    }

    public function getRecentLogs(int $hours = 24, int $limit = 100): array
    {
        $models = WhatsAppSyncLog::recent($hours)
                                 ->orderBy('synced_at', 'desc')
                                 ->limit($limit)
                                 ->get();

        return $this->factory->buildCollection($models);
    }

    public function getFailedLogs(int $hours = 24, int $limit = 50): array
    {
        $models = WhatsAppSyncLog::failed()
                                 ->recent($hours)
                                 ->orderBy('synced_at', 'desc')
                                 ->limit($limit)
                                 ->get();

        return $this->factory->buildCollection($models);
    }

    public function getSuccessfulLogs(int $hours = 24, int $limit = 50): array
    {
        $models = WhatsAppSyncLog::successful()
                                 ->recent($hours)
                                 ->orderBy('synced_at', 'desc')
                                 ->limit($limit)
                                 ->get();

        return $this->factory->buildCollection($models);
    }

    public function getSyncStatistics(int $hours = 24): array
    {
        $stats = WhatsAppSyncLog::recent($hours)
                                ->selectRaw('
                                    COUNT(*) as total_syncs,
                                    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as successful_syncs,
                                    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed_syncs,
                                    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as partial_syncs,
                                    SUM(messages_synced) as total_messages_synced,
                                    SUM(messages_updated) as total_messages_updated,
                                    AVG(messages_synced) as avg_messages_per_sync
                                ', [
                                    SyncStatus::SUCCESS->value,
                                    SyncStatus::FAILED->value,
                                    SyncStatus::PARTIAL->value,
                                ])
                                ->first();

        return [
            'total_syncs' => $stats->total_syncs ?? 0,
            'successful_syncs' => $stats->successful_syncs ?? 0,
            'failed_syncs' => $stats->failed_syncs ?? 0,
            'partial_syncs' => $stats->partial_syncs ?? 0,
            'total_messages_synced' => $stats->total_messages_synced ?? 0,
            'total_messages_updated' => $stats->total_messages_updated ?? 0,
            'avg_messages_per_sync' => round($stats->avg_messages_per_sync ?? 0, 2),
            'success_rate' => $stats->total_syncs > 0 ? round(($stats->successful_syncs / $stats->total_syncs) * 100, 2) : 0,
            'update_efficiency' => $stats->total_messages_synced > 0 ? round(($stats->total_messages_updated / $stats->total_messages_synced) * 100, 2) : 0,
        ];
    }

    public function getLastSyncForEntity(SyncType $sync_type, int $entity_id): ?WhatsAppSyncLogDomain
    {
        $model = WhatsAppSyncLog::where('sync_type', $sync_type)
                                ->where('entity_id', $entity_id)
                                ->orderBy('synced_at', 'desc')
                                ->first();

        return $model ? $this->factory->buildFromModel($model) : null;
    }

    public function getLastSuccessfulSyncForEntity(SyncType $sync_type, int $entity_id): ?WhatsAppSyncLogDomain
    {
        $model = WhatsAppSyncLog::where('sync_type', $sync_type)
                                ->where('entity_id', $entity_id)
                                ->where('status', SyncStatus::SUCCESS)
                                ->orderBy('synced_at', 'desc')
                                ->first();

        return $model ? $this->factory->buildFromModel($model) : null;
    }

    public function createSyncLog(
        SyncType $sync_type,
        int $entity_id,
        SyncStatus $status,
        ?array $response_data = null,
        ?string $error_message = null,
        int $messages_synced = 0,
        int $messages_updated = 0
    ): WhatsAppSyncLogDomain {
        $log = WhatsAppSyncLogDomain::create(
            $sync_type,
            $entity_id,
            $status,
            $response_data,
            $error_message,
            $messages_synced,
            $messages_updated
        );

        return $this->store($log);
    }

    public function deleteOldLogs(int $days = 90): int
    {
        return WhatsAppSyncLog::where('synced_at', '<', now()->subDays($days))->delete();
    }

    public function getSyncTrends(int $days = 7): array
    {
        $trends = WhatsAppSyncLog::where('synced_at', '>=', now()->subDays($days))
                                 ->selectRaw('
                                     DATE(synced_at) as sync_date,
                                     COUNT(*) as total_syncs,
                                     SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as successful_syncs,
                                     SUM(messages_synced) as messages_synced,
                                     SUM(messages_updated) as messages_updated
                                 ', [SyncStatus::SUCCESS->value])
                                 ->groupBy('sync_date')
                                 ->orderBy('sync_date')
                                 ->get();

        return $trends->map(function($trend) {
            return [
                'date' => $trend->sync_date,
                'total_syncs' => $trend->total_syncs,
                'successful_syncs' => $trend->successful_syncs,
                'success_rate' => $trend->total_syncs > 0 ? round(($trend->successful_syncs / $trend->total_syncs) * 100, 2) : 0,
                'messages_synced' => $trend->messages_synced,
                'messages_updated' => $trend->messages_updated,
                'update_efficiency' => $trend->messages_synced > 0 ? round(($trend->messages_updated / $trend->messages_synced) * 100, 2) : 0,
            ];
        })->toArray();
    }
}
