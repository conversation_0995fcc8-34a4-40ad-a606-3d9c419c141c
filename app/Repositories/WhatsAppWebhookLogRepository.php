<?php

namespace App\Repositories;

use App\Domains\WhatsAppWebhookLog as WhatsAppWebhookLogDomain;
use App\Domains\Filters\WhatsAppWebhookLogFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\WhatsAppWebhookLogFactory;
use App\Models\WhatsAppWebhookLog;
use EloquentBuilder;

class WhatsAppWebhookLogRepository
{
    // TODO use factory instead
    private WhatsAppWebhookLogFactory $factory;

    public function __construct(WhatsAppWebhookLogFactory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * Store a new webhook log
     */
    public function store(WhatsAppWebhookLogDomain $log): WhatsAppWebhookLogDomain
    {
        $model = WhatsAppWebhookLog::create($log->toStoreArray());
        $log->id = $model->id;
        $log->created_at = $model->created_at;
        $log->updated_at = $model->updated_at;

        return $log;
    }

    /**
     * Update an existing webhook log
     */
    public function update(WhatsAppWebhookLogDomain $log, int $organization_id): WhatsAppWebhookLogDomain
    {
        WhatsAppWebhookLog::where('id', $log->id)
            ->where('organization_id', $organization_id)
            ->update($log->toUpdateArray());

        return $log;
    }

    /**
     * Find webhook log by ID
     */
    public function fetchById(int $id): ?WhatsAppWebhookLogDomain
    {
        $model = WhatsAppWebhookLog::find($id);

        if (!$model) {
            return null;
        }

        return $this->factory->buildFromModel($model);
    }

    /**
     * Get webhook log by ID or fail
     */
    public function fetchByIdOrFail(int $id): WhatsAppWebhookLogDomain
    {
        $model = WhatsAppWebhookLog::findOrFail($id);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Delete webhook log by ID
     */
    public function delete(WhatsAppWebhookLogDomain $log): bool
    {
        return WhatsAppWebhookLog::find($log->id)->delete();
    }

    /**
     * Get all webhook logs with filters and pagination
     */
    public function fetchAll(WhatsAppWebhookLogFilters $filters, OrderBy $orderBy): array
    {
        $webhookLogs = [];

        $models = EloquentBuilder::to(WhatsAppWebhookLog::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $webhookLogs[] = $this->factory->buildFromModel($model);
        }

        return [
            'data' => $webhookLogs,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Get webhook logs from organization with filters and pagination
     */
    public function fetchFromOrganization(int $organizationId, WhatsAppWebhookLogFilters $filters, OrderBy $orderBy): array
    {
        $webhookLogs = [];

        $models = EloquentBuilder::to(WhatsAppWebhookLog::class, $filters->filters)
            ->where('organization_id', $organizationId)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $webhookLogs[] = $this->factory->buildFromModel($model);
        }

        return [
            'data' => $webhookLogs,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Get webhook logs by organization
     */
    public function fetchByOrganization(int $organizationId, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::byOrganization($organizationId)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get webhook logs by phone number
     */
    public function fetchByPhoneNumber(string $phoneNumberId, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::byPhoneNumber($phoneNumberId)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get webhook logs by event type
     */
    public function fetchByEventType(string $eventType, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::byEventType($eventType)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get webhook logs by processing status
     */
    public function fetchByProcessingStatus(string $status, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::byProcessingStatus($status)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get recent webhook logs
     */
    public function fetchRecent(int $hours = 24, int $limit = 100): array
    {
        $models = WhatsAppWebhookLog::recent($hours)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get pending webhook logs
     */
    public function fetchPending(int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::pending()
                                   ->orderBy('created_at', 'asc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get failed webhook logs
     */
    public function fetchFailed(int $hours = 24, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::failed()
                                   ->recent($hours)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get successful webhook logs
     */
    public function fetchSuccessful(int $hours = 24, int $limit = 50): array
    {
        $models = WhatsAppWebhookLog::successful()
                                   ->recent($hours)
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return $this->factory->buildCollection($models);
    }

    /**
     * Get webhook logs with filters
     */
    public function fetchWithFilters(array $filters = [], int $page = 1, int $perPage = 15): array
    {
        $query = WhatsAppWebhookLog::query();

        if (isset($filters['organization_id'])) {
            $query->byOrganization($filters['organization_id']);
        }

        if (isset($filters['phone_number_id'])) {
            $query->byPhoneNumber($filters['phone_number_id']);
        }

        if (isset($filters['event_type'])) {
            $query->byEventType($filters['event_type']);
        }

        if (isset($filters['processing_status'])) {
            $query->byProcessingStatus($filters['processing_status']);
        }

        if (isset($filters['hours'])) {
            $query->recent($filters['hours']);
        }

        $models = $query->orderBy('created_at', 'desc')
                       ->paginate($perPage, ['*'], 'page', $page);

        return [
            'data' => $this->factory->buildCollection($models->items()),
            'pagination' => [
                'current_page' => $models->currentPage(),
                'per_page' => $models->perPage(),
                'total' => $models->total(),
                'last_page' => $models->lastPage(),
                'has_more_pages' => $models->hasMorePages(),
            ]
        ];
    }

    /**
     * Get statistics for webhook logs
     */
    public function getStatistics(int $hours = 24): array
    {
        $total = WhatsAppWebhookLog::recent($hours)->count();
        $pending = WhatsAppWebhookLog::pending()->recent($hours)->count();
        $successful = WhatsAppWebhookLog::successful()->recent($hours)->count();
        $failed = WhatsAppWebhookLog::failed()->recent($hours)->count();

        return [
            'total' => $total,
            'pending' => $pending,
            'successful' => $successful,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
            'failure_rate' => $total > 0 ? round(($failed / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Delete old webhook logs
     */
    public function deleteOldLogs(float $days = 90): int
    {
        return WhatsAppWebhookLog::where('created_at', '<', now()->subDays($days))->delete();
    }

    /**
     * Delete old webhook logs by status
     */
    public function deleteOldLogsByStatus(string $status, int $days = 30): int
    {
        return WhatsAppWebhookLog::where('processing_status', $status)
                                 ->where('created_at', '<', now()->subDays($days))
                                 ->delete();
    }

    /**
     * Count old webhook logs that would be deleted
     */
    public function countOldLogs(int $days = 90): int
    {
        return WhatsAppWebhookLog::where('created_at', '<', now()->subDays($days))->count();
    }

    /**
     * Mark log as successful
     */
    public function markAsSuccessful(int $id): WhatsAppWebhookLogDomain
    {
        $model = WhatsAppWebhookLog::findOrFail($id);
        $model->markAsSuccessful();

        return $this->factory->buildFromModel($model->fresh());
    }

    /**
     * Mark log as failed
     */
    public function markAsFailed(int $id, string $errorMessage): WhatsAppWebhookLogDomain
    {
        $model = WhatsAppWebhookLog::findOrFail($id);
        $model->markAsFailed($errorMessage);

        return $this->factory->buildFromModel($model->fresh());
    }
}
