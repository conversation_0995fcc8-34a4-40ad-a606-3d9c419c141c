<?php

namespace App\Repositories;

use App\Domains\ChatBot\Campaign as CampaignDomain;
use App\Domains\Filters\CampaignFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\CampaignFactory;
use App\Helpers\DBLog;
use App\Models\Campaign;
use EloquentBuilder;

class CampaignRepository
{
    private CampaignFactory $campaignFactory;

    public function __construct(CampaignFactory $campaignFactory){
        $this->campaignFactory = $campaignFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(CampaignFilters $filters, OrderBy $orderBy) : array {
        $campaigns = [];

        $models = EloquentBuilder::to(Campaign::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $campaigns[] = $this->campaignFactory->buildFromModel($model);
        }

        return [
            'data' => $campaigns,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, CampaignFilters $filters, OrderBy $orderBy) : array {
        $campaigns = [];

        $models = EloquentBuilder::to(Campaign::class, $filters->filters)
            ->with('template')
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $campaigns[] = $this->campaignFactory->buildFromModel($model);
        }

        return [
            'data' => $campaigns,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromPhoneNumber($phone_number_id, CampaignFilters $filters, OrderBy $orderBy) : array {
        $campaigns = [];

        $models = EloquentBuilder::to(Campaign::class, $filters->filters)
            ->with('template')
            ->with('phoneNumber')
            ->where("phone_number_id", $phone_number_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $campaigns[] = $this->campaignFactory->buildFromModel($model, true, false, false, false, true);
        }

        return [
            'data' => $campaigns,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, CampaignFilters $filters): int {
        return EloquentBuilder::to(Campaign::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, CampaignFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Campaign::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(CampaignDomain $campaign) : CampaignDomain {
        $savedCampaign = Campaign::create($campaign->toStoreArray());

        $campaign->id = $savedCampaign->id;

        return $campaign;
    }

    public function update(CampaignDomain $campaign, int $organization_id) : CampaignDomain {
        Campaign::where('id', $campaign->id)
            ->where('organization_id', $organization_id)
            ->update($campaign->toUpdateArray());

        return $campaign;
    }

    public function save(CampaignDomain $campaign, int $organization_id) : CampaignDomain {
        if($campaign->id){
            return $this->update($campaign, $organization_id);
        }
        return $this->store($campaign);
    }

    public function fetchById(int $id) : CampaignDomain {
        return $this->campaignFactory->buildFromModel(
            Campaign::findOrFail($id)
        );
    }

    public function fetchFullById(int $id) : CampaignDomain {
        return $this->campaignFactory->buildFromModel(
            Campaign::findOrFail($id), true, true, true, true, true
        );
    }

    public function fetchByBot(string $bot) : CampaignDomain {
        return $this->campaignFactory->buildFromModel(
            Campaign::where('bot', $bot)->first()
        );
    }

    public function delete(CampaignDomain $campaign) : bool {
        return Campaign::find($campaign->id)->delete();
    }

    public function addClients(int $campaign_id, array $client_ids) : bool {
        try{
            Campaign::find($campaign_id)->clients()->syncWithoutDetaching($client_ids);
        } catch (\Throwable $e){
            DBLog::logError(
                $e->getMessage(),
                "CampaignRepository::addClients",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ["campaign_id" => $campaign_id, "client_ids" => $client_ids] ?? null
            );
            return false;
        }
        return true;
    }

    public function removeClient(int $campaign_id, int $client_id) : bool
    {
        try{
            Campaign::find($campaign_id)->clients()->detach($client_id);
        } catch (\Throwable $e){
            DBLog::logError(
                $e->getMessage(),
                "CampaignRepository::removeClient",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ["campaign_id" => $campaign_id, "client_id" => $client_id] ?? null
            );
            return false;
        }
        return true;
    }
}
