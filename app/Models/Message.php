<?php

namespace App\Models;

use App\Enums\MessageStatus;
use App\Models\Template;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'messages';

    protected $fillable = [
        'organization_id',
        'campaign_id',
        'template_id',
        'client_id',
        'message',
        'status',
        'is_sent',
        'is_delivered',
        'is_fail',
        'is_read',
        'is_direct_message',
        'delivery_attempts',
        'last_attempt_at',
        'max_retries',
        'next_retry_at',
        'last_error_message',
        'sent_at',
        'scheduled_at',
    ];

    protected $casts = [
        'status' => MessageStatus::class,
        'is_sent' => 'boolean',
        'is_delivered' => 'boolean',
        'is_read' => 'boolean',
        'is_fail' => 'boolean',
        'is_direct_message' => 'boolean',
        'delivery_attempts' => 'integer',
        'max_retries' => 'integer',
        'last_attempt_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'sent_at' => 'datetime',
        'scheduled_at' => 'datetime',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function campaign(){
        return $this->belongsTo(Campaign::class);
    }
    public function client()
    {
        return $this->belongsTo(Client::class);
    }
    public function template(){
        return $this->belongsTo(Template::class);
    }

    public function deliveryAttempts()
    {
        return $this->hasMany(MessageDeliveryAttempt::class)->orderBy('attempt_number', 'desc');
    }

    /**
     * Check if message can be retried
     */
    public function canRetry(): bool
    {
        return $this->delivery_attempts < $this->max_retries &&
               $this->status === MessageStatus::is_failed;
    }

    /**
     * Check if message is ready for retry
     */
    public function isReadyForRetry(): bool
    {
        return $this->canRetry() &&
               ($this->next_retry_at === null || $this->next_retry_at <= now());
    }

    /**
     * Calculate next retry time using exponential backoff
     */
    public function calculateNextRetryTime(): ?\Carbon\Carbon
    {
        if (!$this->canRetry()) {
            return null;
        }

        // Exponential backoff: 1min, 5min, 15min, 30min, 1hour
        $backoffMinutes = match($this->delivery_attempts) {
            0 => 1,
            1 => 5,
            2 => 15,
            3 => 30,
            default => 60
        };

        return now()->addMinutes($backoffMinutes);
    }

    /**
     * Record a delivery attempt
     */
    public function recordDeliveryAttempt(MessageStatus $status, ?string $errorMessage = null, ?array $whatsappResponse = null): void
    {
        $this->delivery_attempts++;
        $this->last_attempt_at = now();
        $this->status = $status;

        if ($status === MessageStatus::is_failed) {
            $this->last_error_message = $errorMessage;
            $this->next_retry_at = $this->calculateNextRetryTime();
        } else {
            $this->last_error_message = null;
            $this->next_retry_at = null;
            if ($status === MessageStatus::is_sent) {
                $this->sent_at = now();
                $this->is_sent = true;
                $this->is_fail = false;
            }
        }

        $this->save();

        // Create delivery attempt record
        MessageDeliveryAttempt::create([
            'message_id' => $this->id,
            'attempt_number' => $this->delivery_attempts,
            'status' => $status->value,
            'error_message' => $errorMessage,
            'whatsapp_response_json' => $whatsappResponse,
            'attempted_at' => now(),
        ]);
    }

    /**
     * Get the latest delivery attempt
     */
    public function getLatestDeliveryAttempt(): ?MessageDeliveryAttempt
    {
        return $this->deliveryAttempts()->first();
    }

    /**
     * Check if message has failed permanently (max retries reached)
     */
    public function hasFailedPermanently(): bool
    {
        return $this->delivery_attempts >= $this->max_retries &&
               $this->status === MessageStatus::is_failed;
    }
}
