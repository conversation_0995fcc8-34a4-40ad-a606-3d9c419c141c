<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\Message;
use App\Domains\Inventory\Client;
use App\Enums\MessageStatus;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Message\StoreRequest;
use App\Http\Requests\Message\UpdateRequest;
use App\Models\Message as MessageModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class MessageFactory
{

    public ClientFactory $clientFactory;
    public TemplateFactory $templateFactory;
    public CampaignFactory $campaignFactory;

    public function __construct(ClientFactory $clientFactory, TemplateFactory $templateFactory)
    {
        $this->clientFactory = $clientFactory;
        $this->templateFactory = $templateFactory;
        $this->campaignFactory = new CampaignFactory(
            $this->templateFactory,
            $this,
            $this->clientFactory,
            new PhoneNumberFactory()
        );
    }

    public function buildFromStoreRequest(StoreRequest $request): Message
    {
        return new Message(
            id: null,
            organization_id: $request->organization_id ?? null,
            campaign_id: $request->campaign_id ?? null,
            template_id: $request->template_id ?? null,
            client_id: $request->client_id ?? null,
            message: $request->message ?? null,
            status: MessageStatus::tryFrom($request->status ?? 1) ?? MessageStatus::is_draft,
            is_sent: $request->is_sent ?? false,
            is_delivered: $request->is_delivered ?? false,
            is_fail: $request->is_fail ?? false,
            is_read: $request->is_read ?? false,
            is_direct_message: $request->is_direct_message ?? false,
            delivery_attempts: $request->delivery_attempts ?? 0,
            last_attempt_at: isset($request->last_attempt_at) ? Carbon::parse($request->last_attempt_at) : null,
            max_retries: $request->max_retries ?? 3,
            next_retry_at: isset($request->next_retry_at) ? Carbon::parse($request->next_retry_at) : null,
            last_error_message: $request->last_error_message ?? null,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            scheduled_at: isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
            created_at: null,
            updated_at: null,
            client: null,
            template: null,
            campaign: null,
            delivery_attempts_history: null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Message {
        return new Message(
            id: null,
            organization_id: null,
            campaign_id: null,
            template_id: $request->template_id ?? null,
            client_id: null,
            message: $request->message ?? null,
            status: MessageStatus::tryFrom($request->status ?? 1) ?? MessageStatus::is_draft,
            is_sent: $request->is_sent ?? false,
            is_delivered: $request->is_delivered ?? false,
            is_fail: $request->is_fail ?? false,
            is_read: $request->is_read ?? false,
            is_direct_message: $request->is_direct_message ?? false,
            delivery_attempts: null,
            last_attempt_at: null,
            max_retries: null,
            next_retry_at: null,
            last_error_message: null,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            scheduled_at: isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
            created_at: null,
            updated_at: null,
            client: null,
            template: null,
            campaign: null,
            delivery_attempts_history: null,
        );
    }

    public function buildFromModel(?MessageModel $message, bool $with_relations = false): ?Message
    {
        if (!$message) {
            return null;
        }

        if($with_relations){
            $client = $this->clientFactory->buildFromModel($message->client ?? null);
            $template = $this->templateFactory->buildFromModel($message->template ?? null);
            $campaign = $this->campaignFactory->buildFromModel($message->campaign ?? null, false);
        }

        return new Message(
            id: $message->id ?? null,
            organization_id: $message->organization_id ?? null,
            campaign_id: $message->campaign_id ?? null,
            template_id: $message->template_id ?? null,
            client_id: $message->client_id ?? null,
            message: $message->message ?? null,
            status: $message->status ?? MessageStatus::is_draft,
            is_sent: $message->is_sent ?? false,
            is_delivered: $message->is_delivered ?? false,
            is_fail: $message->is_fail ?? false,
            is_read: $message->is_read ?? false,
            is_direct_message: $message->is_direct_message ?? false,
            delivery_attempts: $message->delivery_attempts ?? 0,
            last_attempt_at: $message->last_attempt_at ? Carbon::parse($message->last_attempt_at) : null,
            max_retries: $message->max_retries ?? 3,
            next_retry_at: $message->next_retry_at ? Carbon::parse($message->next_retry_at) : null,
            last_error_message: $message->last_error_message ?? null,
            sent_at: $message->sent_at ? Carbon::parse($message->sent_at) : null,
            scheduled_at: $message->scheduled_at ? Carbon::parse($message->scheduled_at) : null,
            created_at: $message->created_at ? Carbon::parse($message->created_at) : null,
            updated_at: $message->updated_at ? Carbon::parse($message->updated_at) : null,
            client: ($with_relations) ? $client : null,
            template: ($with_relations) ? $template : null,
            campaign: ($with_relations) ? $campaign : null,
            delivery_attempts_history: null, // delivery_attempts_history - would need to be loaded separately
        );
    }

    public function buildFromModels(?Collection $messages): ?array
    {
        if (!$messages) {  return null; }

        $domains = [];

        /** @var MessageModel $message */
        foreach ($messages as $message) {
            $domains[] = $this->buildFromModel($message);
        }

        return $domains;
    }

    public function buildFromGeneration(Campaign $campaign, Client $client, int $organization_id, bool $is_draft = false, bool $is_direct_message = false) : Message {
        return new Message(
            id: null,
            organization_id: $organization_id,
            campaign_id: $campaign->id ?? null,
            template_id: $campaign->template_id ?? null,
            client_id: $client->id ?? null,
            message: $campaign->template->toWhatsAppRawMessage() ?? null,
            status: ($is_draft) ? MessageStatus::is_draft : MessageStatus::is_sending,
            is_sent: false,
            is_fail: false,
            is_read: false,
            is_direct_message: $is_direct_message,
            delivery_attempts: 0,
            last_attempt_at: null,
            max_retries: 3,
            next_retry_at: null,
            last_error_message: null,
            sent_at: null,
            scheduled_at: $campaign->scheduled_at ?? null,
            created_at: null,
            updated_at: null,
            client: $client ?? null,
            template: $campaign->template ?? null,
            campaign: $campaign ?? null,
            delivery_attempts_history: null,
        );
    }
}
