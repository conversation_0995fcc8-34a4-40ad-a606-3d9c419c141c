<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\StepNavigation;
use App\Http\Requests\StepNavigation\StoreRequest;
use App\Http\Requests\StepNavigation\UpdateRequest;
use App\Models\StepNavigation as StepNavigationModel;
use Illuminate\Support\Collection;

class StepNavigationFactory
{
    private StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory)
    {
        $this->stepFactory = $stepFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): StepNavigation
    {
        return new StepNavigation(
            null,
            $request->organization_id ?? null,
            $request->step_id ?? null,
            $request->condition_type ?? null,
            $request->condition_data ?? null,
            $request->target_step_identifier ?? null,
            $request->priority ?? 0,
            $request->is_active ?? true,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): StepNavigation
    {
        return new StepNavigation(
            null,
            null, // organization_id not updated
            null, // step_id not updated
            $request->condition_type ?? null,
            $request->condition_data ?? null,
            $request->target_step_identifier ?? null,
            $request->priority ?? null,
            $request->is_active ?? null,
        );
    }

    public function buildFromModel(?StepNavigationModel $stepNavigation, bool $with_step = false): ?StepNavigation
    {
        if (!$stepNavigation) {
            return null;
        }

        return new StepNavigation(
            $stepNavigation->id ?? null,
            $stepNavigation->organization_id ?? null,
            $stepNavigation->step_id ?? null,
            $stepNavigation->condition_type ?? null,
            $stepNavigation->condition_data ?? null,
            $stepNavigation->target_step_identifier ?? null,
            $stepNavigation->priority ?? 0,
            $stepNavigation->is_active ?? true,
            $stepNavigation->created_at ?? null,
            $stepNavigation->updated_at ?? null,
            ($with_step && $stepNavigation->step) ? $this->stepFactory->buildFromModel($stepNavigation->step, false, false) : null,
        );
    }

    /**
     * @param Collection|null $stepNavigations
     * @param bool $with_step
     * @return StepNavigation[]|null
     */
    public function buildFromModels(?Collection $stepNavigations, bool $with_step = false): ?array
    {
        if (!$stepNavigations) {
            return null;
        }

        $domains = [];

        /** @var StepNavigationModel $stepNavigation */
        foreach ($stepNavigations as $stepNavigation) {
            $domains[] = $this->buildFromModel($stepNavigation, $with_step);
        }

        return $domains;
    }

    /**
     * Build from array data (for SaveFullStep use case)
     */
    public function buildFromArray(array $navigationData, ?int $organizationId, ?int $stepId, ?int $id = null): StepNavigation
    {
        return new StepNavigation(
            $id,
            $organizationId,
            $stepId,
            $navigationData['condition_type'] ?? null,
            $navigationData['condition_data'] ?? null,
            $navigationData['target_step_identifier'] ?? null,
            $navigationData['priority'] ?? 0,
            $navigationData['is_active'] ?? true,
        );
    }

    /**
     * Build multiple navigation rules from array
     */
    public function buildFromArrays(array $navigationRules, ?int $organizationId, ?int $stepId): array
    {
        $domains = [];

        foreach ($navigationRules as $rule) {
            $domains[] = $this->buildFromArray($rule, $organizationId, $stepId);
        }

        return $domains;
    }

    /**
     * Create a button click navigation rule
     */
    public function createButtonClickRule(
        int $organizationId,
        int $stepId,
        string $buttonId,
        string $targetStepIdentifier,
        int $priority = 0,
        ?string $buttonText = null
    ): StepNavigation {
        return StepNavigation::createButtonClickRule(
            $organizationId,
            $stepId,
            $buttonId,
            $targetStepIdentifier,
            $priority,
            $buttonText
        );
    }

    /**
     * Create a text match navigation rule
     */
    public function createTextMatchRule(
        int $organizationId,
        int $stepId,
        string $text,
        string $targetStepIdentifier,
        int $priority = 0,
        bool $caseSensitive = false
    ): StepNavigation {
        return StepNavigation::createTextMatchRule(
            $organizationId,
            $stepId,
            $text,
            $targetStepIdentifier,
            $priority,
            $caseSensitive
        );
    }

    /**
     * Create a regex navigation rule
     */
    public function createRegexRule(
        int $organizationId,
        int $stepId,
        string $pattern,
        string $targetStepIdentifier,
        int $priority = 0,
        string $flags = ''
    ): StepNavigation {
        return StepNavigation::createRegexRule(
            $organizationId,
            $stepId,
            $pattern,
            $targetStepIdentifier,
            $priority,
            $flags
        );
    }

    /**
     * Create a default navigation rule
     */
    public function createDefaultRule(
        int $organizationId,
        int $stepId,
        string $targetStepIdentifier,
        int $priority = 999
    ): StepNavigation {
        return StepNavigation::createDefaultRule(
            $organizationId,
            $stepId,
            $targetStepIdentifier,
            $priority
        );
    }
}
