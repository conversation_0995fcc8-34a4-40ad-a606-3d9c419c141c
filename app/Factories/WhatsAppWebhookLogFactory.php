<?php

namespace App\Factories;

use App\Domains\WhatsAppWebhookLog;
use App\Models\WhatsAppWebhookLog as WhatsAppWebhookLogModel;
use App\Http\Requests\WhatsAppWebhookLog\StoreRequest;
use App\Http\Requests\WhatsAppWebhookLog\UpdateRequest;
use Carbon\Carbon;

class WhatsAppWebhookLogFactory
{
    /**
     * Build domain from model
     */
    public function buildFromModel(WhatsAppWebhookLogModel $model): WhatsAppWebhookLog
    {
        return new WhatsAppWebhookLog(
            id: $model->id,
            organization_id: $model->organization_id,
            phone_number_id: $model->phone_number_id,
            event_type: $model->event_type,
            webhook_payload: $model->webhook_payload ?? [],
            processed_at: $model->processed_at,
            processing_status: $model->processing_status,
            error_message: $model->error_message,
            created_at: $model->created_at,
            updated_at: $model->updated_at
        );
    }

    /**
     * Build collection of domains from models
     */
    public function buildCollection($models): array
    {
        return collect($models)->map(fn($model) => $this->buildFromModel($model))->toArray();
    }

    /**
     * Build domain from StoreRequest
     */
    public function buildFromStoreRequest(StoreRequest $request): WhatsAppWebhookLog
    {
        $data = $request->validated();

        return WhatsAppWebhookLog::create(
            organization_id: $data['organization_id'] ?? null,
            phone_number_id: $data['phone_number_id'],
            event_type: $data['event_type'],
            webhook_payload: $data['webhook_payload'],
            processing_status: $data['processing_status'] ?? 'pending',
            error_message: $data['error_message'] ?? null
        );
    }

    /**
     * Build domain from UpdateRequest for updating existing domain
     */
    public function buildFromUpdateRequest(UpdateRequest $request): WhatsAppWebhookLog
    {
        $data = $request->validated();

        return new WhatsAppWebhookLog(
            id: null,
            organization_id: null,
            phone_number_id: $data['phone_number_id'],
            event_type: $data['event_type'],
            webhook_payload: $data['webhook_payload'],
            processing_status: $data['processing_status'] ?? 'pending',
            error_message: $data['error_message'] ?? null
        );
    }

    /**
     * Create domain for message webhook
     */
    public function createForMessage(
        ?int $organizationId,
        string $phoneNumberId,
        array $webhookPayload,
        string $processingStatus = 'pending',
        ?string $errorMessage = null
    ): WhatsAppWebhookLog {
        return WhatsAppWebhookLog::create(
            organization_id: $organizationId,
            phone_number_id: $phoneNumberId,
            event_type: 'message',
            webhook_payload: $webhookPayload,
            processing_status: $processingStatus,
            error_message: $errorMessage
        );
    }

    /**
     * Create domain for status webhook
     */
    public function createForStatus(
        ?int $organizationId,
        string $phoneNumberId,
        array $webhookPayload,
        string $processingStatus = 'pending',
        ?string $errorMessage = null
    ): WhatsAppWebhookLog {
        return WhatsAppWebhookLog::create(
            organization_id: $organizationId,
            phone_number_id: $phoneNumberId,
            event_type: 'status',
            webhook_payload: $webhookPayload,
            processing_status: $processingStatus,
            error_message: $errorMessage
        );
    }

    /**
     * Create domain for other webhook types
     */
    public function createForOther(
        ?int $organizationId,
        string $phoneNumberId,
        array $webhookPayload,
        string $processingStatus = 'pending',
        ?string $errorMessage = null
    ): WhatsAppWebhookLog {
        return WhatsAppWebhookLog::create(
            organization_id: $organizationId,
            phone_number_id: $phoneNumberId,
            event_type: 'other',
            webhook_payload: $webhookPayload,
            processing_status: $processingStatus,
            error_message: $errorMessage
        );
    }

    /**
     * Create sample message webhook payload for testing
     */
    public function createSampleMessagePayload(string $messageId = 'msg_123', string $from = '*************'): array
    {
        return [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '*********',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'metadata' => [
                                    'display_phone_number' => '*************',
                                    'phone_number_id' => '***************'
                                ],
                                'contacts' => [
                                    [
                                        'profile' => [
                                            'name' => 'Test User'
                                        ],
                                        'wa_id' => $from
                                    ]
                                ],
                                'messages' => [
                                    [
                                        'from' => $from,
                                        'id' => $messageId,
                                        'timestamp' => now()->timestamp,
                                        'text' => [
                                            'body' => 'Hello, this is a test message'
                                        ],
                                        'type' => 'text'
                                    ]
                                ]
                            ],
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Create sample status webhook payload for testing
     */
    public function createSampleStatusPayload(string $messageId = 'msg_123', string $status = 'delivered'): array
    {
        return [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '*********',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'metadata' => [
                                    'display_phone_number' => '*************',
                                    'phone_number_id' => '***************'
                                ],
                                'statuses' => [
                                    [
                                        'id' => $messageId,
                                        'status' => $status,
                                        'timestamp' => now()->timestamp,
                                        'recipient_id' => '*************'
                                    ]
                                ]
                            ],
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Create domain for testing with fake data
     */
    public function createFake(array $overrides = []): WhatsAppWebhookLog
    {
        $defaults = [
            'id' => fake()->randomNumber(5),
            'organization_id' => fake()->randomNumber(3),
            'phone_number_id' => '***************',
            'event_type' => fake()->randomElement(['message', 'status', 'other']),
            'webhook_payload' => $this->createSampleMessagePayload(),
            'processed_at' => fake()->boolean(70) ? fake()->dateTimeBetween('-1 hour', 'now') : null,
            'processing_status' => fake()->randomElement(['pending', 'success', 'failed']),
            'error_message' => fake()->boolean(20) ? fake()->sentence() : null,
            'created_at' => fake()->dateTimeBetween('-1 day', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 hour', 'now'),
        ];

        $data = array_merge($defaults, $overrides);

        return new WhatsAppWebhookLog(
            id: $data['id'],
            organization_id: $data['organization_id'],
            phone_number_id: $data['phone_number_id'],
            event_type: $data['event_type'],
            webhook_payload: $data['webhook_payload'],
            processed_at: $data['processed_at'] ? Carbon::parse($data['processed_at']) : null,
            processing_status: $data['processing_status'],
            error_message: $data['error_message'],
            created_at: $data['created_at'] ? Carbon::parse($data['created_at']) : null,
            updated_at: $data['updated_at'] ? Carbon::parse($data['updated_at']) : null
        );
    }

    /**
     * Create multiple fake domains for testing
     */
    public function createFakeCollection(int $count = 5, array $overrides = []): array
    {
        $collection = [];
        for ($i = 0; $i < $count; $i++) {
            $collection[] = $this->createFake($overrides);
        }
        return $collection;
    }
}
