<?php

namespace App\Domains\ChatBot;

use App\Enums\ChatBot\WhatsAppButtonType;
use App\Helpers\DBLog;
use Carbon\Carbon;

class Button
{
    // Legacy constants for backward compatibility - deprecated, use WhatsAppButtonType enum instead
    private const string REPLY_TYPE = "QUICK_REPLY";
    private const string URL_TYPE = "URL";
    private const string PHONE_NUMBER_TYPE = "PHONE_NUMBER";

    public ?int $id;
    public ?int $organization_id;
    public ?string $text;
    public ?string $type;
    public ?string $internal_type;
    public ?string $internal_data;
    public ?string $callback_data;
    public ?string $json;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?array $callback_data_array = null;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?string $text,
        ?string $type,
        ?string $internal_type,
        ?string $internal_data,
        ?string $callback_data,
        ?string $json,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->text = $text;
        $this->type = $type;
        $this->internal_type = $internal_type;
        $this->internal_data = $internal_data;
        $this->callback_data = $callback_data;
        $this->json = $json;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->setCallbackDataArray();
    }

    public function setCallbackDataArray() : void {
        try{
            $this->callback_data_array = json_decode($this->callback_data, true);
        }catch (\Throwable $exception){
            $this->callback_data_array = [];
            DBLog::logError("possible not array: ".$exception->getMessage(), "Domain::Button", $this->organization_id);
        }
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "text" => $this->text,
            "type" => $this->type,
            "internal_type" => $this->internal_type,
            "internal_data" => $this->internal_data,
            "callback_data" => $this->callback_data,
            "callback_data_array" => $this->callback_data_array,
            "json" => $this->json,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s")
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "text" => $this->text,
            "type" => $this->type,
            "internal_type" => $this->internal_type,
            "internal_data" => $this->internal_data,
            "callback_data" => $this->callback_data,
            "json" => $this->json,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "text" => $this->text,
            "type" => $this->type,
            "internal_type" => $this->internal_type,
            "internal_data" => $this->internal_data,
            "callback_data" => $this->callback_data,
            "json" => $this->json,
        ];
    }

    public function toWhatsAppPayload(): array
    {
        // Normalize type to enum value for consistent handling
        $buttonType = $this->getWhatsAppButtonType();

        if (!$buttonType) {
            // Fallback to JSON if type is not recognized
            return json_decode($this->json, true) ?? [];
        }

        return $buttonType->toWhatsAppPayload([
            'id' => $this->id ?? uniqid(),
            'title' => $this->text ?? '',
            'url' => $this->getInternalDataForWhatsApp(),
            'phone_number' => $this->getInternalDataForWhatsApp(),
            'copy_code' => $this->getInternalDataForWhatsApp(),
            'flow_token' => $this->getInternalDataForWhatsApp(),
        ]);
    }

    public function toReplyWhatsAppPayload(): array
    {
        return [
            'type' => self::REPLY_TYPE,
            'text' => $this->text ?? '',
        ];
    }
    public function toUrlWhatsAppPayload(): array
    {
        return [
            'type' => self::URL_TYPE,
            'text' => $this->text ?? '',
            'url' => $this->getInternalDataForWhatsApp(),
        ];
    }
    public function toCallWhatsAppPayload(): array
    {
        return [
            'type' => self::PHONE_NUMBER_TYPE,
            'text' => $this->text ?? '',
            'phone_number' => $this->getInternalDataForWhatsApp(),
        ];
    }



    /**
     * @throws \Exception
     */
    protected function ensureKeys(array $keys): void
    {
        foreach ($keys as $key) {
            if (empty($this->callback_data_array[$key])) {
                throw new \Exception("Missing required callback_data key: $key");
            }
        }
    }

    /**
     * Check if this button is used for conditional navigation
     */
    public function isConditionalButton(): bool
    {
        return $this->internal_type === 'condition';
    }

    /**
     * Get target step identifier for conditional navigation
     */
    public function getTargetStepIdentifier(): ?string
    {
        if (!$this->isConditionalButton()) {
            return null;
        }

        // Try to get from callback_data_array first
        if (isset($this->callback_data_array['target_step'])) {
            return $this->callback_data_array['target_step'];
        }

        // Fallback to callback_data as string
        return $this->callback_data;
    }

    /**
     * Set target step for conditional navigation
     */
    public function setTargetStep(string $stepIdentifier): void
    {
        $this->internal_type = 'condition';
        $this->callback_data = json_encode(['target_step' => $stepIdentifier]);
        $this->setCallbackDataArray();
    }

    /**
     * Create a conditional navigation button
     */
    public static function createConditionalButton(
        int $organizationId,
        string $text,
        string $targetStepIdentifier,
        string $buttonId = null
    ): self {
        $callbackData = [
            'target_step' => $targetStepIdentifier,
            'button_id' => $buttonId ?? strtolower(str_replace(' ', '_', $text))
        ];

        return new self(
            null, // id
            $organizationId,
            $text,
            'QUICK_REPLY', // type
            'condition', // internal_type
            null, // internal_data
            json_encode($callbackData),
            json_encode(['conditional_navigation' => true])
        );
    }

    /**
     * Extract internal data from button array based on button type
     * This method handles case-insensitive type and array key matching
     */
    public static function extractInternalDataFromButtonArray(array $button): ?string
    {
        $type = strtolower(trim($button['type'] ?? ''));

        return match ($type) {
            'url' => self::extractUrlData($button),
            'phone_number' => self::extractPhoneNumberData($button),
            'copy_code' => self::extractCopyCodeData($button),
            'flow' => self::extractFlowData($button),
            'quick_reply', 'reply' => null, // No internal data needed for reply buttons
            default => null,
        };
    }

    /**
     * Extract URL data from button array (case-insensitive)
     */
    private static function extractUrlData(array $button): ?string
    {
        // Try different case variations of 'url' key
        $urlKeys = ['url', 'URL', 'Url'];

        foreach ($urlKeys as $key) {
            if (isset($button[$key]) && !empty($button[$key])) {
                return $button[$key];
            }
        }

        return null;
    }

    /**
     * Extract phone number data from button array (case-insensitive)
     */
    private static function extractPhoneNumberData(array $button): ?string
    {
        // Try different case variations of phone number keys
        $phoneKeys = ['phone_number', 'PHONE_NUMBER', 'Phone_Number', 'phoneNumber', 'phone'];

        foreach ($phoneKeys as $key) {
            if (isset($button[$key]) && !empty($button[$key])) {
                return $button[$key];
            }
        }

        return null;
    }

    /**
     * Extract copy code data from button array (case-insensitive)
     */
    private static function extractCopyCodeData(array $button): ?string
    {
        // Try different case variations of copy code keys
        $codeKeys = ['copy_code', 'COPY_CODE', 'Copy_Code', 'copyCode', 'code'];

        foreach ($codeKeys as $key) {
            if (isset($button[$key]) && !empty($button[$key])) {
                return $button[$key];
            }
        }

        return null;
    }

    /**
     * Extract flow data from button array (case-insensitive)
     */
    private static function extractFlowData(array $button): ?string
    {
        // Try different case variations of flow keys
        $flowKeys = ['flow_token', 'FLOW_TOKEN', 'Flow_Token', 'flowToken', 'flow'];

        foreach ($flowKeys as $key) {
            if (isset($button[$key]) && !empty($button[$key])) {
                return $button[$key];
            }
        }

        return null;
    }

    /**
     * Get internal data for WhatsApp payload
     * Uses internal_data if available, otherwise falls back to callback_data_array
     */
    public function getInternalDataForWhatsApp(): ?string
    {
        if (!empty($this->internal_data)) {
            return $this->internal_data;
        }

        // Fallback to callback_data_array for backward compatibility
        $type = strtolower(trim($this->type ?? ''));

        return match ($type) {
            'url' => $this->callback_data_array['url'] ?? null,
            'phone_number' => $this->callback_data_array['phone_number'] ?? null,
            'copy_code' => $this->callback_data_array['copy_code'] ?? $this->callback_data_array['code'] ?? null,
            'flow' => $this->callback_data_array['flow_token'] ?? $this->callback_data_array['flow'] ?? null,
            default => null,
        };
    }

    /**
     * Get WhatsAppButtonType enum from current type
     */
    public function getWhatsAppButtonType(): ?WhatsAppButtonType
    {
        if (!$this->type) {
            return null;
        }

        // Normalize type string to match enum values
        $normalizedType = strtolower(trim($this->type));

        return match ($normalizedType) {
            'quick_reply', 'reply' => WhatsAppButtonType::REPLY,
            'url' => WhatsAppButtonType::URL,
            'phone_number' => WhatsAppButtonType::PHONE_NUMBER,
            'copy_code' => WhatsAppButtonType::COPY_CODE,
            'flow' => WhatsAppButtonType::FLOW,
            default => null,
        };
    }

    /**
     * Validate button for WhatsApp compliance
     */
    public function validateForWhatsApp(): bool
    {
        try {
            $buttonType = $this->getWhatsAppButtonType();

            if (!$buttonType) {
                throw new \Exception('Invalid button type: ' . $this->type);
            }

            // Validate title length
            if (!$this->validateTitleLength()) {
                throw new \Exception('Button title exceeds maximum length for type: ' . $this->type);
            }

            // Validate type-specific requirements
            $this->validateTypeSpecificRequirements($buttonType);

            return true;
        } catch (\Throwable $exception) {
            // Only log in non-testing environments to avoid database issues
            if (!app()->environment('testing')) {
                DBLog::logError(
                    $exception->getMessage() ?? null,
                    "ChatBot::Button::validateForWhatsApp",
                    $this->organization_id ?? null,
                    request()->user()->id ?? null,
                    $this->toArray()
                );
            }
            return false;
        }
    }

    /**
     * Validate button title length according to WhatsApp limits
     */
    public function validateTitleLength(): bool
    {
        $buttonType = $this->getWhatsAppButtonType();

        if (!$buttonType) {
            return false;
        }

        $maxLength = $buttonType->getMaxTitleLength();
        $titleLength = mb_strlen($this->text ?? '');

        return $titleLength <= $maxLength;
    }

    /**
     * Validate type-specific requirements
     */
    private function validateTypeSpecificRequirements(WhatsAppButtonType $buttonType): void
    {
        match ($buttonType) {
            WhatsAppButtonType::URL => $this->validateUrlButton(),
            WhatsAppButtonType::PHONE_NUMBER => $this->validatePhoneNumberButton(),
            WhatsAppButtonType::COPY_CODE => $this->validateCopyCodeButton(),
            WhatsAppButtonType::FLOW => $this->validateFlowButton(),
            WhatsAppButtonType::REPLY => null, // No additional validation needed for reply buttons
        };
    }

    /**
     * Validate URL button requirements
     */
    private function validateUrlButton(): void
    {
        $url = $this->getInternalDataForWhatsApp();

        if (empty($url)) {
            throw new \Exception('URL button requires a valid URL in internal_data or callback_data');
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new \Exception('URL button requires a valid URL format');
        }

        if (!str_starts_with($url, 'https://')) {
            throw new \Exception('URL button requires HTTPS URL');
        }
    }

    /**
     * Validate phone number button requirements
     */
    private function validatePhoneNumberButton(): void
    {
        $phoneNumber = $this->getInternalDataForWhatsApp();

        if (empty($phoneNumber)) {
            throw new \Exception('Phone number button requires a valid phone number in internal_data or callback_data');
        }

        // Basic phone number validation (international format)
        // Requires + followed by country code (1-3 digits) and number (4-15 total digits)
        if (!preg_match('/^\+[1-9]\d{3,14}$/', $phoneNumber)) {
            throw new \Exception('Phone number button requires valid international format (+1234567890)');
        }
    }

    /**
     * Validate copy code button requirements
     */
    private function validateCopyCodeButton(): void
    {
        $copyCode = $this->getInternalDataForWhatsApp();

        if (empty($copyCode)) {
            throw new \Exception('Copy code button requires a valid code in internal_data or callback_data');
        }
    }

    /**
     * Validate flow button requirements
     */
    private function validateFlowButton(): void
    {
        $flowToken = $this->getInternalDataForWhatsApp();

        if (empty($flowToken)) {
            throw new \Exception('Flow button requires a valid flow token in internal_data or callback_data');
        }
    }

    /**
     * Validate a collection of buttons for WhatsApp compliance
     *
     * @param Button[] $buttons
     * @return bool
     * @throws \Exception
     */
    public static function validateButtonCollection(array $buttons): bool
    {
        if (empty($buttons)) {
            return true;
        }

        // Count reply buttons
        $replyButtonCount = 0;
        $buttonTypeCount = [];

        foreach ($buttons as $button) {
            if (!($button instanceof self)) {
                throw new \Exception('All items must be Button instances');
            }

            // Validate individual button
            if (!$button->validateForWhatsApp()) {
                throw new \Exception('Button validation failed: ' . $button->text);
            }

            $buttonType = $button->getWhatsAppButtonType();
            if (!$buttonType) {
                continue;
            }

            // Count button types
            $typeValue = $buttonType->value;
            $buttonTypeCount[$typeValue] = ($buttonTypeCount[$typeValue] ?? 0) + 1;

            // Check reply button limit (special case)
            if ($buttonType === WhatsAppButtonType::REPLY) {
                $replyButtonCount++;
                if ($replyButtonCount > 3) {
                    throw new \Exception('Maximum of 3 reply buttons allowed per message');
                }
            } else {
                // Check individual button type limits for non-reply buttons
                $maxButtons = $buttonType->getMaxButtons();
                if ($buttonTypeCount[$typeValue] > $maxButtons) {
                    throw new \Exception("Too many {$typeValue} buttons. Maximum allowed: {$maxButtons}");
                }
            }
        }

        return true;
    }

    /**
     * Get maximum allowed buttons for current type
     */
    public function getMaxAllowedButtons(): int
    {
        $buttonType = $this->getWhatsAppButtonType();
        return $buttonType ? $buttonType->getMaxButtons() : 1;
    }

    /**
     * Check if this button is a reply type
     */
    public function isReplyButton(): bool
    {
        return $this->getWhatsAppButtonType() === WhatsAppButtonType::REPLY;
    }
}
