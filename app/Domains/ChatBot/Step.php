<?php

namespace App\Domains\ChatBot;

use App\Enums\StepType;
use Carbon\Carbon;
use App\Domains\ChatBot\StepNavigation;

class Step
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $flow_id;
    public ?string $step;
    public ?string $type; // Legacy field - will be deprecated
    public ?StepType $step_type; // New enum-based type
    public ?int $position;

    public ?int $next_step;
    public ?int $earlier_step;

    public ?bool $is_initial_step;
    public ?bool $is_ending_step;

    // New fields
    public ?array $configuration;
    public ?array $navigation_rules;
    public ?int $timeout_seconds;

    public ?string $json;
    public ?string $input;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Flow $flow;
    public ?Component $component;
    public ?array $stepNavigations;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $flow_id,
        ?string $step,
        ?string $type,
        ?StepType $step_type,
        ?int $position,
        ?int $next_step,
        ?int $earlier_step,
        ?bool $is_initial_step,
        ?bool $is_ending_step,
        ?array $configuration = null,
        ?array $navigation_rules = null,
        ?int $timeout_seconds = null,
        ?string $json = null,
        ?string $input = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Flow $flow = null,
        ?Component $component = null,
        ?array $stepNavigations = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->flow_id = $flow_id;
        $this->step = $step;
        $this->type = $type;
        $this->step_type = $step_type;
        $this->position = $position;
        $this->next_step = $next_step;
        $this->earlier_step = $earlier_step;
        $this->is_initial_step = $is_initial_step;
        $this->is_ending_step = $is_ending_step;
        $this->configuration = $configuration;
        $this->navigation_rules = $navigation_rules;
        $this->timeout_seconds = $timeout_seconds;
        $this->json = $json;
        $this->input = $input;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->flow = $flow;
        $this->component = $component;
        $this->stepNavigations = $stepNavigations;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "flow_id" => $this->flow_id,
            "step" => $this->step,
            "type" => $this->type, // Legacy field
            "step_type" => $this->step_type?->value,
            "position" => $this->position,
            "next_step" => $this->next_step,
            "earlier_step" => $this->earlier_step,
            "is_initial_step" => $this->is_initial_step,
            "is_ending_step" => $this->is_ending_step,
            "configuration" => $this->configuration,
            "navigation_rules" => $this->navigation_rules,
            "timeout_seconds" => $this->timeout_seconds,
            "json" => $this->json,
            "input" => $this->input,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "flow" => $this->flow?->toArray(),
            "component" => $this->component?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        $this->setStepTypeFromLegacyFields();
        $this->setDefaultConfiguration();

        return [
            "organization_id" => $this->organization_id,
            "flow_id" => $this->flow_id,
            "step" => $this->step,
            "type" => $this->type, // Legacy field
            "step_type" => $this->step_type?->value,
            "position" => $this->position,
            "next_step" => $this->next_step,
            "earlier_step" => $this->earlier_step,
            "is_initial_step" => $this->is_initial_step,
            "is_ending_step" => $this->is_ending_step,
            "configuration" => $this->configuration ? json_encode($this->configuration) : null,
            "navigation_rules" => $this->navigation_rules ? json_encode($this->navigation_rules) : null,
            "timeout_seconds" => $this->timeout_seconds,
            "json" => $this->json,
            "input" => $this->input,
        ];
    }

    public function toUpdateArray(): array
    {
        $this->setStepTypeFromLegacyFields();
        $this->setDefaultConfiguration();

        return [
            "step" => $this->step,
            "type" => $this->type, // Legacy field
            "step_type" => $this->step_type?->value,
            "position" => $this->position,
            "next_step" => $this->next_step,
            "earlier_step" => $this->earlier_step,
            "is_initial_step" => $this->is_initial_step,
            "is_ending_step" => $this->is_ending_step,
            "configuration" => $this->configuration ? json_encode($this->configuration) : null,
            "navigation_rules" => $this->navigation_rules ? json_encode($this->navigation_rules) : null,
            "timeout_seconds" => $this->timeout_seconds,
            "json" => $this->json,
            "input" => $this->input,
        ];
    }

    /**
     * Set step_type from legacy type field if not already set
     */
    public function setStepTypeFromLegacyFields(): void
    {
        if ($this->step_type) {
            return; // Already set
        }

        // Convert from legacy type field
        if ($this->type === 'interactive') {
            $this->step_type = StepType::INTERACTIVE;
        } elseif ($this->type === 'input') {
            $this->step_type = StepType::INPUT;
        } elseif ($this->type === 'command') {
            $this->step_type = StepType::COMMAND;
        } elseif ($this->type === 'text' || $this->type === 'message') {
            $this->step_type = StepType::MESSAGE;
        } else {
            // Default fallback
            $this->step_type = StepType::MESSAGE;
        }
    }

    /**
     * Set default configuration if not already set
     */
    public function setDefaultConfiguration(): void
    {
        if (!$this->configuration && $this->step_type) {
            $this->configuration = $this->step_type->getDefaultConfiguration();
        }
    }

    /**
     * Validate step configuration
     */
    public function validateConfiguration(): bool
    {
        if (!$this->step_type) {
            return false;
        }

        return $this->step_type->validateConfiguration($this->configuration ?? []);
    }

    public function hasButtons() : bool {
        return (count($this->component->buttons ?? []) > 0);
    }

    public function getInputField() : string|null {
        return explode('.', $this->input)[1] ?? null;
    }

    public function getInputDomain() : string|null {
        return explode('.', $this->input)[0] ?? null;
    }

    /**
     * Get next step based on navigation rules and user input
     */
    public function getNextStepByCondition(string $userInput, ?string $buttonId = null): ?int
    {
        if (!$this->navigation_rules || !is_array($this->navigation_rules)) {
            return $this->next_step;
        }

        foreach ($this->navigation_rules as $rule) {
            if ($this->evaluateNavigationRule($rule, $userInput, $buttonId)) {
                return $rule['target_step_id'] ?? null;
            }
        }

        // Return default next step if no rules match
        return $this->next_step;
    }

    /**
     * Evaluate a single navigation rule
     */
    private function evaluateNavigationRule(array $rule, string $userInput, ?string $buttonId = null): bool
    {
        $conditionType = $rule['condition_type'] ?? 'default';
        $conditionValue = $rule['condition_value'] ?? '';

        return match ($conditionType) {
            'button_click' => $buttonId === $conditionValue,
            'text_match' => strtolower($userInput) === strtolower($conditionValue),
            'text_contains' => str_contains(strtolower($userInput), strtolower($conditionValue)),
            'regex_match' => preg_match($conditionValue, $userInput),
            'default' => true,
            default => false,
        };
    }

    /**
     * Add navigation rule
     */
    public function addNavigationRule(string $conditionType, string $conditionValue, int $targetStepId, int $priority = 0): void
    {
        if (!$this->navigation_rules) {
            $this->navigation_rules = [];
        }

        $this->navigation_rules[] = [
            'condition_type' => $conditionType,
            'condition_value' => $conditionValue,
            'target_step_id' => $targetStepId,
            'priority' => $priority,
        ];

        // Sort by priority (higher priority first)
        usort($this->navigation_rules, fn($a, $b) => ($b['priority'] ?? 0) <=> ($a['priority'] ?? 0));
    }

    /**
     * Check if step has timeout configured
     */
    public function hasTimeout(): bool
    {
        return $this->timeout_seconds !== null && $this->timeout_seconds > 0;
    }

    /**
     * Get timeout in seconds
     */
    public function getTimeoutSeconds(): int
    {
        return $this->timeout_seconds ?? 0;
    }

    /**
     * Check if step has navigation rules
     */
    public function hasNavigationRules(): bool
    {
        return !empty($this->stepNavigations);
    }

    /**
     * Get navigation rules ordered by priority
     */
    public function getNavigationRules(): array
    {
        if (empty($this->stepNavigations)) {
            return [];
        }

        // Sort by priority (lower number = higher priority)
        usort($this->stepNavigations, function($a, $b) {
            return ($a->priority ?? 0) <=> ($b->priority ?? 0);
        });

        return $this->stepNavigations;
    }

    /**
     * Get active navigation rules only
     */
    public function getActiveNavigationRules(): array
    {
        if (empty($this->stepNavigations)) {
            return [];
        }

        $activeRules = array_filter($this->stepNavigations, function($rule) {
            return $rule->is_active ?? true;
        });

        // Sort by priority
        usort($activeRules, function($a, $b) {
            return ($a->priority ?? 0) <=> ($b->priority ?? 0);
        });

        return $activeRules;
    }

    /**
     * Find matching navigation rule for user input
     */
    public function findMatchingNavigationRule(string $userInput, ?array $context = null): ?StepNavigation
    {
        $activeRules = $this->getActiveNavigationRules();

        foreach ($activeRules as $rule) {
            if ($rule->matchesCondition($userInput, $context)) {
                return $rule;
            }
        }

        return null;
    }

    /**
     * Check if step supports conditional navigation
     */
    public function supportsConditionalNavigation(): bool
    {
        return $this->step_type?->supportsNavigationRules() ?? false;
    }

    /**
     * Get default navigation rule (fallback)
     */
    public function getDefaultNavigationRule(): ?StepNavigation
    {
        $activeRules = $this->getActiveNavigationRules();

        foreach ($activeRules as $rule) {
            if ($rule->condition_type === StepNavigation::DEFAULT) {
                return $rule;
            }
        }

        return null;
    }

    /**
     * Get navigation rules by condition type
     */
    public function getNavigationRulesByType(string $conditionType): array
    {
        if (empty($this->stepNavigations)) {
            return [];
        }

        return array_filter($this->stepNavigations, function($rule) use ($conditionType) {
            return $rule->condition_type === $conditionType && ($rule->is_active ?? true);
        });
    }

    /**
     * Check if step has button click navigation rules
     */
    public function hasButtonClickNavigation(): bool
    {
        return !empty($this->getNavigationRulesByType(StepNavigation::BUTTON_CLICK));
    }

    /**
     * Check if step has text match navigation rules
     */
    public function hasTextMatchNavigation(): bool
    {
        return !empty($this->getNavigationRulesByType(StepNavigation::TEXT_MATCH));
    }

    /**
     * Check if step has regex navigation rules
     */
    public function hasRegexNavigation(): bool
    {
        return !empty($this->getNavigationRulesByType(StepNavigation::REGEX));
    }

    /**
     * Check if step has default navigation rule
     */
    public function hasDefaultNavigation(): bool
    {
        return $this->getDefaultNavigationRule() !== null;
    }
}
