<?php

namespace App\UseCases\ChatBot\WhatsApp;

use App\Repositories\MessageRepository;
use App\Repositories\WhatsAppSyncLogRepository;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;

class SyncMessageStatus
{
    private MessageRepository $messageRepository;
    private WhatsAppMessageRepository $whatsappMessageRepository;
    private WhatsAppSyncLogRepository $syncLogRepository;

    public function __construct(
        MessageRepository $messageRepository,
        WhatsAppMessageRepository $whatsappMessageRepository,
        WhatsAppSyncLogRepository $syncLogRepository
    ) {
        $this->messageRepository = $messageRepository;
        $this->whatsappMessageRepository = $whatsappMessageRepository;
        $this->syncLogRepository = $syncLogRepository;
    }

    public function execute(int $message_id, int $organization_id, ?int $user_id = null): array
    {
        try {
            // Fetch message
            $message = $this->messageRepository->fetchById($message_id);

            if ($message->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Message not found or access denied");
            }

            // Get WhatsApp message
            $whatsappMessage = $this->whatsappMessageRepository->fetchByMessageId($message_id);
            if (!$whatsappMessage) {
                throw new \InvalidArgumentException("WhatsApp message not found");
            }

            // Simulate WhatsApp API status check
            $apiResponse = $this->checkMessageStatusWithWhatsAppAPI($whatsappMessage->whatsapp_message_id);

            $messagesUpdated = 0;
            $syncStatus = SyncStatus::SUCCESS;
            $errorMessage = null;

            if ($apiResponse['success']) {
                // Update message status if changed
                $newStatus = $this->mapWhatsAppStatusToMessageStatus($apiResponse['status']);

                // Don't overwrite recent webhook updates (within last 5 minutes)
                $recentlyUpdated = $message->updated_at && $message->updated_at->gt(now()->subMinutes(5));

                if ($message->status !== $newStatus && !$recentlyUpdated) {
                    $this->updateMessageStatus($message, $newStatus, $apiResponse);
                    $messagesUpdated = 1;
                }

                // Update WhatsApp message sync fields
                $this->updateWhatsAppMessageSyncFields($whatsappMessage, true);

                // Mark delivery as confirmed if delivered
                if ($apiResponse['status'] === 'delivered' || $apiResponse['status'] === 'read') {
                    $whatsappMessage->confirmDelivery();
                }

            } else {
                $syncStatus = SyncStatus::FAILED;
                $errorMessage = $apiResponse['error'] ?? 'Unknown error';
                $this->updateWhatsAppMessageSyncFields($whatsappMessage, false);
            }

            // Create sync log
            $syncLog = $this->syncLogRepository->createSyncLog(
                SyncType::MESSAGE,
                $message_id,
                $syncStatus,
                $apiResponse,
                $errorMessage,
                1, // messages_synced
                $messagesUpdated
            );

            DBLog::logInfo(
                "Message status sync completed",
                "WhatsApp::SyncMessageStatus",
                $organization_id,
                $user_id,
                [
                    'message_id' => $message_id,
                    'sync_status' => $syncStatus->value,
                    'messages_updated' => $messagesUpdated,
                    'api_status' => $apiResponse['status'] ?? null
                ]
            );

            return [
                'message_id' => $message_id,
                'sync_status' => $syncStatus->value,
                'messages_synced' => 1,
                'messages_updated' => $messagesUpdated,
                'api_response' => $apiResponse,
                'sync_log_id' => $syncLog->id,
                'message' => 'Message status sync completed successfully'
            ];

        } catch (\Throwable $e) {
            // Create failed sync log
            $this->syncLogRepository->createSyncLog(
                SyncType::MESSAGE,
                $message_id,
                SyncStatus::FAILED,
                null,
                $e->getMessage(),
                1,
                0
            );

            DBLog::logError(
                $e->getMessage(),
                "WhatsApp::SyncMessageStatus",
                $organization_id,
                $user_id,
                ['message_id' => $message_id]
            );
            throw $e;
        }
    }

    private function checkMessageStatusWithWhatsAppAPI(string $whatsapp_message_id): array
    {
        // This is a mock implementation
        // In a real scenario, this would call the WhatsApp Business API

        $statuses = ['sent', 'delivered', 'read', 'failed'];
        $randomStatus = $statuses[array_rand($statuses)];

        // Simulate API success/failure
        $isSuccess = rand(1, 100) <= 85; // 85% success rate

        if ($isSuccess) {
            return [
                'success' => true,
                'status' => $randomStatus,
                'timestamp' => now()->timestamp,
                'whatsapp_message_id' => $whatsapp_message_id,
                'details' => [
                    'recipient_id' => 'user_' . rand(1000, 9999),
                    'conversation_id' => 'conv_' . uniqid()
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => 'WhatsApp API error: ' . ['Rate limit exceeded', 'Invalid message ID', 'Service unavailable'][array_rand(['Rate limit exceeded', 'Invalid message ID', 'Service unavailable'])],
                'error_code' => rand(400, 500),
                'whatsapp_message_id' => $whatsapp_message_id
            ];
        }
    }

    private function mapWhatsAppStatusToMessageStatus(string $whatsappStatus): MessageStatus
    {
        return match($whatsappStatus) {
            'sent' => MessageStatus::is_sent,
            'delivered' => MessageStatus::is_delivered,
            'read' => MessageStatus::is_read,
            'failed' => MessageStatus::is_failed,
            default => MessageStatus::is_draft
        };
    }

    private function updateMessageStatus($message, MessageStatus $newStatus, array $apiResponse): void
    {
        // Use the domain method to update status correctly
        $whatsappStatus = $apiResponse['status'] ?? 'unknown';
        $message->updateMessageStatus($whatsappStatus);

        // Save through repository
        $this->messageRepository->save($message, $message->organization_id);
    }

    private function updateWhatsAppMessageSyncFields($whatsappMessage, bool $success): void
    {
        $whatsappMessage->recordStatusCheck($success);
        // Save through repository would be called here
        // $this->whatsappMessageRepository->save($whatsappMessage);
    }
}
