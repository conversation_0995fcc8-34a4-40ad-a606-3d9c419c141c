<?php

/**
 * Test Generator for Domain and Factory Tests
 * 
 * This script automatically generates test files for all domains and factories
 * Usage: php tests/generate-tests.php [--force] [--dry-run]
 */

require_once __DIR__ . '/../vendor/autoload.php';

class TestGenerator
{
    private bool $force = false;
    private bool $dryRun = false;
    private array $generatedFiles = [];

    public function __construct(array $args = [])
    {
        $this->parseArguments($args);
    }

    private function parseArguments(array $args): void
    {
        foreach ($args as $arg) {
            if ($arg === '--force') {
                $this->force = true;
            } elseif ($arg === '--dry-run') {
                $this->dryRun = true;
            }
        }
    }

    public function run(): int
    {
        $this->printHeader();
        
        // Generate domain tests
        $this->generateDomainTests();
        
        // Generate factory tests
        $this->generateFactoryTests();
        
        $this->printSummary();
        
        return 0;
    }

    private function generateDomainTests(): void
    {
        echo "Generating Domain Tests...\n";
        
        $domainPaths = $this->findDomainFiles();
        
        foreach ($domainPaths as $domainPath) {
            $this->generateDomainTest($domainPath);
        }
    }

    private function generateFactoryTests(): void
    {
        echo "\nGenerating Factory Tests...\n";
        
        $factoryPaths = $this->findFactoryFiles();
        
        foreach ($factoryPaths as $factoryPath) {
            $this->generateFactoryTest($factoryPath);
        }
    }

    private function findDomainFiles(): array
    {
        $domains = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(__DIR__ . '/../app/Domains')
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $relativePath = str_replace(__DIR__ . '/../app/Domains/', '', $file->getPathname());
                $relativePath = str_replace('.php', '', $relativePath);
                $domains[] = $relativePath;
            }
        }
        
        return $domains;
    }

    private function findFactoryFiles(): array
    {
        $factories = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(__DIR__ . '/../app/Factories')
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $relativePath = str_replace(__DIR__ . '/../app/Factories/', '', $file->getPathname());
                $relativePath = str_replace('.php', '', $relativePath);
                $factories[] = $relativePath;
            }
        }
        
        return $factories;
    }

    private function generateDomainTest(string $domainPath): void
    {
        $testPath = "tests/Unit/Domains/{$domainPath}Test.php";
        
        if (file_exists($testPath) && !$this->force) {
            echo "  ⏭️  Skipping {$domainPath}Test (already exists)\n";
            return;
        }

        $namespace = 'Tests\\Unit\\Domains\\' . str_replace('/', '\\', dirname($domainPath));
        if (dirname($domainPath) === '.') {
            $namespace = 'Tests\\Unit\\Domains';
        }
        
        $className = basename($domainPath) . 'Test';
        $domainClass = 'App\\Domains\\' . str_replace('/', '\\', $domainPath);
        
        $content = $this->generateDomainTestContent($namespace, $className, $domainClass, $domainPath);
        
        if ($this->dryRun) {
            echo "  📝 Would generate {$testPath}\n";
        } else {
            $this->ensureDirectoryExists(dirname($testPath));
            file_put_contents($testPath, $content);
            echo "  ✅ Generated {$testPath}\n";
            $this->generatedFiles[] = $testPath;
        }
    }

    private function generateFactoryTest(string $factoryPath): void
    {
        $testPath = "tests/Unit/Factories/{$factoryPath}Test.php";
        
        if (file_exists($testPath) && !$this->force) {
            echo "  ⏭️  Skipping {$factoryPath}Test (already exists)\n";
            return;
        }

        $namespace = 'Tests\\Unit\\Factories\\' . str_replace('/', '\\', dirname($factoryPath));
        if (dirname($factoryPath) === '.') {
            $namespace = 'Tests\\Unit\\Factories';
        }
        
        $className = basename($factoryPath) . 'Test';
        $factoryClass = 'App\\Factories\\' . str_replace('/', '\\', $factoryPath);
        
        $content = $this->generateFactoryTestContent($namespace, $className, $factoryClass, $factoryPath);
        
        if ($this->dryRun) {
            echo "  📝 Would generate {$testPath}\n";
        } else {
            $this->ensureDirectoryExists(dirname($testPath));
            file_put_contents($testPath, $content);
            echo "  ✅ Generated {$testPath}\n";
            $this->generatedFiles[] = $testPath;
        }
    }

    private function generateDomainTestContent(string $namespace, string $className, string $domainClass, string $domainPath): string
    {
        $domainName = basename($domainPath);
        
        return "<?php

namespace {$namespace};

use {$domainClass};
use Tests\\Unit\\Domains\\BaseDomainTest;
use Carbon\\Carbon;

class {$className} extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        \$domain = \$this->createDomainInstance();
        
        \$this->assertInstanceOf({$domainName}::class, \$domain);
        // Add specific assertions for {$domainName} properties
    }

    public function test_to_array_method()
    {
        \$domain = \$this->createDomainInstance();
        \$array = \$domain->toArray();

        \$this->assertIsArray(\$array);
        \$this->assertArrayStructure(\$this->getExpectedArrayKeys(), \$array);
    }

    protected function createDomainInstance()
    {
        // TODO: Implement domain instance creation for {$domainName}
        // Return new {$domainName}(...);
        \$this->markTestIncomplete('Domain instance creation not implemented for {$domainName}');
    }

    protected function getExpectedArrayKeys(): array
    {
        // TODO: Define expected array keys for {$domainName}
        return [
            'id',
            // Add other expected keys
        ];
    }
}
";
    }

    private function generateFactoryTestContent(string $namespace, string $className, string $factoryClass, string $factoryPath): string
    {
        $factoryName = basename($factoryPath);
        
        return "<?php

namespace {$namespace};

use {$factoryClass};
use Tests\\Unit\\Factories\\BaseFactoryTest;
use Illuminate\\Foundation\\Testing\\RefreshDatabase;

class {$className} extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        \$model = \$this->createModelInstance();
        \$factory = \$this->createFactoryInstance();
        \$domain = \$factory->buildFromModel(\$model);

        \$this->assertInstanceOf(\$this->getDomainClass(), \$domain);
        // Add specific assertions for {$factoryName}
    }

    protected function createFactoryInstance()
    {
        // TODO: Implement factory instance creation for {$factoryName}
        // return new {$factoryName}();
        \$this->markTestIncomplete('Factory instance creation not implemented for {$factoryName}');
    }

    protected function getDomainClass(): string
    {
        // TODO: Return the domain class that this factory creates
        return 'App\\\\Domains\\\\YourDomain';
    }

    protected function createModelInstance()
    {
        // TODO: Create and return a model instance for testing
        \$this->markTestIncomplete('Model instance creation not implemented for {$factoryName}');
    }
}
";
    }

    private function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    private function printHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════════════════════╗\n";
        echo "║                              Test Generator                                  ║\n";
        echo "║                    Automatically Generate Domain & Factory Tests            ║\n";
        echo "╚══════════════════════════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        if ($this->dryRun) {
            echo "🔍 DRY RUN MODE - No files will be created\n\n";
        }
        
        if ($this->force) {
            echo "⚠️  FORCE MODE - Existing files will be overwritten\n\n";
        }
    }

    private function printSummary(): void
    {
        echo "\n";
        echo "Summary:\n";
        echo "  Generated Files: " . count($this->generatedFiles) . "\n";
        
        if (!$this->dryRun && count($this->generatedFiles) > 0) {
            echo "\nNext Steps:\n";
            echo "  1. Review generated test files\n";
            echo "  2. Implement TODO sections in each test\n";
            echo "  3. Run tests: php vendor/bin/phpunit tests/Unit/\n";
            echo "  4. Or use: php tests/run-domain-factory-tests.php\n";
        }
        echo "\n";
    }
}

// Run the generator
$generator = new TestGenerator(array_slice($argv, 1));
exit($generator->run());
