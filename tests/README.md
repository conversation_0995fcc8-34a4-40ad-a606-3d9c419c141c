# Domain & Factory Testing Framework

This testing framework provides comprehensive test coverage for all Domain objects and Factory classes in your application using SQLite memory database for fast, isolated testing.

## 🚀 Quick Start

### 1. Run Existing Tests
```bash
# Run all domain and factory tests
php tests/run-domain-factory-tests.php

# Run with verbose output
php tests/run-domain-factory-tests.php --verbose

# Run specific tests
php tests/run-domain-factory-tests.php --filter=Template

# Or use PHPUnit directly
php vendor/bin/phpunit tests/Unit/
```

### 2. Generate Tests for All Domains and Factories
```bash
# Generate test files for all domains and factories
php tests/generate-tests.php

# Preview what would be generated (dry run)
php tests/generate-tests.php --dry-run

# Force overwrite existing files
php tests/generate-tests.php --force
```

## 📁 Test Structure

```
tests/
├── Unit/
│   ├── Domains/
│   │   ├── BaseDomainTest.php          # Base class for domain tests
│   │   ├── UserTest.php                # User domain tests
│   │   ├── OrganizationTest.php        # Organization domain tests
│   │   └── ChatBot/
│   │       ├── TemplateTest.php        # Template domain tests
│   │       ├── FlowTest.php           # Flow domain tests
│   │       └── ...
│   └── Factories/
│       ├── BaseFactoryTest.php         # Base class for factory tests
│       ├── UserFactoryTest.php         # User factory tests
│       ├── OrganizationFactoryTest.php # Organization factory tests
│       └── ChatBot/
│           ├── TemplateFactoryTest.php # Template factory tests
│           └── ...
├── Feature/                            # Integration tests
├── generate-tests.php                  # Test generator script
├── run-domain-factory-tests.php       # Test runner script
└── README.md                          # This file
```

## 🧪 Test Types

### Domain Tests
Domain tests verify that your domain objects:
- ✅ Can be instantiated with all properties
- ✅ Have correct `toArray()` method implementation
- ✅ Have correct `toStoreArray()` method (excludes id, timestamps)
- ✅ Have correct `toUpdateArray()` method (excludes id, timestamps)
- ✅ Handle business logic correctly
- ✅ Validate data properly

### Factory Tests
Factory tests verify that your factories:
- ✅ Can build domain objects from Eloquent models
- ✅ Return null when given null models
- ✅ Can build from store requests
- ✅ Can build from update requests
- ✅ Handle dependencies correctly

## 🗄️ Database Configuration

The tests use SQLite memory database for:
- **Speed**: In-memory database is extremely fast
- **Isolation**: Each test gets a fresh database
- **Simplicity**: No external database setup required

Configuration in `phpunit.xml`:
```xml
<env name="DB_CONNECTION" value="sqlite"/>
<env name="DB_DATABASE" value=":memory:"/>
```

## 📝 Writing Tests

### Creating a Domain Test

1. Extend `BaseDomainTest`
2. Implement required methods:
   - `test_domain_instantiation()`
   - `test_to_array_method()`
   - `createDomainInstance()`
   - `getExpectedArrayKeys()`

Example:
```php
<?php

namespace Tests\Unit\Domains;

use App\Domains\YourDomain;
use Tests\Unit\Domains\BaseDomainTest;

class YourDomainTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();
        
        $this->assertInstanceOf(YourDomain::class, $domain);
        $this->assertEquals('expected_value', $domain->property);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    protected function createDomainInstance()
    {
        return new YourDomain(
            1,
            'test_value',
            // ... other parameters
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return ['id', 'property', 'created_at', 'updated_at'];
    }
}
```

### Creating a Factory Test

1. Extend `BaseFactoryTest`
2. Implement required methods:
   - `test_build_from_model()`
   - `createFactoryInstance()`
   - `getDomainClass()`
   - `createModelInstance()`

Example:
```php
<?php

namespace Tests\Unit\Factories;

use App\Factories\YourFactory;
use App\Domains\YourDomain;
use App\Models\YourModel;
use Tests\Unit\Factories\BaseFactoryTest;

class YourFactoryTest extends BaseFactoryTest
{
    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(YourDomain::class, $domain);
        $this->assertEquals($model->id, $domain->id);
    }

    protected function createFactoryInstance()
    {
        return new YourFactory();
    }

    protected function getDomainClass(): string
    {
        return YourDomain::class;
    }

    protected function createModelInstance()
    {
        return YourModel::factory()->create();
    }
}
```

## 🏭 Model Factories

Laravel model factories are included for testing:
- `UserFactory` - Creates test users
- `OrganizationFactory` - Creates test organizations  
- `TemplateFactory` - Creates test templates
- `PhoneNumberFactory` - Creates test phone numbers

## 🔧 Utilities

### Test Generator
Automatically generates test files for all domains and factories:
```bash
php tests/generate-tests.php
```

### Test Runner
Runs all tests with nice formatting:
```bash
php tests/run-domain-factory-tests.php
```

## 📊 Coverage

The framework tests:
- ✅ **13 Core Domains**: User, Organization, Profile, Log, etc.
- ✅ **13 ChatBot Domains**: Template, Component, Flow, Step, etc.
- ✅ **19 Inventory Domains**: Product, Stock, Client, etc.
- ✅ **13 Import Domains**: Import processes and validations
- ✅ **All Corresponding Factories**: 50+ factory classes

## 🎯 Best Practices

1. **Keep tests focused**: Test one thing at a time
2. **Use descriptive names**: `test_validates_email_format_correctly`
3. **Test edge cases**: null values, empty arrays, invalid data
4. **Mock dependencies**: Use mocks for external services
5. **Clean data**: Use `RefreshDatabase` trait for isolation

## 🚨 Troubleshooting

### Common Issues

**Tests fail with database errors:**
- Ensure SQLite is enabled in PHP
- Check that migrations are up to date

**Factory tests fail:**
- Verify model factories exist in `database/factories/`
- Check that relationships are properly defined

**Domain tests fail:**
- Verify domain constructors match test parameters
- Check that required methods exist (`toArray`, `toStoreArray`, etc.)

### Getting Help

1. Check test output for specific error messages
2. Run with `--verbose` flag for detailed output
3. Use `--filter` to isolate problematic tests
4. Review base test classes for available helper methods

## 📈 Next Steps

1. **Run the generator** to create all test files
2. **Implement TODOs** in generated test files
3. **Add business logic tests** for domain-specific functionality
4. **Create integration tests** for complex workflows
5. **Set up CI/CD** to run tests automatically
