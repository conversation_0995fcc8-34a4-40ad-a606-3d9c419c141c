# ProjectProduct Module Test Suite Documentation

## Overview

This document provides a comprehensive overview of the test suite implemented for the ProjectProduct module in the Inventory system. The test suite covers all layers of the application architecture with both unit and integration tests, following the same high-quality patterns established for the Client, Project, Product, Stock, Budget, Batch, Brand, Sale, Shop, StockEntry, StockExit, Item, BudgetProduct, and CustomProduct modules.

## Test Structure

### 1. Domain Tests (`tests/Unit/Domains/Inventory/ProjectProductTest.php`)

**Purpose**: Test the ProjectProduct domain object and its methods in isolation.

**Coverage**:
- ✅ Domain instantiation with all properties (project_id, product_id, quantity, value, description)
- ✅ Domain instantiation with minimal data
- ✅ `toArray()` method functionality with relationships
- ✅ `toStoreArray()` method (excludes id, timestamps)
- ✅ `toUpdateArray()` method (excludes id, timestamps)
- ✅ Date handling (null dates use Carbon::now())
- ✅ Quantity and value handling (zero, high, decimal values)
- ✅ Description handling (including long descriptions)
- ✅ Relationship handling (Project, Product)
- ✅ Custom product support (product_id can be null)

**Key Test Cases**:
- Project products with complete data (all relationships)
- Minimal project products (required fields only)
- Zero, high, and decimal quantity/value handling
- Array conversion methods with proper field exclusions
- Relationship handling with Project and Product objects
- Long description handling
- Custom products (null product_id)

### 2. Factory Tests (`tests/Unit/Factories/Inventory/ProjectProductFactoryTest.php`)

**Purpose**: Test the ProjectProductFactory's ability to build domain objects from various sources.

**Coverage**:
- ✅ `buildFromModel()` - Convert Eloquent model to domain with relationships
- ✅ `buildFromStoreRequest()` - Build from HTTP store request
- ✅ `buildFromUpdateRequest()` - Build from HTTP update request
- ✅ `buildFromStoreCustomRequest()` - Build from HTTP store custom request
- ✅ Relationship loading control (with_project, with_product parameters)
- ✅ Null handling for all methods
- ✅ Quantity and value variations
- ✅ Description variations (including long descriptions)
- ✅ Various quantities and values testing
- ✅ Custom product handling

**Key Test Cases**:
- Model to domain conversion with relationship loading
- Request to domain conversion with proper null handling
- Quantity and value handling across all methods
- Relationship loading control testing
- Long description handling
- Custom product request handling

### 3. Repository Tests (`tests/Unit/Repositories/ProjectProductRepositoryTest.php`)

**Purpose**: Test the ProjectProductRepository's data access operations.

**Coverage**:
- ✅ `fetchAll()` with pagination
- ✅ `fetchFromOrganization()` with organization isolation
- ✅ `fetchFromProject()` with project-specific filtering
- ✅ `fetchById()` with success and not found scenarios
- ✅ `store()` - Create new project products
- ✅ `storeCustom()` - Create new custom project products
- ✅ `delete()` - Hard delete project products (no soft delete)
- ✅ Organization isolation via project relationships
- ✅ Quantity and value handling (zero, high, decimal)
- ✅ Description handling (including long descriptions)
- ✅ Various quantities and values testing
- ✅ Custom product storage and retrieval

**Key Test Cases**:
- Pagination with different limits
- Organization-based data isolation via relationships
- Project-specific project product retrieval
- Hard delete behavior verification
- Quantity and value range handling
- Multiple project products from same project
- Custom product storage with null product_id
- Mixed regular and custom product handling

### 4. UseCase Tests (`tests/Unit/UseCases/Inventory/ProjectProduct/`)

**Purpose**: Test business logic and orchestration in UseCases.

#### Store UseCase (`StoreTest.php`)
- ✅ Successful project product creation
- ✅ Different quantities and values handling
- ✅ Zero and high quantity handling
- ✅ Factory and repository exception handling
- ✅ Decimal values handling
- ✅ Long description handling

#### StoreCustom UseCase (`StoreCustomTest.php`)
- ✅ Successful custom project product creation
- ✅ Ensures product_id is null for custom products
- ✅ Different quantities and values handling
- ✅ Zero quantity handling
- ✅ Factory and repository exception handling
- ✅ Decimal values and long description handling

#### Delete UseCase (`DeleteTest.php`)
- ✅ Successful deletion with hard delete
- ✅ Repository exception handling
- ✅ Different project product types handling (regular and custom)
- ✅ Zero quantity and high value handling
- ✅ Long description handling
- ✅ Various project and product ID combinations

#### Get UseCase (`GetTest.php`)
- ✅ Retrieve project product by ID
- ✅ Handle not found scenarios
- ✅ Different project product types and value ranges
- ✅ Complete property verification
- ✅ Quantity and value variations
- ✅ Various project and product ID combinations
- ✅ Long description handling
- ✅ Custom product retrieval

#### GetAll UseCase (`GetAllTest.php`)
- ✅ Paginated results with organization filtering
- ✅ Empty results handling
- ✅ Pagination support
- ✅ Error handling
- ✅ Various project product types handling
- ✅ Multiple projects handling
- ✅ Long description handling
- ✅ Mixed regular and custom products

#### GetAllFromProject UseCase (`GetAllFromProjectTest.php`)
- ✅ Project-specific project product retrieval
- ✅ Empty project handling
- ✅ Custom project products from specific project
- ✅ Mixed regular and custom products from project
- ✅ Various quantities and values
- ✅ Long description handling
- ✅ Large number of products handling
- ✅ Project ownership verification

### 5. Integration/Feature Tests (`tests/Feature/Api/Inventory/ProjectProductTest.php`)

**Purpose**: Test complete HTTP request-to-response flows through all application layers.

**Coverage**:
- ✅ Project product creation with proper JSON structure
- ✅ Custom project product creation with proper JSON structure
- ✅ Project product retrieval with proper JSON structure
- ✅ Project product deletion with hard delete verification
- ✅ Organization-based access control via relationships
- ✅ Pagination via HTTP
- ✅ Project-specific project product retrieval via HTTP
- ✅ Validation error handling
- ✅ Data consistency across operations
- ✅ Hard delete behavior
- ✅ Quantity and value variations (zero, high, decimal)
- ✅ Organization isolation testing via relationships
- ✅ Multiple project products from same project
- ✅ Long description handling
- ✅ Complete CRUD workflow testing (regular and custom)
- ✅ Mixed regular and custom product handling

**Key Test Cases**:
- Complete CRUD operations via API (regular and custom)
- Authentication and authorization
- Input validation and error responses
- Data transformation and consistency
- Quantity and value management
- Project-specific project product management
- Organization isolation verification via relationships
- Long description support
- Mixed regular and custom product scenarios
- Custom product creation with null product_id

## Test Execution

### Running All ProjectProduct Tests
```bash
# Run all project product-related tests
php artisan test tests/Unit/Domains/Inventory/ProjectProductTest.php
php artisan test tests/Unit/Factories/Inventory/ProjectProductFactoryTest.php
php artisan test tests/Unit/Repositories/ProjectProductRepositoryTest.php
php artisan test tests/Unit/UseCases/Inventory/ProjectProduct/
php artisan test tests/Feature/Api/Inventory/ProjectProductTest.php

# Run all tests with coverage
php artisan test --coverage
```

### Test Categories
- **Unit Tests**: 11 test classes, ~220 test methods
- **Integration Tests**: 1 test class, ~30 test methods
- **Total Coverage**: ~250 test methods covering all ProjectProduct module functionality

## Test Quality Features

### 1. Comprehensive Business Logic Testing
- **Project Product Management**: Complete CRUD operations for regular products
- **Custom Product Management**: Complete CRUD operations for custom products
- **Project Integration**: Project products belong to specific projects
- **Product Integration**: Project products reference specific products (or null for custom)
- **Organization Isolation**: Indirect organization-based access control via relationships
- **Data Validation**: Quantity, value, and description validation

### 2. Advanced Repository Testing
- **Project-Specific Queries**: Fetch project products from specific projects
- **Custom Product Storage**: Separate storage method for custom products
- **Hard Delete**: Proper hard delete behavior verification (no soft delete)
- **Numeric Handling**: Zero, high, and decimal quantity/value handling
- **Organization Filtering**: Organization-based data isolation via relationships
- **Relationship Loading**: Conditional relationship loading
- **Description Handling**: Support for long descriptions

### 3. UseCase Complexity
- **Dual Storage Paths**: Separate UseCases for regular and custom products
- **Relationship Validation**: Consistent across all operations
- **Error Handling**: Comprehensive exception scenarios
- **Data Transformation**: Request to domain conversion
- **Project Integration**: Project-specific project product operations

### 4. Integration Test Completeness
- **Project Integration**: Complete project-project product relationship workflows
- **Product Integration**: Complete product-project product relationship workflows
- **Custom Product Integration**: Complete custom product workflows
- **Data Consistency**: Verification across multiple operations
- **Organization Security**: Indirect access control testing via relationships
- **Numeric Validation**: Quantity and value handling
- **Relationship Integration**: Complete relationship workflows
- **Description Support**: Long description handling

## Key Features of ProjectProduct Module

### 1. ProjectProduct Properties
- **Core Info**: Quantity (required), value (required), description (required)
- **Relationships**: Project (required), Product (optional - null for custom)
- **Organization**: Indirect organization-based isolation via project relationships
- **Project Integration**: Project products belong to specific projects
- **Product Integration**: Project products reference specific products or are custom

### 2. Advanced Functionality
- **Dual Product Types**: Regular products (with product_id) and custom products (product_id = null)
- **Project Management**: Project products are grouped by projects
- **Product Integration**: Project products reference specific products when not custom
- **Relationship Management**: Complex relationship handling with conditional loading
- **Description Support**: Full support for long descriptions

### 3. Business Rules
- **Organization Isolation**: Project products belong to organizations via project relationships
- **Project Integration**: Project products must belong to a project
- **Product Integration**: Project products can reference a product or be custom (null product_id)
- **Hard Delete**: Project products are hard deleted (no soft delete)
- **Required Relationships**: Project is required, Product is optional
- **Description Required**: Description is a required field
- **Custom Product Support**: Full support for custom products without product references

## Coverage Metrics

The test suite provides comprehensive coverage of:
- **Domain Logic**: 100% of ProjectProduct domain methods
- **Factory Methods**: 100% of ProjectProductFactory methods including custom product handling
- **Repository Operations**: 100% of ProjectProductRepository methods including custom storage
- **UseCase Logic**: 100% of all ProjectProduct UseCases (6 total including custom)
- **API Endpoints**: 100% of ProjectProduct API routes including custom endpoints
- **Error Scenarios**: Comprehensive error handling coverage
- **Business Rules**: All domain-specific rules including custom product rules
- **Project Integration**: Complete project-project product relationship workflows
- **Product Integration**: Complete product-project product relationship workflows
- **Custom Product Integration**: Complete custom product workflows

## Maintenance Guidelines

### Adding New Tests
1. Follow the established naming conventions
2. Use appropriate mocking for unit tests
3. Include both success and failure scenarios
4. Test edge cases including numeric variations and long descriptions
5. Maintain organization isolation via relationship testing
6. Test project integration scenarios
7. Test both regular and custom product scenarios

### Project Integration Testing
- Always test project-project product relationships
- Verify project-specific project product retrieval
- Test project integration across operations
- Validate project-based filtering

### Custom Product Testing
- Always test custom product creation and retrieval
- Verify product_id is null for custom products
- Test custom product workflows separately
- Validate custom product storage and retrieval

### Relationship Testing
- Test Project and Product relationships
- Verify conditional relationship loading
- Test relationship integrity across operations
- Validate relationship-based operations
- Test organization isolation via relationships
- Handle null product_id for custom products

This comprehensive test suite ensures the ProjectProduct module is robust, maintainable, and handles the complex business logic around project product management, project integration, product integration, custom product support, and relationship management reliably for production use.
