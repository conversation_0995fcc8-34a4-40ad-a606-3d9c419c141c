<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProductFilters;
use App\Domains\Inventory\Product;
use App\Factories\Inventory\ProductFactory;
use App\Models\Brand;
use App\Models\Organization;
use App\Models\Product as ProductModel;
use App\Repositories\ProductRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProductRepository $repository;
    private ProductFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ProductRepository::class);
        $this->factory = app(ProductFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test products
        ProductModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $product) {
            $this->assertInstanceOf(Product::class, $product);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create products with different names
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Product'
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Product'
        ]);

        $filters = new ProductFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Product', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more products than the limit
        ProductModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_products()
    {
        $otherOrganization = Organization::factory()->create();

        // Create products for different organizations
        ProductModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        ProductModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $product) {
            $this->assertEquals($this->organization->id, $product->organization_id);
        }
    }

    public function test_fetch_by_id_returns_product()
    {
        $model = ProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $product = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals($model->id, $product->id);
        $this->assertEquals($model->name, $product->name);
        $this->assertEquals($model->organization_id, $product->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_product()
    {
        $domain = new Product(
            id: null,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($domain->name, $result->name);
        $this->assertEquals($domain->organization_id, $result->organization_id);

        // Verify it was actually saved to database
        $this->assertDatabaseHas('products', [
            'id' => $result->id,
            'name' => 'Test Product',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_product()
    {
        $model = ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $domain = new Product(
            id: $model->id,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Updated Name',
            barcode: '9876543210987',
            description: 'Updated description',
            price: 149.99,
            unity: 2,
            last_priced_at: now()
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);

        // Verify it was actually updated in database
        $this->assertDatabaseHas('products', [
            'id' => $model->id,
            'name' => 'Updated Name',
            'description' => 'Updated description'
        ]);
    }

    public function test_delete_removes_product()
    {
        $model = ProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('products', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        ProductModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create products for different organization
        $otherOrg = Organization::factory()->create();
        ProductModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new ProductFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 100.00
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 200.00
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 300.00
        ]);

        $filters = new ProductFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'price');

        $this->assertEquals(600.00, $sum);
    }

    public function test_find_by_barcode_and_organization()
    {
        $product = ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'barcode' => '1234567890123'
        ]);

        $result = $this->repository->findByBarcodeAndOrganization('1234567890123', $this->organization->id);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($product->id, $result->id);
        $this->assertEquals('1234567890123', $result->barcode);
    }

    public function test_find_by_barcode_and_organization_returns_null_when_not_found()
    {
        $result = $this->repository->findByBarcodeAndOrganization('nonexistent', $this->organization->id);

        $this->assertNull($result);
    }

    public function test_save_creates_or_updates_product()
    {
        // Test create
        $newDomain = new Product(
            id: null,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'New Product',
            barcode: '1234567890123',
            description: 'New product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $savedNew = $this->repository->save($newDomain);

        $this->assertInstanceOf(Product::class, $savedNew);
        $this->assertNotNull($savedNew->id);
        $this->assertEquals('New Product', $savedNew->name);

        // Test update
        $updateDomain = new Product(
            id: $savedNew->id,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Updated Product',
            barcode: '1234567890123',
            description: 'Updated product description',
            price: 149.99,
            unity: 1,
            last_priced_at: now()
        );

        $savedUpdated = $this->repository->save($updateDomain);

        $this->assertInstanceOf(Product::class, $savedUpdated);
        $this->assertEquals($savedNew->id, $savedUpdated->id);
        $this->assertEquals('Updated Product', $savedUpdated->name);
    }

    public function test_repository_handles_soft_deleted_products()
    {
        $model = ProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the product
        $model->delete();

        // Should not find soft deleted product
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $product1 = ProductModel::factory()->create(['organization_id' => $org1->id]);
        $product2 = ProductModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new ProductFilters([]);

        // Count should only include products from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_fetch_all_with_brand_relationship()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);

        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true);

        $this->assertEquals(1, $result['count']);
        $product = $result['data'][0];
        $this->assertNotNull($product->brand);
        $this->assertEquals($brand->name, $product->brand->name);
    }

    public function test_fetch_all_without_brand_relationship()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);

        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, false);

        $this->assertEquals(1, $result['count']);
        $product = $result['data'][0];
        $this->assertNull($product->brand);
    }

    public function test_sum_with_filters()
    {
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Product',
            'price' => 100.00
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Product',
            'price' => 200.00
        ]);

        $filters = new ProductFilters(['name' => 'Alpha']);
        $sum = $this->repository->sum($this->organization->id, $filters, 'price');

        $this->assertEquals(100.00, $sum);
    }

    public function test_find_by_barcode_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        ProductModel::factory()->create([
            'organization_id' => $org1->id,
            'barcode' => '1234567890123'
        ]);
        ProductModel::factory()->create([
            'organization_id' => $org2->id,
            'barcode' => '1234567890123'
        ]);

        $result1 = $this->repository->findByBarcodeAndOrganization('1234567890123', $org1->id);
        $result2 = $this->repository->findByBarcodeAndOrganization('1234567890123', $org2->id);

        $this->assertInstanceOf(Product::class, $result1);
        $this->assertInstanceOf(Product::class, $result2);
        $this->assertEquals($org1->id, $result1->organization_id);
        $this->assertEquals($org2->id, $result2->organization_id);
        $this->assertNotEquals($result1->id, $result2->id);
    }

    public function test_repository_handles_different_price_ranges()
    {
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 0.0
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 999999.99
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'price', 'by' => 'asc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(0.0, $result['data'][0]->price);
        $this->assertEquals(999999.99, $result['data'][1]->price);
    }

    public function test_repository_handles_different_unity_values()
    {
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'unity' => 1
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'unity' => 2
        ]);
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'unity' => 3
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'unity', 'by' => 'asc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(1, $result['data'][0]->unity);
        $this->assertEquals(2, $result['data'][1]->unity);
        $this->assertEquals(3, $result['data'][2]->unity);
    }

    public function test_repository_handles_null_barcode()
    {
        ProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'barcode' => null
        ]);

        $filters = new ProductFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->barcode);
    }
}
