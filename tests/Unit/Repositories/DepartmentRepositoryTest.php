<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\DepartmentFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Department;
use App\Factories\Inventory\DepartmentFactory;
use App\Models\Department as DepartmentModel;
use App\Models\Organization;
use App\Repositories\DepartmentRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DepartmentRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private DepartmentRepository $repository;
    private DepartmentFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(DepartmentRepository::class);
        $this->factory = app(DepartmentFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test departments
        DepartmentModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $department) {
            $this->assertInstanceOf(Department::class, $department);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create departments with different names
        DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Department'
        ]);
        DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Department'
        ]);

        $filters = new DepartmentFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Department', $result['data'][0]->name);
    }

    public function test_fetch_all_with_is_active_filter()
    {
        // Create departments with different active states
        DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Department',
            'is_active' => true
        ]);
        DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Department',
            'is_active' => false
        ]);

        $filters = new DepartmentFilters(['is_active' => true]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Active Department', $result['data'][0]->name);
        $this->assertTrue($result['data'][0]->is_active);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more departments than the limit
        DepartmentModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_departments()
    {
        $otherOrganization = Organization::factory()->create();

        // Create departments for different organizations
        DepartmentModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        DepartmentModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $department) {
            $this->assertEquals($this->organization->id, $department->organization_id);
        }
    }

    public function test_fetch_by_id_returns_department()
    {
        $model = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $department = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Department::class, $department);
        $this->assertEquals($model->id, $department->id);
        $this->assertEquals($model->name, $department->name);
        $this->assertEquals($model->organization_id, $department->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_department()
    {
        $domain = new Department(
            id: null,
            organization_id: $this->organization->id,
            name: 'Store Test Department',
            is_active: true
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('Store Test Department', $result->name);

        $this->assertDatabaseHas('departments', [
            'id' => $result->id,
            'name' => 'Store Test Department',
            'organization_id' => $this->organization->id,
            'is_active' => true
        ]);
    }

    public function test_update_modifies_existing_department()
    {
        $model = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Department',
            'is_active' => true
        ]);

        $domain = new Department(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'New Department',
            is_active: false
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertEquals('New Department', $result->name);

        $this->assertDatabaseHas('departments', [
            'id' => $model->id,
            'name' => 'New Department',
            'is_active' => false
        ]);
    }

    public function test_delete_removes_department()
    {
        $model = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('departments', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        DepartmentModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create departments for different organization
        $otherOrg = Organization::factory()->create();
        DepartmentModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new DepartmentFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_count_with_filters()
    {
        DepartmentModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'is_active' => true
        ]);
        DepartmentModel::factory()->count(1)->create([
            'organization_id' => $this->organization->id,
            'is_active' => false
        ]);

        $filters = new DepartmentFilters(['is_active' => true]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(2, $count);
    }

    public function test_sum_returns_correct_value()
    {
        // Create departments with specific IDs for sum testing
        $dept1 = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $dept2 = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new DepartmentFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'id');

        $this->assertEquals($dept1->id + $dept2->id, $sum);
    }

    public function test_repository_handles_soft_deleted_departments()
    {
        $model = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the department
        $model->delete();

        // Should not find soft deleted department
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $dept1 = DepartmentModel::factory()->create(['organization_id' => $org1->id]);
        $dept2 = DepartmentModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result1 = $this->repository->fetchFromOrganization($org1->id, $filters, $orderBy);
        $result2 = $this->repository->fetchFromOrganization($org2->id, $filters, $orderBy);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
        $this->assertEquals($dept1->id, $result1['data'][0]->id);
        $this->assertEquals($dept2->id, $result2['data'][0]->id);
    }

    public function test_update_only_affects_organization_departments()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $dept1 = DepartmentModel::factory()->create([
            'organization_id' => $org1->id,
            'name' => 'Original Name'
        ]);
        $dept2 = DepartmentModel::factory()->create([
            'organization_id' => $org2->id,
            'name' => 'Original Name'
        ]);

        $domain = new Department(
            id: $dept1->id,
            organization_id: $org1->id,
            name: 'Updated Name',
            is_active: true
        );

        $this->repository->update($domain, $org1->id);

        // Check that only the department from org1 was updated
        $this->assertDatabaseHas('departments', [
            'id' => $dept1->id,
            'name' => 'Updated Name'
        ]);
        $this->assertDatabaseHas('departments', [
            'id' => $dept2->id,
            'name' => 'Original Name'
        ]);
    }

    public function test_fetch_by_id_includes_users_relationship()
    {
        $model = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $department = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Department::class, $department);
        // The repository uses ->with('users') so the relationship should be loaded
        // This tests that the query includes the relationship even if no users are attached
    }

    public function test_store_sets_id_on_domain_object()
    {
        $domain = new Department(
            id: null,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $this->assertNull($domain->id);

        $result = $this->repository->store($domain);

        $this->assertNotNull($result->id);
        $this->assertEquals($domain->id, $result->id); // Should be the same object
    }
}
