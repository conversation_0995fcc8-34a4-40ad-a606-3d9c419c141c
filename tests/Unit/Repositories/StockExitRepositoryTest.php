<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\StockExitFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Models\StockExit as StockExitModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Shop;
use App\Repositories\StockExitRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StockExitRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private StockExitRepository $repository;
    private StockExitFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(StockExitRepository::class);
        $this->factory = app(StockExitFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test stock exits
        StockExitModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $stockExit) {
            $this->assertInstanceOf(StockExit::class, $stockExit);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_exits()
    {
        $otherOrganization = Organization::factory()->create();

        // Create stock exits for different organizations
        StockExitModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        StockExitModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $stockExit) {
            $this->assertEquals($this->organization->id, $stockExit->organization_id);
        }
    }

    public function test_fetch_by_id_returns_stock_exit()
    {
        $model = StockExitModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $stockExit = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(StockExit::class, $stockExit);
        $this->assertEquals($model->id, $stockExit->id);
        $this->assertEquals($model->quantity, $stockExit->quantity);
        $this->assertEquals($model->value, $stockExit->value);
        $this->assertEquals($model->organization_id, $stockExit->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_stock_exit()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new StockExit(
            id: null,
            organization_id: $this->organization->id,
            shop_id: $shop->id,
            user_id: $user->id,
            brand_id: $product->brand_id,
            product_id: $product->id,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: 1500.50,
            description: 'Store test exit'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);

        $this->assertDatabaseHas('stock_exits', [
            'id' => $result->id,
            'quantity' => 100,
            'value' => 1500.50,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_stock_exit()
    {
        $model = StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Old description'
        ]);

        $domain = new StockExit(
            id: $model->id,
            organization_id: $this->organization->id,
            shop_id: $model->shop_id,
            user_id: $model->user_id,
            brand_id: $model->brand_id,
            product_id: $model->product_id,
            batch_id: $model->batch_id,
            client_id: $model->client_id,
            project_id: $model->project_id,
            quantity: 150,
            value: 2250.75,
            description: 'New description'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(150, $result->quantity);
        $this->assertEquals(2250.75, $result->value);
        $this->assertEquals('New description', $result->description);

        $this->assertDatabaseHas('stock_exits', [
            'id' => $model->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'New description'
        ]);
    }

    public function test_delete_removes_stock_exit()
    {
        $model = StockExitModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('stock_exits', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        StockExitModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create exits for different organization
        $otherOrg = Organization::factory()->create();
        StockExitModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new StockExitFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 100.50
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 200.25
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 150.75
        ]);

        $filters = new StockExitFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(451.50, $sum);
    }

    public function test_repository_handles_soft_deleted_exits()
    {
        $model = StockExitModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the exit
        $model->delete();

        // Should not find soft deleted exit
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $exit1 = StockExitModel::factory()->create(['organization_id' => $org1->id]);
        $exit2 = StockExitModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new StockExitFilters([]);

        // Count should only include exits from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_repository_handles_null_description()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => null
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->description);
    }

    public function test_repository_handles_special_characters()
    {
        $specialDescription = 'Exit with special chars: @#$%^&*()';
        
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => $specialDescription
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($specialDescription, $result['data'][0]->description);
    }

    public function test_repository_handles_unicode_characters()
    {
        $unicodeDescription = 'Salida con caracteres especiales: ñáéíóú 中文';

        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => $unicodeDescription
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($unicodeDescription, $result['data'][0]->description);
    }

    public function test_repository_with_quantity_filtering()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200
        ]);

        $filters = new StockExitFilters(['quantity' => 100]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
    }

    public function test_repository_with_value_filtering()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1500.50
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2500.75
        ]);

        $filters = new StockExitFilters(['value' => 1500.50]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(1500.50, $result['data'][0]->value);
    }

    public function test_repository_with_description_filtering()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => 'Alpha exit'
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => 'Beta exit'
        ]);

        $filters = new StockExitFilters(['description' => 'Alpha']);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha exit', $result['data'][0]->description);
    }

    public function test_repository_with_multiple_filters()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Alpha exit'
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200,
            'value' => 1500.50,
            'description' => 'Beta exit'
        ]);

        $filters = new StockExitFilters([
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Alpha'
        ]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
        $this->assertEquals(1500.50, $result['data'][0]->value);
        $this->assertEquals('Alpha exit', $result['data'][0]->description);
    }

    public function test_repository_ordering_by_quantity()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 300
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('quantity', 'asc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
        $this->assertEquals(300, $result['data'][1]->quantity);
    }

    public function test_repository_ordering_by_value()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2500.75
        ]);
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1500.50
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('value', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2500.75, $result['data'][0]->value);
        $this->assertEquals(1500.50, $result['data'][1]->value);
    }

    public function test_repository_with_zero_quantity()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_with_high_quantity()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_with_decimal_values()
    {
        StockExitModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 123.456789
        ]);

        $filters = new StockExitFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }
}
