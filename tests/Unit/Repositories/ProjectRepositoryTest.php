<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProjectFilters;
use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Models\Budget;
use App\Models\Client;
use App\Models\CustomProduct;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Project as ProjectModel;
use App\Repositories\ProjectRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProjectRepository $repository;
    private ProjectFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ProjectRepository::class);
        $this->factory = app(ProjectFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test projects
        ProjectModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ProjectFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $project) {
            $this->assertInstanceOf(Project::class, $project);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create projects with different names
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Project'
        ]);
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Project'
        ]);

        $filters = new ProjectFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Project', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more projects than the limit
        ProjectModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ProjectFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_projects()
    {
        $otherOrganization = Organization::factory()->create();

        // Create projects for different organizations
        ProjectModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        ProjectModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new ProjectFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $project) {
            $this->assertEquals($this->organization->id, $project->organization_id);
        }
    }

    public function test_fetch_by_id_returns_project()
    {
        $model = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $project = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Project::class, $project);
        $this->assertEquals($model->id, $project->id);
        $this->assertEquals($model->name, $project->name);
        $this->assertEquals($model->organization_id, $project->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_project()
    {
        $domain = new Project(
            id: null,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($domain->name, $result->name);
        $this->assertEquals($domain->organization_id, $result->organization_id);

        // Verify it was actually saved to database
        $this->assertDatabaseHas('projects', [
            'id' => $result->id,
            'name' => 'Test Project',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_project()
    {
        $model = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $domain = new Project(
            id: $model->id,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Updated Name',
            description: 'Updated description',
            value: 15000.75,
            cost: 12000.50
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);

        // Verify it was actually updated in database
        $this->assertDatabaseHas('projects', [
            'id' => $model->id,
            'name' => 'Updated Name',
            'description' => 'Updated description'
        ]);
    }

    public function test_delete_removes_project()
    {
        $model = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('projects', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        ProjectModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create projects for different organization
        $otherOrg = Organization::factory()->create();
        ProjectModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new ProjectFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1000.00
        ]);
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2000.00
        ]);
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 3000.00
        ]);

        $filters = new ProjectFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(6000.00, $sum);
    }

    public function test_attach_products()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        $domain = $this->factory->buildFromModel($project);
        $attachProducts = new AttachProductsDomain([
            $product1->id => ['quantity' => 5, 'value' => 100.00, 'description' => 'Product 1'],
            $product2->id => ['quantity' => 3, 'value' => 200.00, 'description' => 'Product 2']
        ]);

        $this->repository->attachProducts($domain, $attachProducts);

        // Verify products were attached
        $this->assertDatabaseHas('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product1->id,
            'quantity' => 5,
            'value' => 100.00
        ]);
        $this->assertDatabaseHas('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product2->id,
            'quantity' => 3,
            'value' => 200.00
        ]);
    }

    public function test_clear_products()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        // Attach a product first
        $project->products()->attach($product->id, [
            'quantity' => 5,
            'value' => 100.00,
            'description' => 'Test product'
        ]);

        $domain = $this->factory->buildFromModel($project);
        $this->repository->clearProducts($domain);

        // Verify products were cleared
        $this->assertDatabaseMissing('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product->id
        ]);
    }

    public function test_attach_custom_products()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($project);
        $attachCustomProducts = new AttachCustomDomain([
            ['name' => 'Custom Product 1', 'description' => 'Description 1', 'quantity' => 2, 'value' => 150.00],
            ['name' => 'Custom Product 2', 'description' => 'Description 2', 'quantity' => 1, 'value' => 300.00]
        ]);

        $this->repository->attachCustomProducts($domain, $attachCustomProducts);

        // Verify custom products were attached
        $this->assertDatabaseHas('custom_products', [
            'project_id' => $project->id,
            'name' => 'Custom Product 1',
            'quantity' => 2,
            'value' => 150.00
        ]);
        $this->assertDatabaseHas('custom_products', [
            'project_id' => $project->id,
            'name' => 'Custom Product 2',
            'quantity' => 1,
            'value' => 300.00
        ]);
    }

    public function test_clear_custom_products()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Create custom products first
        CustomProduct::factory()->create([
            'project_id' => $project->id,
            'name' => 'Test Custom Product'
        ]);

        $domain = $this->factory->buildFromModel($project);
        $this->repository->clearCustomProducts($domain);

        // Verify custom products were cleared
        $this->assertDatabaseMissing('custom_products', [
            'project_id' => $project->id,
            'name' => 'Test Custom Product'
        ]);
    }

    public function test_repository_handles_soft_deleted_projects()
    {
        $model = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the project
        $model->delete();

        // Should not find soft deleted project
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $project1 = ProjectModel::factory()->create(['organization_id' => $org1->id]);
        $project2 = ProjectModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new ProjectFilters([]);

        // Count should only include projects from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_fetch_all_with_client_and_budget_relationships()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id,
            'budget_id' => $budget->id
        ]);

        $filters = new ProjectFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true, true);

        $this->assertEquals(1, $result['count']);
        $project = $result['data'][0];
        $this->assertNotNull($project->client);
        $this->assertNotNull($project->budget);
        $this->assertEquals($client->name, $project->client->name);
        $this->assertEquals($budget->name, $project->budget->name);
    }

    public function test_fetch_all_with_products_and_custom_products()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        // Attach product and custom product
        $project->products()->attach($product->id, [
            'quantity' => 5,
            'value' => 100.00,
            'description' => 'Test product'
        ]);
        CustomProduct::factory()->create([
            'project_id' => $project->id,
            'name' => 'Custom Product'
        ]);

        $filters = new ProjectFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, false, false, true, true);

        $this->assertEquals(1, $result['count']);
        $projectDomain = $result['data'][0];
        $this->assertNotNull($projectDomain->products);
        $this->assertNotNull($projectDomain->customProducts);
        $this->assertCount(1, $projectDomain->products);
        $this->assertCount(1, $projectDomain->customProducts);
    }

    public function test_sum_with_filters()
    {
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Project',
            'value' => 1000.00
        ]);
        ProjectModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Project',
            'value' => 2000.00
        ]);

        $filters = new ProjectFilters(['name' => 'Alpha']);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(1000.00, $sum);
    }

    public function test_attach_products_with_empty_domain()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($project);
        $attachProducts = new AttachProductsDomain([]);

        // Should not throw exception with empty products
        $this->repository->attachProducts($domain, $attachProducts);

        // No products should be attached
        $this->assertDatabaseMissing('projects_products', [
            'project_id' => $project->id
        ]);
    }

    public function test_attach_custom_products_with_empty_domain()
    {
        $project = ProjectModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($project);
        $attachCustomProducts = new AttachCustomDomain([]);

        // Should not throw exception with empty custom products
        $this->repository->attachCustomProducts($domain, $attachCustomProducts);

        // No custom products should be attached
        $this->assertDatabaseMissing('custom_products', [
            'project_id' => $project->id
        ]);
    }
}
