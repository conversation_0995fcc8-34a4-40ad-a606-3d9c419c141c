<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\BudgetProduct;
use App\Factories\Inventory\BudgetProductFactory;
use App\Models\BudgetProduct as BudgetProductModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Budget;
use App\Repositories\BudgetProductRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BudgetProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private BudgetProductRepository $repository;
    private BudgetProductFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(BudgetProductRepository::class);
        $this->factory = app(BudgetProductFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test budget products
        BudgetProductModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $budgetProduct) {
            $this->assertInstanceOf(BudgetProduct::class, $budgetProduct);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_budget_products()
    {
        $otherOrganization = Organization::factory()->create();

        // Create budget products for different organizations
        BudgetProductModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        BudgetProductModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $budgetProduct) {
            $this->assertEquals($this->organization->id, $budgetProduct->organization_id);
        }
    }

    public function test_fetch_from_budget_returns_budget_specific_products()
    {
        $budget1 = Budget::factory()->create(['organization_id' => $this->organization->id]);
        $budget2 = Budget::factory()->create(['organization_id' => $this->organization->id]);

        // Create budget products for different budgets
        BudgetProductModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $budget1->id
        ]);
        BudgetProductModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $budget2->id
        ]);

        $result = $this->repository->fetchFromBudget($budget1->id);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        foreach ($result as $budgetProduct) {
            $this->assertInstanceOf(BudgetProduct::class, $budgetProduct);
            $this->assertEquals($budget1->id, $budgetProduct->budget_id);
        }
    }

    public function test_fetch_by_id_returns_budget_product()
    {
        $model = BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $budgetProduct = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(BudgetProduct::class, $budgetProduct);
        $this->assertEquals($model->id, $budgetProduct->id);
        $this->assertEquals($model->quantity, $budgetProduct->quantity);
        $this->assertEquals($model->value, $budgetProduct->value);
        $this->assertEquals($model->organization_id, $budgetProduct->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_budget_product()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: $budget->id,
            product_id: $product->id,
            quantity: 10,
            value: 150.50
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);

        $this->assertDatabaseHas('budget_products', [
            'id' => $result->id,
            'quantity' => 10,
            'value' => 150.50,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_budget_product()
    {
        $model = BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $domain = new BudgetProduct(
            id: $model->id,
            organization_id: $this->organization->id,
            budget_id: $model->budget_id,
            product_id: $model->product_id,
            quantity: 15,
            value: 225.75
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);

        $this->assertDatabaseHas('budget_products', [
            'id' => $model->id,
            'quantity' => 15,
            'value' => 225.75
        ]);
    }

    public function test_delete_removes_budget_product()
    {
        $model = BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('budget_products', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_soft_deleted_budget_products()
    {
        $model = BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the budget product
        $model->delete();

        // Should not find soft deleted budget product
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $budgetProduct1 = BudgetProductModel::factory()->create(['organization_id' => $org1->id]);
        $budgetProduct2 = BudgetProductModel::factory()->create(['organization_id' => $org2->id]);

        // Fetch should only include budget products from specific organization
        $result1 = $this->repository->fetchFromOrganization($org1->id);
        $result2 = $this->repository->fetchFromOrganization($org2->id);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
    }

    public function test_repository_handles_zero_quantity()
    {
        BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_handles_high_quantity()
    {
        BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_handles_decimal_values()
    {
        BudgetProductModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 123.456789
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }

    public function test_repository_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            BudgetProductModel::factory()->create([
                'organization_id' => $this->organization->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(4, $result['count']);

        foreach ($result['data'] as $index => $budgetProduct) {
            $this->assertEquals($testCases[$index]['quantity'], $budgetProduct->quantity);
            $this->assertEquals($testCases[$index]['value'], $budgetProduct->value);
        }
    }

    public function test_fetch_from_budget_with_empty_budget()
    {
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        $result = $this->repository->fetchFromBudget($budget->id);

        $this->assertIsArray($result);
        $this->assertCount(0, $result);
    }

    public function test_fetch_from_budget_with_multiple_products()
    {
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        BudgetProductModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $budget->id
        ]);

        $result = $this->repository->fetchFromBudget($budget->id);

        $this->assertIsArray($result);
        $this->assertCount(5, $result);

        foreach ($result as $budgetProduct) {
            $this->assertInstanceOf(BudgetProduct::class, $budgetProduct);
            $this->assertEquals($budget->id, $budgetProduct->budget_id);
        }
    }
}
