<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\BudgetFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Budget;
use App\Factories\Inventory\BudgetFactory;
use App\Models\Budget as BudgetModel;
use App\Models\BudgetCustomProduct;
use App\Models\BudgetProduct;
use App\Models\Client;
use App\Models\CustomProduct;
use App\Models\Organization;
use App\Models\Product;
use App\Repositories\BudgetRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BudgetRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private BudgetRepository $repository;
    private BudgetFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(BudgetRepository::class);
        $this->factory = app(BudgetFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test budgets
        BudgetModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $budget) {
            $this->assertInstanceOf(Budget::class, $budget);
        }
    }

    public function test_fetch_all_with_filters()
    {
        $client1 = Client::factory()->create(['organization_id' => $this->organization->id]);
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        // Create budgets with different clients
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client1->id,
            'name' => 'Alpha Budget'
        ]);
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client2->id,
            'name' => 'Beta Budget'
        ]);

        $filters = new BudgetFilters(['client_id' => $client1->id]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($client1->id, $result['data'][0]->client_id);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more budgets than the limit
        BudgetModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_budgets()
    {
        $otherOrganization = Organization::factory()->create();

        // Create budgets for different organizations
        BudgetModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        BudgetModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $budget) {
            $this->assertEquals($this->organization->id, $budget->organization_id);
        }
    }

    public function test_fetch_by_id_returns_budget()
    {
        $model = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $budget = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Budget::class, $budget);
        $this->assertEquals($model->id, $budget->id);
        $this->assertEquals($model->name, $budget->name);
        $this->assertEquals($model->organization_id, $budget->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_delete_removes_budget()
    {
        $model = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('budgets', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        BudgetModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create budgets for different organization
        $otherOrg = Organization::factory()->create();
        BudgetModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new BudgetFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1000.00
        ]);
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2000.00
        ]);
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 3000.00
        ]);

        $filters = new BudgetFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(6000.00, $sum);
    }

    public function test_add_product_to_budget()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $result = $this->repository->addProductToBudget($budget->id, $product->id, 10, 1000.00);

        $this->assertTrue($result);

        $this->assertDatabaseHas('budget_products', [
            'budget_id' => $budget->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'value' => 1000.00
        ]);
    }

    public function test_remove_product_from_budget()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        
        $budgetProduct = BudgetProduct::factory()->create([
            'budget_id' => $budget->id,
            'product_id' => $product->id
        ]);

        $result = $this->repository->removeProductFromBudget($budget->id, $product->id);

        $this->assertTrue($result);

        $this->assertDatabaseMissing('budget_products', [
            'budget_id' => $budget->id,
            'product_id' => $product->id
        ]);
    }

    public function test_add_custom_product_to_budget()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $customProduct = CustomProduct::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $result = $this->repository->addCustomProductToBudget($budget->id, $customProduct->id, 5, 500.00);

        $this->assertTrue($result);

        $this->assertDatabaseHas('budget_custom_products', [
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id,
            'quantity' => 5,
            'value' => 500.00
        ]);
    }

    public function test_remove_custom_product_from_budget()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $customProduct = CustomProduct::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        
        $budgetCustomProduct = BudgetCustomProduct::factory()->create([
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id
        ]);

        $result = $this->repository->removeCustomProductFromBudget($budget->id, $customProduct->id);

        $this->assertTrue($result);

        $this->assertDatabaseMissing('budget_custom_products', [
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id
        ]);
    }

    public function test_repository_handles_soft_deleted_budgets()
    {
        $model = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the budget
        $model->delete();

        // Should not find soft deleted budget
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $budget1 = BudgetModel::factory()->create(['organization_id' => $org1->id]);
        $budget2 = BudgetModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new BudgetFilters([]);

        // Count should only include budgets from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_fetch_all_with_client_relationship()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);

        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true);

        $this->assertEquals(1, $result['count']);
        $budget = $result['data'][0];
        $this->assertNotNull($budget->client);
        $this->assertEquals($client->name, $budget->client->name);
    }

    public function test_fetch_all_without_client_relationship()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);

        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, false);

        $this->assertEquals(1, $result['count']);
        $budget = $result['data'][0];
        $this->assertNull($budget->client);
    }

    public function test_sum_with_filters()
    {
        $client1 = Client::factory()->create(['organization_id' => $this->organization->id]);
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client1->id,
            'value' => 1000.00
        ]);
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client2->id,
            'value' => 2000.00
        ]);

        $filters = new BudgetFilters(['client_id' => $client1->id]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(1000.00, $sum);
    }

    public function test_repository_handles_different_value_ranges()
    {
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 0.0
        ]);
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 999999.99
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'value', 'by' => 'asc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(0.0, $result['data'][0]->value);
        $this->assertEquals(999999.99, $result['data'][1]->value);
    }

    public function test_repository_handles_null_client()
    {
        BudgetModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => null
        ]);

        $filters = new BudgetFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->client_id);
    }

    public function test_product_operations_with_different_quantities()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Test zero quantity
        $result = $this->repository->addProductToBudget($budget->id, $product->id, 0, 0.00);
        $this->assertTrue($result);

        // Test high quantity
        $this->repository->removeProductFromBudget($budget->id, $product->id);
        $result = $this->repository->addProductToBudget($budget->id, $product->id, 999999, 9999999.99);
        $this->assertTrue($result);

        $this->assertDatabaseHas('budget_products', [
            'budget_id' => $budget->id,
            'product_id' => $product->id,
            'quantity' => 999999,
            'value' => 9999999.99
        ]);
    }

    public function test_custom_product_operations_with_different_quantities()
    {
        $budget = BudgetModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $customProduct = CustomProduct::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Test zero quantity
        $result = $this->repository->addCustomProductToBudget($budget->id, $customProduct->id, 0, 0.00);
        $this->assertTrue($result);

        // Test high quantity
        $this->repository->removeCustomProductFromBudget($budget->id, $customProduct->id);
        $result = $this->repository->addCustomProductToBudget($budget->id, $customProduct->id, 999999, 9999999.99);
        $this->assertTrue($result);

        $this->assertDatabaseHas('budget_custom_products', [
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id,
            'quantity' => 999999,
            'value' => 9999999.99
        ]);
    }
}
