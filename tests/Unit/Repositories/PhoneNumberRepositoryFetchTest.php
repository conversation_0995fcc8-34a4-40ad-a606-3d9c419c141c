<?php

namespace Tests\Unit\Repositories;

use Tests\TestCase;
use App\Repositories\PhoneNumberRepository;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Models\PhoneNumber;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PhoneNumberRepositoryFetchTest extends TestCase
{
    use RefreshDatabase;

    private PhoneNumberRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new PhoneNumberRepository(new PhoneNumberFactory());
    }

    public function test_fetch_by_whatsapp_phone_number_id_returns_phone_number_with_active_organization()
    {
        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'test_phone_id_123',
            'is_active' => true,
        ]);

        $result = $this->repository->fetchByWhatsAppPhoneNumberId('test_phone_id_123');

        $this->assertNotNull($result);
        $this->assertEquals($phoneNumber->id, $result->id);
        $this->assertEquals($phoneNumber->whatsapp_phone_number_id, $result->whatsapp_phone_number_id);
        $this->assertNotNull($result->organization);
        $this->assertEquals($organization->id, $result->organization->id);
        $this->assertEquals($organization->name, $result->organization->name);
    }

    public function test_fetch_by_whatsapp_phone_number_id_returns_null_when_phone_number_not_found()
    {
        $result = $this->repository->fetchByWhatsAppPhoneNumberId('non_existent_phone_id');

        $this->assertNull($result);
    }

    public function test_fetch_by_whatsapp_phone_number_id_returns_null_when_phone_number_is_inactive()
    {
        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'inactive_phone_id',
            'is_active' => false,
        ]);

        $result = $this->repository->fetchByWhatsAppPhoneNumberId('inactive_phone_id');

        $this->assertNull($result);
    }

    public function test_fetch_by_whatsapp_phone_number_id_returns_null_when_organization_is_inactive()
    {
        $organization = Organization::factory()->create([
            'is_active' => false,
            'is_suspended' => false,
        ]);

        PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'phone_with_inactive_org',
            'is_active' => true,
        ]);

        $result = $this->repository->fetchByWhatsAppPhoneNumberId('phone_with_inactive_org');

        $this->assertNotNull($result);
        $this->assertNull($result->organization);
    }

    public function test_fetch_by_whatsapp_phone_number_id_returns_null_when_organization_is_suspended()
    {
        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => true,
        ]);

        PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'phone_with_suspended_org',
            'is_active' => true,
        ]);

        $result = $this->repository->fetchByWhatsAppPhoneNumberId('phone_with_suspended_org');

        $this->assertNotNull($result);
        $this->assertNull($result->organization);
    }

    public function test_fetch_by_whatsapp_phone_number_id_with_valid_organization_loads_relationship()
    {
        $organization = Organization::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'test_relationship_phone',
            'is_active' => true,
        ]);

        $result = $this->repository->fetchByWhatsAppPhoneNumberId('test_relationship_phone');

        $this->assertNotNull($result);
        $this->assertNotNull($result->organization);
        $this->assertEquals('Test Organization', $result->organization->name);
        $this->assertEquals('Test Description', $result->organization->description);
        $this->assertTrue($result->organization->is_active);
        $this->assertFalse($result->organization->is_suspended);
    }
}
