<?php

namespace Tests\Unit\Repositories;

use Tests\TestCase;
use App\Repositories\OrganizationRepository;
use App\Factories\OrganizationFactory;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrganizationRepositoryWebhookTest extends TestCase
{
    use RefreshDatabase;

    private OrganizationRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new OrganizationRepository(new OrganizationFactory());
    }

    public function test_fetch_by_webhook_token_returns_organization_with_valid_token()
    {
        $token = 'test_webhook_token_123';
        
        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNotNull($result);
        $this->assertEquals($organization->id, $result->id);
        $this->assertEquals($organization->name, $result->name);
        $this->assertEquals($token, $result->whatsapp_webhook_verify_token);
        $this->assertTrue($result->is_active);
        $this->assertFalse($result->is_suspended);
    }

    public function test_fetch_by_webhook_token_returns_null_when_token_not_found()
    {
        $result = $this->repository->fetchByWebhookToken('non_existent_token');

        $this->assertNull($result);
    }

    public function test_fetch_by_webhook_token_returns_null_when_organization_is_inactive()
    {
        $token = 'inactive_org_token';
        
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => false,
            'is_suspended' => false,
        ]);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNull($result);
    }

    public function test_fetch_by_webhook_token_returns_null_when_organization_is_suspended()
    {
        $token = 'suspended_org_token';
        
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => true,
        ]);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNull($result);
    }

    public function test_fetch_by_webhook_token_returns_null_when_organization_is_inactive_and_suspended()
    {
        $token = 'inactive_suspended_org_token';
        
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => false,
            'is_suspended' => true,
        ]);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNull($result);
    }

    public function test_fetch_by_webhook_token_returns_correct_organization_when_multiple_exist()
    {
        $token = 'unique_token_123';
        
        // Create inactive organization with same token
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => false,
            'is_suspended' => false,
        ]);

        // Create active organization with same token
        $activeOrg = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNotNull($result);
        $this->assertEquals($activeOrg->id, $result->id);
        $this->assertTrue($result->is_active);
        $this->assertFalse($result->is_suspended);
    }

    public function test_fetch_by_webhook_token_returns_null_when_token_is_null()
    {
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => null,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $result = $this->repository->fetchByWebhookToken('any_token');

        $this->assertNull($result);
    }

    public function test_fetch_by_webhook_token_is_case_sensitive()
    {
        $token = 'CaseSensitiveToken123';
        
        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $result = $this->repository->fetchByWebhookToken('casesensitivetoken123');

        $this->assertNull($result);

        $result = $this->repository->fetchByWebhookToken($token);

        $this->assertNotNull($result);
    }
}
