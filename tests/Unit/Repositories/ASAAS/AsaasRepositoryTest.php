<?php

namespace Tests\Unit\Repositories\ASAAS;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Repositories\ASAAS\AsaasRepository;
use App\Factories\ASAAS\OrganizationFactory;
use App\Factories\ASAAS\ClientFactory;
use App\Factories\ASAAS\SaleFactory;
use App\Factories\UserFactory;
use App\Factories\OrganizationFactory as BaseOrganizationFactory;
use App\Factories\Inventory\ShopFactory;
use App\Models\Organization as OrganizationModel;
use App\Models\Client as ClientModel;
use App\Models\Sale as SaleModel;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasSale;
use App\Enums\AsaasEnvironment;
use App\Enums\SubscriptionStatus;
use App\Enums\PaymentStatus;

class AsaasRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private AsaasRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = new AsaasRepository(
            new OrganizationFactory(),
            new ClientFactory(),
            new SaleFactory(
                new UserFactory(new BaseOrganizationFactory()),
                new ShopFactory(),
                new ClientFactory()
            )
        );
    }

    public function test_find_organization_with_full_integration()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $asaasModel = AsaasOrganization::factory()->create([
            'organization_id' => $organizationModel->id,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);

        $organization = $this->repository->findOrganizationWithFullIntegration($organizationModel->id);

        $this->assertNotNull($organization);
        $this->assertTrue($organization->hasAsaasIntegration());
        $this->assertEquals($organizationModel->id, $organization->id);
    }

    public function test_find_organization_by_id()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $asaasModel = AsaasOrganization::factory()->create([
            'organization_id' => $organizationModel->id,
        ]);

        $organization = $this->repository->findOrganizationById($organizationModel->id);

        $this->assertNotNull($organization);
        $this->assertTrue($organization->hasAsaasIntegration());
    }

    public function test_find_organizations_with_asaas_integration()
    {
        // Organization with ASAAS
        $org1 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create(['organization_id' => $org1->id]);

        // Organization without ASAAS
        $org2 = OrganizationModel::factory()->create();

        $organizations = $this->repository->findOrganizationsWithAsaasIntegration();

        $this->assertCount(1, $organizations);
        $this->assertEquals($org1->id, $organizations[0]->id);
        $this->assertTrue($organizations[0]->hasAsaasIntegration());
    }

    public function test_find_organizations_by_subscription_status()
    {
        $org1 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org1->id,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);

        $org2 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org2->id,
            'subscription_status' => SubscriptionStatus::INACTIVE,
        ]);

        $activeOrgs = $this->repository->findOrganizationsBySubscriptionStatus(SubscriptionStatus::ACTIVE);
        $inactiveOrgs = $this->repository->findOrganizationsBySubscriptionStatus(SubscriptionStatus::INACTIVE);

        $this->assertCount(1, $activeOrgs);
        $this->assertCount(1, $inactiveOrgs);
        $this->assertEquals($org1->id, $activeOrgs[0]->id);
        $this->assertEquals($org2->id, $inactiveOrgs[0]->id);
    }

    public function test_find_organizations_with_active_subscriptions()
    {
        $org1 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org1->id,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);

        $org2 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org2->id,
            'subscription_status' => SubscriptionStatus::INACTIVE,
        ]);

        $organizations = $this->repository->findOrganizationsWithActiveSubscriptions();

        $this->assertCount(1, $organizations);
        $this->assertEquals($org1->id, $organizations[0]->id);
    }

    public function test_find_organizations_in_courtesy()
    {
        $org1 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org1->id,
            'is_courtesy' => true,
            'courtesy_expires_at' => now()->addDays(7),
        ]);

        $org2 = OrganizationModel::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $org2->id,
            'is_courtesy' => false,
        ]);

        $organizations = $this->repository->findOrganizationsInCourtesy();

        $this->assertCount(1, $organizations);
        $this->assertEquals($org1->id, $organizations[0]->id);
        $this->assertTrue($organizations[0]->isInCourtesy());
    }

    public function test_find_clients_by_organization()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $client1 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);
        $client2 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);
        
        // Client from different organization
        $otherOrg = OrganizationModel::factory()->create();
        $client3 = ClientModel::factory()->create(['organization_id' => $otherOrg->id]);

        $clients = $this->repository->findClientsByOrganization($organizationModel->id);

        $this->assertCount(2, $clients);
        $clientIds = array_map(fn($client) => $client->id, $clients);
        $this->assertContains($client1->id, $clientIds);
        $this->assertContains($client2->id, $clientIds);
        $this->assertNotContains($client3->id, $clientIds);
    }

    public function test_find_clients_without_asaas_integration()
    {
        $organizationModel = OrganizationModel::factory()->create();
        
        // Client with ASAAS
        $client1 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);
        AsaasClient::factory()->create(['client_id' => $client1->id]);
        
        // Client without ASAAS
        $client2 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);

        $clients = $this->repository->findClientsWithoutAsaasIntegration($organizationModel->id);

        $this->assertCount(1, $clients);
        $this->assertEquals($client2->id, $clients[0]->id);
        $this->assertFalse($clients[0]->hasAsaasIntegration());
    }

    public function test_find_sales_by_payment_status()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $clientModel = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);
        
        $sale1 = SaleModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
        ]);
        AsaasSale::factory()->create([
            'sale_id' => $sale1->id,
            'payment_status' => PaymentStatus::RECEIVED,
        ]);

        $sale2 = SaleModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
        ]);
        AsaasSale::factory()->create([
            'sale_id' => $sale2->id,
            'payment_status' => PaymentStatus::PENDING,
        ]);

        $paidSales = $this->repository->findSalesByPaymentStatus(PaymentStatus::RECEIVED, $organizationModel->id);
        $pendingSales = $this->repository->findSalesByPaymentStatus(PaymentStatus::PENDING, $organizationModel->id);

        $this->assertCount(1, $paidSales);
        $this->assertCount(1, $pendingSales);
        $this->assertEquals($sale1->id, $paidSales[0]->id);
        $this->assertEquals($sale2->id, $pendingSales[0]->id);
    }

    public function test_get_integration_summary()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $asaasOrg = AsaasOrganization::factory()->create([
            'organization_id' => $organizationModel->id,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);

        // Create clients
        $client1 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);
        AsaasClient::factory()->create(['client_id' => $client1->id]);
        
        $client2 = ClientModel::factory()->create(['organization_id' => $organizationModel->id]);

        // Create sales
        $sale1 = SaleModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $client1->id,
        ]);
        AsaasSale::factory()->create(['sale_id' => $sale1->id]);

        $summary = $this->repository->getIntegrationSummary($organizationModel->id);

        $this->assertArrayHasKey('organization', $summary);
        $this->assertArrayHasKey('clients', $summary);
        $this->assertArrayHasKey('sales', $summary);
        $this->assertArrayHasKey('integration_health', $summary);

        $this->assertEquals(2, $summary['clients']['total']);
        $this->assertEquals(1, $summary['clients']['with_asaas']);
        $this->assertEquals(1, $summary['clients']['without_asaas']);

        $this->assertEquals(1, $summary['sales']['total']);
        $this->assertEquals(1, $summary['sales']['with_asaas']);
        $this->assertEquals(0, $summary['sales']['without_asaas']);
    }
}
