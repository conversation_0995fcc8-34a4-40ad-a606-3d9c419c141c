<?php

namespace Tests\Unit\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Repositories\CampaignRepository;
use App\Factories\ChatBot\CampaignFactory;
use App\Domains\ChatBot\Campaign;
use App\Models\Campaign as CampaignModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Template;
use App\Models\PhoneNumber;
use App\Enums\CampaignStatus;
use App\Domains\Filters\CampaignFilters;
use App\Domains\Filters\OrderBy;

class CampaignRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private CampaignRepository $repository;
    private CampaignFactory $factory;
    private Organization $organization;
    private User $user;
    private Template $template;
    private PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->template = Template::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create(['organization_id' => $this->organization->id]);
        
        $this->factory = $this->createMock(CampaignFactory::class);
        $this->repository = new CampaignRepository($this->factory);
    }

    public function test_store_creates_new_campaign()
    {
        // Arrange
        $campaign = new Campaign(
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            template_id: $this->template->id,
            phone_number_id: $this->phoneNumber->id,
            name: 'Test Campaign',
            description: 'Test Description'
        );

        $expectedModel = new CampaignModel([
            'id' => 1,
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'name' => 'Test Campaign'
        ]);

        $this->factory->expects($this->once())
                     ->method('buildFromModel')
                     ->willReturn($campaign);

        // Act
        $result = $this->repository->store($campaign);

        // Assert
        $this->assertInstanceOf(Campaign::class, $result);
        $this->assertDatabaseHas('campaigns', [
            'name' => 'Test Campaign',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_fetch_by_id_returns_campaign()
    {
        // Arrange
        $model = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        $expectedCampaign = new Campaign(
            id: $model->id,
            organization_id: $model->organization_id,
            name: $model->name
        );

        $this->factory->expects($this->once())
                     ->method('buildFromModel')
                     ->with($this->callback(function($arg) use ($model) {
                         return $arg->id === $model->id;
                     }))
                     ->willReturn($expectedCampaign);

        // Act
        $result = $this->repository->fetchById($model->id);

        // Assert
        $this->assertInstanceOf(Campaign::class, $result);
        $this->assertEquals($model->id, $result->id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        // Act & Assert
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById(999);
    }

    public function test_update_modifies_existing_campaign()
    {
        // Arrange
        $model = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $campaign = new Campaign(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            description: 'Updated Description'
        );

        $this->factory->method('buildFromModel')->willReturn($campaign);

        // Act
        $result = $this->repository->update($campaign, $this->organization->id);

        // Assert
        $this->assertInstanceOf(Campaign::class, $result);
        $this->assertDatabaseHas('campaigns', [
            'id' => $model->id,
            'name' => 'Updated Name',
            'description' => 'Updated Description'
        ]);
    }

    public function test_update_validates_organization_access()
    {
        // Arrange
        $otherOrganization = Organization::factory()->create();
        $model = CampaignModel::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $campaign = new Campaign(
            id: $model->id,
            organization_id: $otherOrganization->id,
            name: 'Updated Name'
        );

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign not found or access denied');

        $this->repository->update($campaign, $this->organization->id);
    }

    public function test_delete_removes_campaign()
    {
        // Arrange
        $model = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $campaign = new Campaign(
            id: $model->id,
            organization_id: $this->organization->id
        );

        // Act
        $result = $this->repository->delete($campaign);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('campaigns', ['id' => $model->id]);
    }

    public function test_get_all_applies_filters_correctly()
    {
        // Arrange
        $draftCampaign = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::DRAFT,
            'name' => 'Draft Campaign'
        ]);

        $sentCampaign = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::COMPLETED,
            'name' => 'Sent Campaign'
        ]);

        $filters = new CampaignFilters(
            organization_id: $this->organization->id,
            status: CampaignStatus::DRAFT
        );

        $orderBy = new OrderBy('name', 'asc');

        $this->factory->method('buildCollection')
                     ->willReturn([new Campaign(id: $draftCampaign->id)]);

        // Act
        $result = $this->repository->getAll($filters, $orderBy);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
    }

    public function test_fetch_by_status_returns_filtered_campaigns()
    {
        // Arrange
        CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::DRAFT
        ]);

        CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        $this->factory->method('buildCollection')
                     ->willReturn([new Campaign(id: 1)]);

        // Act
        $result = $this->repository->fetchByStatus(CampaignStatus::DRAFT, $this->organization->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
    }

    public function test_count_by_status_returns_correct_counts()
    {
        // Arrange
        CampaignModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::DRAFT
        ]);

        CampaignModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        // Act
        $result = $this->repository->countByStatus($this->organization->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(2, $result[CampaignStatus::DRAFT->value]);
        $this->assertEquals(3, $result[CampaignStatus::COMPLETED->value]);
    }

    public function test_fetch_full_by_id_loads_relationships()
    {
        // Arrange
        $model = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'template_id' => $this->template->id
        ]);

        $expectedCampaign = new Campaign(
            id: $model->id,
            organization_id: $model->organization_id
        );

        $this->factory->expects($this->once())
                     ->method('buildFromModel')
                     ->with(
                         $this->callback(function($arg) use ($model) {
                             return $arg->id === $model->id;
                         }),
                         true, // loadTemplate
                         true, // loadMessages
                         true  // loadClients
                     )
                     ->willReturn($expectedCampaign);

        // Act
        $result = $this->repository->fetchFullById($model->id);

        // Assert
        $this->assertInstanceOf(Campaign::class, $result);
    }

    public function test_get_recent_campaigns_filters_by_date()
    {
        // Arrange
        $recentCampaign = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'created_at' => now()->subDays(5)
        ]);

        $oldCampaign = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id,
            'created_at' => now()->subDays(35)
        ]);

        $this->factory->method('buildCollection')
                     ->willReturn([new Campaign(id: $recentCampaign->id)]);

        // Act
        $result = $this->repository->getRecentCampaigns($this->organization->id, 30);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
    }

    public function test_save_handles_both_create_and_update()
    {
        // Test create (no ID)
        $newCampaign = new Campaign(
            organization_id: $this->organization->id,
            name: 'New Campaign'
        );

        $this->factory->method('buildFromModel')->willReturn($newCampaign);

        $result1 = $this->repository->save($newCampaign, $this->organization->id);
        $this->assertInstanceOf(Campaign::class, $result1);

        // Test update (with ID)
        $existingModel = CampaignModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $existingCampaign = new Campaign(
            id: $existingModel->id,
            organization_id: $this->organization->id,
            name: 'Updated Campaign'
        );

        $result2 = $this->repository->save($existingCampaign, $this->organization->id);
        $this->assertInstanceOf(Campaign::class, $result2);
    }
}
