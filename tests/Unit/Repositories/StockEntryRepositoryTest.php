<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\StockEntryFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Models\StockEntry as StockEntryModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Shop;
use App\Repositories\StockEntryRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StockEntryRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private StockEntryRepository $repository;
    private StockEntryFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(StockEntryRepository::class);
        $this->factory = app(StockEntryFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test stock entries
        StockEntryModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $stockEntry) {
            $this->assertInstanceOf(StockEntry::class, $stockEntry);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_entries()
    {
        $otherOrganization = Organization::factory()->create();

        // Create stock entries for different organizations
        StockEntryModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        StockEntryModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $stockEntry) {
            $this->assertEquals($this->organization->id, $stockEntry->organization_id);
        }
    }

    public function test_fetch_by_id_returns_stock_entry()
    {
        $model = StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $stockEntry = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(StockEntry::class, $stockEntry);
        $this->assertEquals($model->id, $stockEntry->id);
        $this->assertEquals($model->quantity, $stockEntry->quantity);
        $this->assertEquals($model->value, $stockEntry->value);
        $this->assertEquals($model->organization_id, $stockEntry->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_stock_entry()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new StockEntry(
            id: null,
            organization_id: $this->organization->id,
            shop_id: $shop->id,
            user_id: $user->id,
            brand_id: $product->brand_id,
            product_id: $product->id,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: 1500.50,
            description: 'Store test entry'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);

        $this->assertDatabaseHas('stock_entries', [
            'id' => $result->id,
            'quantity' => 100,
            'value' => 1500.50,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_stock_entry()
    {
        $model = StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Old description'
        ]);

        $domain = new StockEntry(
            id: $model->id,
            organization_id: $this->organization->id,
            shop_id: $model->shop_id,
            user_id: $model->user_id,
            brand_id: $model->brand_id,
            product_id: $model->product_id,
            batch_id: $model->batch_id,
            client_id: $model->client_id,
            project_id: $model->project_id,
            quantity: 150,
            value: 2250.75,
            description: 'New description'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(150, $result->quantity);
        $this->assertEquals(2250.75, $result->value);
        $this->assertEquals('New description', $result->description);

        $this->assertDatabaseHas('stock_entries', [
            'id' => $model->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'New description'
        ]);
    }

    public function test_delete_removes_stock_entry()
    {
        $model = StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('stock_entries', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        StockEntryModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create entries for different organization
        $otherOrg = Organization::factory()->create();
        StockEntryModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new StockEntryFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 100.50
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 200.25
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 150.75
        ]);

        $filters = new StockEntryFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(451.50, $sum);
    }

    public function test_repository_handles_soft_deleted_entries()
    {
        $model = StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the entry
        $model->delete();

        // Should not find soft deleted entry
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $entry1 = StockEntryModel::factory()->create(['organization_id' => $org1->id]);
        $entry2 = StockEntryModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new StockEntryFilters([]);

        // Count should only include entries from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_repository_handles_null_description()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => null
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->description);
    }

    public function test_repository_handles_special_characters()
    {
        $specialDescription = 'Entry with special chars: @#$%^&*()';
        
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => $specialDescription
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($specialDescription, $result['data'][0]->description);
    }

    public function test_repository_handles_unicode_characters()
    {
        $unicodeDescription = 'Entrada con caracteres especiales: ñáéíóú 中文';

        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => $unicodeDescription
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($unicodeDescription, $result['data'][0]->description);
    }

    public function test_repository_with_quantity_filtering()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200
        ]);

        $filters = new StockEntryFilters(['quantity' => 100]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
    }

    public function test_repository_with_value_filtering()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1500.50
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2500.75
        ]);

        $filters = new StockEntryFilters(['value' => 1500.50]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(1500.50, $result['data'][0]->value);
    }

    public function test_repository_with_description_filtering()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => 'Alpha entry'
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => 'Beta entry'
        ]);

        $filters = new StockEntryFilters(['description' => 'Alpha']);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha entry', $result['data'][0]->description);
    }

    public function test_repository_with_multiple_filters()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Alpha entry'
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200,
            'value' => 1500.50,
            'description' => 'Beta entry'
        ]);

        $filters = new StockEntryFilters([
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Alpha'
        ]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
        $this->assertEquals(1500.50, $result['data'][0]->value);
        $this->assertEquals('Alpha entry', $result['data'][0]->description);
    }

    public function test_repository_ordering_by_quantity()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 300
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('quantity', 'asc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(100, $result['data'][0]->quantity);
        $this->assertEquals(300, $result['data'][1]->quantity);
    }

    public function test_repository_ordering_by_value()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 2500.75
        ]);
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1500.50
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('value', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2500.75, $result['data'][0]->value);
        $this->assertEquals(1500.50, $result['data'][1]->value);
    }

    public function test_repository_with_zero_quantity()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_with_high_quantity()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_with_decimal_values()
    {
        StockEntryModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 123.456789
        ]);

        $filters = new StockEntryFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }
}
