<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\DepartmentUser;
use App\Factories\Inventory\DepartmentUserFactory;
use App\Models\DepartmentUser as DepartmentUserModel;
use App\Models\Department as DepartmentModel;
use App\Models\User as UserModel;
use App\Models\Organization;
use App\Repositories\DepartmentUserRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DepartmentUserRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private DepartmentUserRepository $repository;
    private DepartmentUserFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(DepartmentUserRepository::class);
        $this->factory = app(DepartmentUserFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test department users
        DepartmentUserModel::factory()->count(5)->create();

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $departmentUser) {
            $this->assertInstanceOf(DepartmentUser::class, $departmentUser);
        }
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more department users than the default limit
        DepartmentUserModel::factory()->count(35)->create();

        $result = $this->repository->fetchAll();

        $this->assertEquals(30, $result['count']); // Default pagination limit
        $this->assertEquals(35, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(2, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_department_users()
    {
        $otherOrganization = Organization::factory()->create();

        // Create departments for different organizations
        $department1 = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $department2 = DepartmentModel::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create users for different organizations
        $user1 = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $user2 = UserModel::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create department users
        DepartmentUserModel::factory()->count(3)->create([
            'department_id' => $department1->id,
            'user_id' => $user1->id
        ]);
        DepartmentUserModel::factory()->count(2)->create([
            'department_id' => $department2->id,
            'user_id' => $user2->id
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $departmentUser) {
            $this->assertEquals($department1->id, $departmentUser->department_id);
        }
    }

    public function test_fetch_by_id_returns_department_user()
    {
        $model = DepartmentUserModel::factory()->create();

        $departmentUser = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(DepartmentUser::class, $departmentUser);
        $this->assertEquals($model->id, $departmentUser->id);
        $this->assertEquals($model->user_id, $departmentUser->user_id);
        $this->assertEquals($model->department_id, $departmentUser->department_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_department_user()
    {
        $department = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $user = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = new DepartmentUser(
            id: null,
            user_id: $user->id,
            department_id: $department->id
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(DepartmentUser::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($user->id, $result->user_id);
        $this->assertEquals($department->id, $result->department_id);

        $this->assertDatabaseHas('department_users', [
            'id' => $result->id,
            'user_id' => $user->id,
            'department_id' => $department->id
        ]);
    }

    public function test_update_modifies_existing_department_user()
    {
        $department1 = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $department2 = DepartmentModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $user1 = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $user2 = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $model = DepartmentUserModel::factory()->create([
            'department_id' => $department1->id,
            'user_id' => $user1->id
        ]);

        $domain = new DepartmentUser(
            id: $model->id,
            user_id: $user2->id,
            department_id: $department2->id
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(DepartmentUser::class, $result);
        $this->assertEquals($user2->id, $result->user_id);
        $this->assertEquals($department2->id, $result->department_id);

        $this->assertDatabaseHas('department_users', [
            'id' => $model->id,
            'user_id' => $user2->id,
            'department_id' => $department2->id
        ]);
    }

    public function test_delete_removes_department_user()
    {
        $model = DepartmentUserModel::factory()->create();

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was deleted (hard delete, not soft delete for this model)
        $this->assertDatabaseMissing('department_users', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_organization_scoping_correctly()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $dept1 = DepartmentModel::factory()->create(['organization_id' => $org1->id]);
        $dept2 = DepartmentModel::factory()->create(['organization_id' => $org2->id]);

        $user1 = UserModel::factory()->create(['organization_id' => $org1->id]);
        $user2 = UserModel::factory()->create(['organization_id' => $org2->id]);

        $deptUser1 = DepartmentUserModel::factory()->create([
            'department_id' => $dept1->id,
            'user_id' => $user1->id
        ]);
        $deptUser2 = DepartmentUserModel::factory()->create([
            'department_id' => $dept2->id,
            'user_id' => $user2->id
        ]);

        $result1 = $this->repository->fetchFromOrganization($org1->id);
        $result2 = $this->repository->fetchFromOrganization($org2->id);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
        $this->assertEquals($deptUser1->id, $result1['data'][0]->id);
        $this->assertEquals($deptUser2->id, $result2['data'][0]->id);
    }

    public function test_store_sets_id_on_domain_object()
    {
        $department = DepartmentModel::factory()->create();
        $user = UserModel::factory()->create();

        $domain = new DepartmentUser(
            id: null,
            user_id: $user->id,
            department_id: $department->id
        );

        $this->assertNull($domain->id);

        $result = $this->repository->store($domain);

        $this->assertNotNull($result->id);
        $this->assertEquals($domain->id, $result->id); // Should be the same object
    }

    public function test_fetch_from_organization_with_empty_result()
    {
        $emptyOrganization = Organization::factory()->create();

        $result = $this->repository->fetchFromOrganization($emptyOrganization->id);

        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(0, $result['total']);
    }
}
