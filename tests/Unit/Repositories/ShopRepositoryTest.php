<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\ShopFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ShopFactory;
use App\Models\Shop as ShopModel;
use App\Models\Organization;
use App\Repositories\ShopRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShopRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ShopRepository $repository;
    private ShopFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ShopRepository::class);
        $this->factory = app(ShopFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test shops
        ShopModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $shop) {
            $this->assertInstanceOf(Shop::class, $shop);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create shops with different names
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Shop'
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Shop'
        ]);

        $filters = new ShopFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Shop', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more shops than the limit
        ShopModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 5);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_shops()
    {
        $otherOrganization = Organization::factory()->create();

        // Create shops for different organizations
        ShopModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        ShopModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $shop) {
            $this->assertEquals($this->organization->id, $shop->organization_id);
        }
    }

    public function test_fetch_by_id_returns_shop()
    {
        $model = ShopModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $shop = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Shop::class, $shop);
        $this->assertEquals($model->id, $shop->id);
        $this->assertEquals($model->name, $shop->name);
        $this->assertEquals($model->organization_id, $shop->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_shop()
    {
        $domain = new Shop(
            id: null,
            organization_id: $this->organization->id,
            name: 'Store Test Shop',
            description: 'Test shop for store',
            is_active: true
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('Store Test Shop', $result->name);

        $this->assertDatabaseHas('shops', [
            'id' => $result->id,
            'name' => 'Store Test Shop',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_shop()
    {
        $model = ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Shop',
            'description' => 'Old description',
            'is_active' => true
        ]);

        $domain = new Shop(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'New Shop',
            description: 'New description',
            is_active: false
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('New Shop', $result->name);
        $this->assertFalse($result->is_active);

        $this->assertDatabaseHas('shops', [
            'id' => $model->id,
            'name' => 'New Shop',
            'description' => 'New description',
            'is_active' => false
        ]);
    }

    public function test_delete_removes_shop()
    {
        $model = ShopModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('shops', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        ShopModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create shops for different organization
        $otherOrg = Organization::factory()->create();
        ShopModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new ShopFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_repository_handles_soft_deleted_shops()
    {
        $model = ShopModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the shop
        $model->delete();

        // Should not find soft deleted shop
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $shop1 = ShopModel::factory()->create(['organization_id' => $org1->id]);
        $shop2 = ShopModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new ShopFilters([]);

        // Count should only include shops from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_repository_handles_empty_description()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => ''
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('', $result['data'][0]->description);
    }

    public function test_repository_handles_null_description()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => null
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->description);
    }

    public function test_repository_handles_special_characters()
    {
        $specialName = 'Shop & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';
        
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => $specialName,
            'description' => $specialDescription
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($specialName, $result['data'][0]->name);
        $this->assertEquals($specialDescription, $result['data'][0]->description);
    }

    public function test_repository_handles_unicode_characters()
    {
        $unicodeName = 'Shöp Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';

        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => $unicodeName,
            'description' => $unicodeDescription
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($unicodeName, $result['data'][0]->name);
        $this->assertEquals($unicodeDescription, $result['data'][0]->description);
    }

    public function test_repository_with_active_status_filtering()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Shop',
            'is_active' => true
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Shop',
            'is_active' => false
        ]);

        $filters = new ShopFilters(['is_active' => true]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Active Shop', $result['data'][0]->name);
        $this->assertTrue($result['data'][0]->is_active);
    }

    public function test_repository_with_inactive_status_filtering()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Shop',
            'is_active' => true
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Shop',
            'is_active' => false
        ]);

        $filters = new ShopFilters(['is_active' => false]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Inactive Shop', $result['data'][0]->name);
        $this->assertFalse($result['data'][0]->is_active);
    }

    public function test_repository_with_name_filtering()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Main Store'
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Warehouse'
        ]);

        $filters = new ShopFilters(['name' => 'Main']);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Main Store', $result['data'][0]->name);
    }

    public function test_repository_with_description_filtering()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Shop 1',
            'description' => 'Main store location'
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Shop 2',
            'description' => 'Warehouse location'
        ]);

        $filters = new ShopFilters(['description' => 'Main']);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Main store location', $result['data'][0]->description);
    }

    public function test_repository_with_multiple_filters()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Main Store',
            'description' => 'Main store location',
            'is_active' => true
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Main Store',
            'description' => 'Main store location',
            'is_active' => false
        ]);

        $filters = new ShopFilters([
            'name' => 'Main',
            'description' => 'Main',
            'is_active' => true
        ]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Active Main Store', $result['data'][0]->name);
        $this->assertTrue($result['data'][0]->is_active);
    }

    public function test_repository_ordering_by_name()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Zebra Shop'
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Shop'
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('name', 'asc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals('Alpha Shop', $result['data'][0]->name);
        $this->assertEquals('Zebra Shop', $result['data'][1]->name);
    }

    public function test_repository_ordering_by_is_active()
    {
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Shop',
            'is_active' => false
        ]);
        ShopModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Shop',
            'is_active' => true
        ]);

        $filters = new ShopFilters([]);
        $orderBy = new OrderBy('is_active', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertTrue($result['data'][0]->is_active); // Active first
        $this->assertFalse($result['data'][1]->is_active); // Inactive second
    }
}
