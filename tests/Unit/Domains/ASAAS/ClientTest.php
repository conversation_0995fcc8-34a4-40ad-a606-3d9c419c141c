<?php

namespace Tests\Unit\Domains\ASAAS;

use Tests\TestCase;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Domains\Inventory\Client as ClientDomain;
use Carbon\Carbon;

class ClientTest extends TestCase
{
    public function test_client_domain_can_be_created()
    {
        $clientDomain = new ClientDomain(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: '11999999999',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: '12345678901',
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $asaasClient = new AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            client: $clientDomain
        );

        $this->assertEquals(1, $asaasClient->id);
        $this->assertEquals(1, $asaasClient->organization_id);
        $this->assertEquals(1, $asaasClient->client_id);
        $this->assertEquals('Test Client', $asaasClient->client->name);
        $this->assertEquals('<EMAIL>', $asaasClient->client->email);
        $this->assertEquals('11999999999', $asaasClient->client->phone);
        $this->assertEquals('12345678901', $asaasClient->client->cpf);
    }

    public function test_client_without_asaas_integration()
    {
        $clientDomain = new ClientDomain(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: null,
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $asaasClient = new AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            client: $clientDomain
        );

        $this->assertFalse($asaasClient->hasAsaasIntegration());
    }

    public function test_client_with_asaas_integration()
    {
        $clientDomain = new ClientDomain(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: null,
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $asaasClient = new AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            asaas_customer_id: 'cus_123456',
            asaas_synced_at: Carbon::now(),
            client: $clientDomain
        );

        $this->assertTrue($asaasClient->hasAsaasIntegration());
    }

    public function test_client_get_document_cpf()
    {
        $clientDomain = new ClientDomain(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: '12345678901',
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $asaasClient = new AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            client: $clientDomain
        );

        $this->assertEquals('12345678901', $asaasClient->getDocument());
    }

    public function test_client_get_document_cnpj()
    {
        $clientDomain = new ClientDomain(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: '12345678000195',
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $asaasClient = new AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            client: $clientDomain
        );

        $this->assertEquals('12345678000195', $asaasClient->getDocument());
    }
}
