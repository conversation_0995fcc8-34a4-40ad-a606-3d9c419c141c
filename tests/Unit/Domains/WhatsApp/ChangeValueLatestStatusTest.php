<?php

namespace Tests\Unit\Domains\WhatsApp;

use App\Domains\WhatsApp\ChangeValue;
use Tests\TestCase;

class ChangeValueLatestStatusTest extends TestCase
{
    public function test_get_latest_status_returns_first_status_when_statuses_exist()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.123',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '5511999999999'
                ],
                [
                    'id' => 'wamid.456',
                    'status' => 'sent',
                    'timestamp' => '1234567880',
                    'recipient_id' => '5511999999999'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNotNull($latestStatus);
        $this->assertEquals('wamid.123', $latestStatus['id']);
        $this->assertEquals('delivered', $latestStatus['status']);
        $this->assertEquals('1234567890', $latestStatus['timestamp']);
    }

    public function test_get_latest_status_returns_null_when_no_statuses()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => []
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNull($latestStatus);
    }

    public function test_get_latest_status_returns_null_when_statuses_not_set()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNull($latestStatus);
    }

    public function test_get_latest_status_returns_single_status_when_only_one_exists()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.single',
                    'status' => 'read',
                    'timestamp' => '1234567890',
                    'recipient_id' => '5511999999999'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNotNull($latestStatus);
        $this->assertEquals('wamid.single', $latestStatus['id']);
        $this->assertEquals('read', $latestStatus['status']);
    }

    public function test_get_latest_status_handles_incomplete_status_data()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.incomplete',
                    'status' => 'delivered'
                    // Missing timestamp and recipient_id
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNotNull($latestStatus);
        $this->assertEquals('wamid.incomplete', $latestStatus['id']);
        $this->assertEquals('delivered', $latestStatus['status']);
        $this->assertArrayNotHasKey('timestamp', $latestStatus);
    }

    public function test_get_latest_status_preserves_all_status_fields()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.complete',
                    'status' => 'failed',
                    'timestamp' => '1234567890',
                    'recipient_id' => '5511999999999',
                    'errors' => [
                        [
                            'code' => 131000,
                            'title' => 'Message failed to send'
                        ]
                    ]
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $latestStatus = $changeValue->getLatestStatus();

        // Assert
        $this->assertNotNull($latestStatus);
        $this->assertEquals('wamid.complete', $latestStatus['id']);
        $this->assertEquals('failed', $latestStatus['status']);
        $this->assertEquals('1234567890', $latestStatus['timestamp']);
        $this->assertEquals('5511999999999', $latestStatus['recipient_id']);
        $this->assertArrayHasKey('errors', $latestStatus);
        $this->assertIsArray($latestStatus['errors']);
    }
}
