<?php

namespace Tests\Unit\Domains\WhatsApp;

use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use Tests\TestCase;

class WhatsAppMessageUpdateStatusTest extends TestCase
{
    public function test_update_whatsapp_message_status_updates_message_status_field()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('delivered');

        // Assert
        $this->assertEquals('delivered', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_sent_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: null
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('sent');

        // Assert
        $this->assertEquals('sent', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_delivered_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('delivered');

        // Assert
        $this->assertEquals('delivered', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_read_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'delivered'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('read');

        // Assert
        $this->assertEquals('read', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_failed_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('failed');

        // Assert
        $this->assertEquals('failed', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_overwrites_previous_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('delivered');
        $whatsAppMessage->updateWhatsAppMessageStatus('read');

        // Assert
        $this->assertEquals('read', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_empty_string()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('');

        // Assert
        $this->assertEquals('', $whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_handles_null_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus(null);

        // Assert
        $this->assertNull($whatsAppMessage->message_status);
    }

    public function test_update_whatsapp_message_status_preserves_other_fields()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent',
            wa_id: '5511999999999',
            input_phone: '11999999999'
        );

        $originalWaId = $whatsAppMessage->wa_id;
        $originalInputPhone = $whatsAppMessage->input_phone;
        $originalWhatsappMessageId = $whatsAppMessage->whatsapp_message_id;

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('delivered');

        // Assert
        $this->assertEquals('delivered', $whatsAppMessage->message_status);
        $this->assertEquals($originalWaId, $whatsAppMessage->wa_id);
        $this->assertEquals($originalInputPhone, $whatsAppMessage->input_phone);
        $this->assertEquals($originalWhatsappMessageId, $whatsAppMessage->whatsapp_message_id);
    }

    public function test_update_whatsapp_message_status_handles_unknown_status()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            id: 1,
            message_id: 1,
            message: null,
            whatsapp_message_id: 'wamid.123',
            message_status: 'sent'
        );

        // Act
        $whatsAppMessage->updateWhatsAppMessageStatus('unknown_status');

        // Assert
        $this->assertEquals('unknown_status', $whatsAppMessage->message_status);
    }
}
