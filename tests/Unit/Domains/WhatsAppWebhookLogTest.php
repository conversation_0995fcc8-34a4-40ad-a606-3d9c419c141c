<?php

namespace Tests\Unit\Domains;

use Tests\TestCase;
use App\Domains\WhatsAppWebhookLog;
use Carbon\Carbon;

class WhatsAppWebhookLogTest extends TestCase
{
    public function test_can_create_domain_instance()
    {
        $log = new WhatsAppWebhookLog(
            id: 1,
            organization_id: 123,
            phone_number_id: '569357716260641',
            event_type: 'message',
            webhook_payload: ['test' => 'data'],
            processing_status: 'pending'
        );

        $this->assertEquals(1, $log->id);
        $this->assertEquals(123, $log->organization_id);
        $this->assertEquals('569357716260641', $log->phone_number_id);
        $this->assertEquals('message', $log->event_type);
        $this->assertEquals(['test' => 'data'], $log->webhook_payload);
        $this->assertEquals('pending', $log->processing_status);
    }

    public function test_create_static_method()
    {
        $log = WhatsAppWebhookLog::create(
            organization_id: 123,
            phone_number_id: '569357716260641',
            event_type: 'message',
            webhook_payload: ['test' => 'data'],
            processing_status: 'pending'
        );

        $this->assertEquals(123, $log->organization_id);
        $this->assertEquals('569357716260641', $log->phone_number_id);
        $this->assertEquals('message', $log->event_type);
        $this->assertEquals(['test' => 'data'], $log->webhook_payload);
        $this->assertEquals('pending', $log->processing_status);
        $this->assertInstanceOf(Carbon::class, $log->created_at);
        $this->assertInstanceOf(Carbon::class, $log->updated_at);
    }

    public function test_to_store_array()
    {
        $log = WhatsAppWebhookLog::create(
            organization_id: 123,
            phone_number_id: '569357716260641',
            event_type: 'message',
            webhook_payload: ['test' => 'data'],
            processing_status: 'pending',
            error_message: 'Test error'
        );

        $array = $log->toStoreArray();

        $this->assertEquals([
            'organization_id' => 123,
            'phone_number_id' => '569357716260641',
            'event_type' => 'message',
            'webhook_payload' => ['test' => 'data'],
            'processed_at' => null,
            'processing_status' => 'pending',
            'error_message' => 'Test error',
        ], $array);
    }

    public function test_to_array()
    {
        $log = WhatsAppWebhookLog::create(
            organization_id: 123,
            phone_number_id: '569357716260641',
            event_type: 'message',
            webhook_payload: ['test' => 'data'],
            processing_status: 'success'
        );

        $array = $log->toArray();

        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('organization_id', $array);
        $this->assertArrayHasKey('phone_number_id', $array);
        $this->assertArrayHasKey('event_type', $array);
        $this->assertArrayHasKey('event_type_label', $array);
        $this->assertArrayHasKey('webhook_payload', $array);
        $this->assertArrayHasKey('processing_status', $array);
        $this->assertArrayHasKey('processing_status_label', $array);
        $this->assertArrayHasKey('processing_status_color', $array);
        $this->assertArrayHasKey('was_successful', $array);
        $this->assertArrayHasKey('has_error', $array);

        $this->assertEquals('Message', $array['event_type_label']);
        $this->assertEquals('Success', $array['processing_status_label']);
        $this->assertEquals('green', $array['processing_status_color']);
        $this->assertTrue($array['was_successful']);
    }

    public function test_event_type_labels()
    {
        $messageLog = new WhatsAppWebhookLog(event_type: 'message');
        $statusLog = new WhatsAppWebhookLog(event_type: 'status');
        $otherLog = new WhatsAppWebhookLog(event_type: 'other');

        $this->assertEquals('Message', $messageLog->getEventTypeLabel());
        $this->assertEquals('Status Update', $statusLog->getEventTypeLabel());
        $this->assertEquals('Other', $otherLog->getEventTypeLabel());
    }

    public function test_processing_status_labels()
    {
        $pendingLog = new WhatsAppWebhookLog(processing_status: 'pending');
        $successLog = new WhatsAppWebhookLog(processing_status: 'success');
        $failedLog = new WhatsAppWebhookLog(processing_status: 'failed');

        $this->assertEquals('Pending', $pendingLog->getProcessingStatusLabel());
        $this->assertEquals('Success', $successLog->getProcessingStatusLabel());
        $this->assertEquals('Failed', $failedLog->getProcessingStatusLabel());
    }

    public function test_processing_status_colors()
    {
        $pendingLog = new WhatsAppWebhookLog(processing_status: 'pending');
        $successLog = new WhatsAppWebhookLog(processing_status: 'success');
        $failedLog = new WhatsAppWebhookLog(processing_status: 'failed');

        $this->assertEquals('yellow', $pendingLog->getProcessingStatusColor());
        $this->assertEquals('green', $successLog->getProcessingStatusColor());
        $this->assertEquals('red', $failedLog->getProcessingStatusColor());
    }

    public function test_status_check_methods()
    {
        $pendingLog = new WhatsAppWebhookLog(processing_status: 'pending');
        $successLog = new WhatsAppWebhookLog(processing_status: 'success');
        $failedLog = new WhatsAppWebhookLog(processing_status: 'failed');

        $this->assertTrue($pendingLog->isPending());
        $this->assertFalse($pendingLog->wasSuccessful());
        $this->assertFalse($pendingLog->hasFailed());

        $this->assertFalse($successLog->isPending());
        $this->assertTrue($successLog->wasSuccessful());
        $this->assertFalse($successLog->hasFailed());

        $this->assertFalse($failedLog->isPending());
        $this->assertFalse($failedLog->wasSuccessful());
        $this->assertTrue($failedLog->hasFailed());
    }

    public function test_has_error()
    {
        $logWithError = new WhatsAppWebhookLog(error_message: 'Some error');
        $logWithoutError = new WhatsAppWebhookLog(error_message: null);
        $logWithEmptyError = new WhatsAppWebhookLog(error_message: '');

        $this->assertTrue($logWithError->hasError());
        $this->assertFalse($logWithoutError->hasError());
        $this->assertFalse($logWithEmptyError->hasError());
    }

    public function test_mark_as_successful()
    {
        $log = new WhatsAppWebhookLog(
            processing_status: 'pending',
            error_message: 'Some error'
        );

        $log->markAsSuccessful();

        $this->assertEquals('success', $log->processing_status);
        $this->assertInstanceOf(Carbon::class, $log->processed_at);
        $this->assertNull($log->error_message);
        $this->assertInstanceOf(Carbon::class, $log->updated_at);
    }

    public function test_mark_as_failed()
    {
        $log = new WhatsAppWebhookLog(processing_status: 'pending');

        $errorMessage = 'Processing failed';
        $log->markAsFailed($errorMessage);

        $this->assertEquals('failed', $log->processing_status);
        $this->assertInstanceOf(Carbon::class, $log->processed_at);
        $this->assertEquals($errorMessage, $log->error_message);
        $this->assertInstanceOf(Carbon::class, $log->updated_at);
    }

    public function test_get_duration_since_created()
    {
        $log = new WhatsAppWebhookLog(created_at: now()->subMinutes(30));

        $duration = $log->getDurationSinceCreated();

        $this->assertIsInt($duration);
        $this->assertGreaterThanOrEqual(29, $duration);
        $this->assertLessThanOrEqual(31, $duration);
    }

    public function test_get_duration_since_processed()
    {
        $log = new WhatsAppWebhookLog(processed_at: now()->subMinutes(15));

        $duration = $log->getDurationSinceProcessed();

        $this->assertIsInt($duration);
        $this->assertGreaterThanOrEqual(14, $duration);
        $this->assertLessThanOrEqual(16, $duration);
    }

    public function test_get_message_id_from_message_payload()
    {
        $payload = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'messages' => [
                                    ['id' => 'msg_123']
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $log = new WhatsAppWebhookLog(
            event_type: 'message',
            webhook_payload: $payload
        );

        $this->assertEquals('msg_123', $log->getMessageId());
    }

    public function test_get_message_id_from_status_payload()
    {
        $payload = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    ['id' => 'msg_456']
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $log = new WhatsAppWebhookLog(
            event_type: 'status',
            webhook_payload: $payload
        );

        $this->assertEquals('msg_456', $log->getMessageId());
    }

    public function test_get_sender_phone_number()
    {
        $payload = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'messages' => [
                                    ['from' => '5511999999999']
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $log = new WhatsAppWebhookLog(
            event_type: 'message',
            webhook_payload: $payload
        );

        $this->assertEquals('5511999999999', $log->getSenderPhoneNumber());
    }

    public function test_get_payload_size()
    {
        $payload = ['test' => 'data'];
        $log = new WhatsAppWebhookLog(webhook_payload: $payload);

        $expectedSize = strlen(json_encode($payload));
        $this->assertEquals($expectedSize, $log->getPayloadSize());
    }

    public function test_has_large_payload()
    {
        $smallPayload = ['test' => 'data'];
        $largePayload = ['data' => str_repeat('x', 15000)];

        $smallLog = new WhatsAppWebhookLog(webhook_payload: $smallPayload);
        $largeLog = new WhatsAppWebhookLog(webhook_payload: $largePayload);

        $this->assertFalse($smallLog->hasLargePayload());
        $this->assertTrue($largeLog->hasLargePayload());
    }
}
