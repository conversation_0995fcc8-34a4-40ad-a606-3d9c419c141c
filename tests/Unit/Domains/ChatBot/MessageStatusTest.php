<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Message;
use App\Enums\MessageStatus;
use Tests\TestCase;

class MessageStatusTest extends TestCase
{
    public function test_send_method_updates_status_correctly()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        // Act
        $message->send();

        // Assert
        $this->assertEquals(MessageStatus::is_sent, $message->status);
        $this->assertTrue($message->is_sent);
        $this->assertFalse($message->is_fail);
        $this->assertNotNull($message->sent_at);
    }

    public function test_deliver_method_updates_status_correctly()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_sent
        );

        // Act
        $message->deliver();

        // Assert
        $this->assertEquals(MessageStatus::is_delivered, $message->status);
        $this->assertTrue($message->is_delivered);
        $this->assertFalse($message->is_fail);
    }

    public function test_mark_as_read_method_updates_status_correctly()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_delivered
        );

        // Act
        $message->markAsRead();

        // Assert
        $this->assertEquals(MessageStatus::is_read, $message->status);
        $this->assertTrue($message->is_read);
        $this->assertTrue($message->is_delivered); // Se foi lida, certamente foi entregue
        $this->assertFalse($message->is_fail);
    }

    public function test_fail_method_updates_status_correctly()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_sending
        );

        // Act
        $message->fail();

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $message->status);
        $this->assertTrue($message->is_fail);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
        $this->assertNotNull($message->sent_at);
    }

    public function test_message_status_progression()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        // Act & Assert - Draft -> Sent
        $this->assertEquals(MessageStatus::is_draft, $message->status);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
        $this->assertFalse($message->is_read);

        // Sent
        $message->send();
        $this->assertEquals(MessageStatus::is_sent, $message->status);
        $this->assertTrue($message->is_sent);
        $this->assertFalse($message->is_delivered);
        $this->assertFalse($message->is_read);

        // Delivered
        $message->deliver();
        $this->assertEquals(MessageStatus::is_delivered, $message->status);
        $this->assertTrue($message->is_sent);
        $this->assertTrue($message->is_delivered);
        $this->assertFalse($message->is_read);

        // Read
        $message->markAsRead();
        $this->assertEquals(MessageStatus::is_read, $message->status);
        $this->assertTrue($message->is_sent);
        $this->assertTrue($message->is_delivered);
        $this->assertTrue($message->is_read);
    }

    public function test_message_can_fail_at_any_stage()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_sent,
            is_sent: true
        );

        // Act
        $message->fail();

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $message->status);
        $this->assertTrue($message->is_fail);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
    }

    public function test_to_array_includes_is_delivered_field()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_delivered,
            is_sent: true,
            is_delivered: true
        );

        // Act
        $array = $message->toArray();

        // Assert
        $this->assertArrayHasKey('is_delivered', $array);
        $this->assertTrue($array['is_delivered']);
        $this->assertTrue($array['is_sent']);
        $this->assertEquals(MessageStatus::is_delivered, $array['status']);
    }

    public function test_to_store_array_includes_is_delivered_field()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_delivered,
            is_sent: true,
            is_delivered: true
        );

        // Act
        $array = $message->toStoreArray();

        // Assert
        $this->assertArrayHasKey('is_delivered', $array);
        $this->assertTrue($array['is_delivered']);
        $this->assertTrue($array['is_sent']);
        $this->assertEquals(MessageStatus::is_delivered, $array['status']);
    }

    public function test_to_update_array_includes_is_delivered_field()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_delivered,
            is_sent: true,
            is_delivered: true
        );

        // Act
        $array = $message->toUpdateArray();

        // Assert
        $this->assertArrayHasKey('is_delivered', $array);
        $this->assertTrue($array['is_delivered']);
        $this->assertTrue($array['is_sent']);
        $this->assertEquals(MessageStatus::is_delivered, $array['status']);
    }
}
