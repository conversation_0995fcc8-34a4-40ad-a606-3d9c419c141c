<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Step;
use App\Enums\StepType;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class StepTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Step::class, $domain);
        // Add specific assertions for Step properties
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    protected function createDomainInstance()
    {
        return new Step(
            id: 1,
            organization_id: 1,
            flow_id: 1,
            step: 'step_1',
            type: 'message',
            step_type: StepType::MESSAGE,
            position: 1,
            next_step: 2,
            earlier_step: null,
            is_initial_step: true,
            is_ending_step: false,
            configuration: ['text' => 'Hello World'],
            navigation_rules: null,
            timeout_seconds: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'flow_id',
            'step',
            'type',
            'step_type',
            'position',
            'next_step',
            'earlier_step',
            'is_initial_step',
            'is_ending_step',
            'configuration',
            'navigation_rules',
            'timeout_seconds',
            'json',
            'input',
            'created_at',
            'updated_at',
            'flow',
            'component',
        ];
    }

    public function test_step_type_enum_functionality()
    {
        $step = $this->createDomainInstance();

        $this->assertEquals(StepType::MESSAGE, $step->step_type);
        $this->assertTrue($step->step_type === StepType::MESSAGE);
    }

    public function test_configuration_validation()
    {
        $step = $this->createDomainInstance();

        $this->assertTrue($step->validateConfiguration());

        // Test invalid configuration
        $step->configuration = [];
        $this->assertFalse($step->validateConfiguration());
    }

    public function test_navigation_rules()
    {
        $step = $this->createDomainInstance();
        $step->step_type = StepType::INTERACTIVE;

        // Add navigation rule
        $step->addNavigationRule('button_click', 'yes', 2, 1);
        $step->addNavigationRule('button_click', 'no', 3, 0);

        $this->assertCount(2, $step->navigation_rules);

        // Test navigation with button click
        $nextStep = $step->getNextStepByCondition('', 'yes');
        $this->assertEquals(2, $nextStep);

        $nextStep = $step->getNextStepByCondition('', 'no');
        $this->assertEquals(3, $nextStep);

        // Test default navigation
        $nextStep = $step->getNextStepByCondition('', 'unknown');
        $this->assertEquals(2, $nextStep); // Should return next_step
    }

    public function test_timeout_functionality()
    {
        $step = $this->createDomainInstance();

        $this->assertFalse($step->hasTimeout());
        $this->assertEquals(0, $step->getTimeoutSeconds());

        $step->timeout_seconds = 300;
        $this->assertTrue($step->hasTimeout());
        $this->assertEquals(300, $step->getTimeoutSeconds());
    }

    public function test_legacy_type_field_conversion()
    {
        $step = $this->createDomainInstance();
        $step->step_type = null;
        $step->type = 'interactive';

        $step->setStepTypeFromLegacyFields();

        $this->assertEquals(StepType::INTERACTIVE, $step->step_type);
    }

    public function test_default_configuration_setting()
    {
        $step = $this->createDomainInstance();
        $step->configuration = null;
        $step->step_type = StepType::INPUT;

        $step->setDefaultConfiguration();

        $this->assertNotNull($step->configuration);
        $this->assertArrayHasKey('prompt', $step->configuration);
        $this->assertArrayHasKey('field_mapping', $step->configuration);
    }
}
