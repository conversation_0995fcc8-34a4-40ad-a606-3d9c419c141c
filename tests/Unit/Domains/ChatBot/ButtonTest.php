<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use App\Enums\ChatBot\WhatsAppButtonType;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ButtonTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Button::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Button', $domain->text);
        $this->assertEquals('reply', $domain->type);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_get_whatsapp_button_type_reply()
    {
        $button = new Button(
            1, 1, 'Yes', 'reply', null, null, null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertEquals(WhatsAppButtonType::REPLY, $buttonType);
    }

    public function test_get_whatsapp_button_type_url()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, 'https://example.com', null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertEquals(WhatsAppButtonType::URL, $buttonType);
    }

    public function test_get_whatsapp_button_type_phone_number()
    {
        $button = new Button(
            1, 1, 'Call Us', 'phone_number', null, '+1234567890', null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertEquals(WhatsAppButtonType::PHONE_NUMBER, $buttonType);
    }

    public function test_get_whatsapp_button_type_copy_code()
    {
        $button = new Button(
            1, 1, 'Copy Code', 'copy_code', null, 'PROMO123', null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertEquals(WhatsAppButtonType::COPY_CODE, $buttonType);
    }

    public function test_get_whatsapp_button_type_flow()
    {
        $button = new Button(
            1, 1, 'Start Flow', 'flow', null, 'flow_token_123', null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertEquals(WhatsAppButtonType::FLOW, $buttonType);
    }

    public function test_get_whatsapp_button_type_invalid()
    {
        $button = new Button(
            1, 1, 'Invalid', 'invalid_type', null, null, null, null
        );

        $buttonType = $button->getWhatsAppButtonType();
        $this->assertNull($buttonType);
    }

    public function test_validate_title_length_valid()
    {
        $button = new Button(
            1, 1, 'Short Title', 'reply', null, null, null, null
        );

        $this->assertTrue($button->validateTitleLength());
    }

    public function test_validate_title_length_invalid()
    {
        $button = new Button(
            1, 1, 'This is a very long title that exceeds the maximum allowed length', 'reply', null, null, null, null
        );

        $this->assertFalse($button->validateTitleLength());
    }

    public function test_validate_url_button_valid()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, 'https://example.com', null, null
        );

        $this->assertTrue($button->validateForWhatsApp());
    }

    public function test_validate_url_button_invalid_url()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, 'invalid-url', null, null
        );

        $this->assertFalse($button->validateForWhatsApp());
    }

    public function test_validate_url_button_http_not_allowed()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, 'http://example.com', null, null
        );

        $this->assertFalse($button->validateForWhatsApp());
    }

    public function test_validate_phone_number_button_valid()
    {
        $button = new Button(
            1, 1, 'Call Us', 'phone_number', null, '+1234567890', null, null
        );

        $this->assertTrue($button->validateForWhatsApp());
    }

    public function test_validate_phone_number_button_invalid()
    {
        $button = new Button(
            1, 1, 'Call Us', 'phone_number', null, '************', null, null
        );

        $this->assertFalse($button->validateForWhatsApp());
    }

    public function test_is_reply_button()
    {
        $replyButton = new Button(
            1, 1, 'Yes', 'reply', null, null, null, null
        );

        $urlButton = new Button(
            1, 1, 'Visit', 'url', null, 'https://example.com', null, null
        );

        $this->assertTrue($replyButton->isReplyButton());
        $this->assertFalse($urlButton->isReplyButton());
    }

    protected function createDomainInstance()
    {
        return new Button(
            1, // id
            1, // organization_id
            'Test Button', // text
            'reply', // type
            null, // internal_type
            null, // internal_data
            null, // callback_data
            null, // json
            Carbon::now(), // created_at
            Carbon::now() // updated_at
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'text',
            'type',
            'internal_type',
            'internal_data',
            'callback_data',
            'callback_data_array',
            'json',
            'created_at',
            'updated_at'
        ];
    }
}
