<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use App\Enums\ChatBot\WhatsAppButtonType;
use Tests\TestCase;

class ButtonWhatsAppPayloadEnumTest extends TestCase
{
    public function test_reply_button_whatsapp_payload()
    {
        $button = new Button(
            1, 1, 'Yes', 'reply', null, null, null, null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'reply',
            'reply' => [
                'id' => '1',
                'title' => 'Yes'
            ]
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_url_button_whatsapp_payload()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, 'https://example.com', null, null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'url',
            'url' => 'https://example.com',
            'text' => 'Visit Site'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_phone_number_button_whatsapp_payload()
    {
        $button = new Button(
            1, 1, 'Call Us', 'phone_number', null, '+1234567890', null, null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'phone_number',
            'phone_number' => '+1234567890',
            'text' => 'Call Us'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_copy_code_button_whatsapp_payload()
    {
        $button = new Button(
            1, 1, 'Copy Code', 'copy_code', null, 'PROMO123', null, null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'copy_code',
            'copy_code' => 'PROMO123',
            'text' => 'Copy Code'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_flow_button_whatsapp_payload()
    {
        $button = new Button(
            1, 1, 'Start Flow', 'flow', null, 'flow_token_123', null, null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'flow',
            'flow_token' => 'flow_token_123',
            'text' => 'Start Flow'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_button_with_callback_data_fallback()
    {
        $button = new Button(
            1, 1, 'Visit Site', 'url', null, null,
            json_encode(['url' => 'https://fallback.com']), null
        );

        $payload = $button->toWhatsAppPayload();

        $expectedPayload = [
            'type' => 'url',
            'url' => 'https://fallback.com',
            'text' => 'Visit Site'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_invalid_button_type_fallback_to_json()
    {
        $jsonPayload = ['custom' => 'payload'];
        $button = new Button(
            1, 1, 'Custom', 'invalid_type', null, null, null,
            json_encode($jsonPayload)
        );

        $payload = $button->toWhatsAppPayload();

        $this->assertEquals($jsonPayload, $payload); // Should return JSON content for unknown types
    }

    public function test_button_with_valid_json_fallback()
    {
        $jsonPayload = ['type' => 'custom', 'data' => 'test'];
        $button = new Button(
            1, 1, 'Custom', 'unknown_type', null, null, null,
            json_encode($jsonPayload)
        );

        $payload = $button->toWhatsAppPayload();

        $this->assertEquals($jsonPayload, $payload); // Should return JSON content for unknown types
    }

    public function test_extract_internal_data_url_button()
    {
        $buttonArray = [
            'type' => 'url',
            'text' => 'Visit Site',
            'url' => 'https://example.com'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);
        $this->assertEquals('https://example.com', $internalData);
    }

    public function test_extract_internal_data_phone_button()
    {
        $buttonArray = [
            'type' => 'phone_number',
            'text' => 'Call Us',
            'phone_number' => '+1234567890'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);
        $this->assertEquals('+1234567890', $internalData);
    }

    public function test_extract_internal_data_copy_code_button()
    {
        $buttonArray = [
            'type' => 'copy_code',
            'text' => 'Copy Code',
            'copy_code' => 'PROMO123'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);
        $this->assertEquals('PROMO123', $internalData);
    }

    public function test_extract_internal_data_flow_button()
    {
        $buttonArray = [
            'type' => 'flow',
            'text' => 'Start Flow',
            'flow_token' => 'token123'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);
        $this->assertEquals('token123', $internalData);
    }

    public function test_extract_internal_data_reply_button()
    {
        $buttonArray = [
            'type' => 'reply',
            'text' => 'Yes'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);
        $this->assertNull($internalData);
    }

    public function test_get_max_allowed_buttons()
    {
        $replyButton = new Button(1, 1, 'Yes', 'reply', null, null, null, null);
        $urlButton = new Button(1, 1, 'Visit', 'url', null, 'https://example.com', null, null);

        $this->assertEquals(3, $replyButton->getMaxAllowedButtons());
        $this->assertEquals(1, $urlButton->getMaxAllowedButtons());
    }
}
