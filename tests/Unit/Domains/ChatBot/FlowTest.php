<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class FlowTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $steps = [
            new Step(1, 1, 1, 'Step 1', 'text', 1, null, null, true, false, true, false, false, false, '{}', null, $createdAt, $updatedAt),
            new Step(2, 1, 1, 'Step 2', 'text', 2, null, null, false, false, true, false, false, false, '{}', null, $createdAt, $updatedAt),
        ];

        $flow = new Flow(
            1,
            1,
            'Test Flow',
            'Test Description',
            2,
            '{"flow": "data"}',
            true,
            $createdAt,
            $updatedAt,
            $steps
        );

        $this->assertEquals(1, $flow->id);
        $this->assertEquals(1, $flow->organization_id);
        $this->assertEquals('Test Flow', $flow->name);
        $this->assertEquals('Test Description', $flow->description);
        $this->assertEquals(2, $flow->steps_count);
        $this->assertEquals('{"flow": "data"}', $flow->json);
        $this->assertTrue($flow->is_default_flow);
        $this->assertEquals($createdAt, $flow->created_at);
        $this->assertEquals($updatedAt, $flow->updated_at);
        $this->assertIsArray($flow->steps);
        $this->assertCount(2, $flow->steps);
        $this->assertInstanceOf(Step::class, $flow->steps[0]);
    }

    public function test_to_array_method()
    {
        $flow = $this->createDomainInstance();
        $array = $flow->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals('Test Flow', $array['name']);
        $this->assertEquals('Test Description', $array['description']);
        $this->assertEquals(2, $array['steps_count']);
        $this->assertEquals('{"flow": "data"}', $array['json']);
        $this->assertTrue($array['is_default_flow']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertIsArray($array['steps']);
    }

    public function test_to_store_array_excludes_timestamps_and_id()
    {
        $flow = $this->createDomainInstance();
        $storeArray = $flow->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('steps', $storeArray);
        $this->assertArrayHasKey('organization_id', $storeArray);
        $this->assertArrayHasKey('name', $storeArray);
        $this->assertArrayHasKey('description', $storeArray);
        $this->assertArrayHasKey('steps_count', $storeArray);
        $this->assertArrayHasKey('json', $storeArray);
        $this->assertArrayHasKey('is_default_flow', $storeArray);
    }

    public function test_flow_with_null_values()
    {
        $flow = new Flow(
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        $this->assertNull($flow->id);
        $this->assertNull($flow->organization_id);
        $this->assertNull($flow->name);
        $this->assertNull($flow->description);
        $this->assertNull($flow->steps_count);
        $this->assertNull($flow->json);
        $this->assertNull($flow->is_default_flow);
        $this->assertNull($flow->created_at);
        $this->assertNull($flow->updated_at);
        $this->assertNull($flow->steps);
    }

    public function test_flow_with_empty_steps()
    {
        $flow = new Flow(
            1,
            1,
            'Empty Flow',
            'Flow with no steps',
            0,
            '{}',
            false,
            Carbon::now(),
            Carbon::now(),
            []
        );

        $this->assertEquals('Empty Flow', $flow->name);
        $this->assertEquals(0, $flow->steps_count);
        $this->assertIsArray($flow->steps);
        $this->assertEmpty($flow->steps);
    }

    protected function createDomainInstance()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $steps = [
            new Step(1, 1, 1, 'Step 1', 'text', 1, null, null, true, false, true, false, false, false, '{}', null, $createdAt, $updatedAt),
            new Step(2, 1, 1, 'Step 2', 'text', 2, null, null, false, false, true, false, false, false, '{}', null, $createdAt, $updatedAt),
        ];

        return new Flow(
            1,
            1,
            'Test Flow',
            'Test Description',
            2,
            '{"flow": "data"}',
            true,
            $createdAt,
            $updatedAt,
            $steps
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'description',
            'steps_count',
            'json',
            'is_default_flow',
            'created_at',
            'updated_at',
            'steps'
        ];
    }
}
