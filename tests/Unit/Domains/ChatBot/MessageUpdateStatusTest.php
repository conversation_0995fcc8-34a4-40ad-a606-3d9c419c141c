<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Message;
use App\Enums\MessageStatus;
use Tests\TestCase;

class MessageUpdateStatusTest extends TestCase
{
    public function test_update_message_status_with_sent_calls_send_method()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        // Act
        $message->updateMessageStatus('sent');

        // Assert
        $this->assertEquals(MessageStatus::is_sent, $message->status);
        $this->assertTrue($message->is_sent);
        $this->assertFalse($message->is_fail);
        $this->assertNotNull($message->sent_at);
    }

    public function test_update_message_status_with_delivered_calls_deliver_method()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_sent
        );

        // Act
        $message->updateMessageStatus('delivered');

        // Assert
        $this->assertEquals(MessageStatus::is_delivered, $message->status);
        $this->assertTrue($message->is_delivered);
        $this->assertFalse($message->is_fail);
    }

    public function test_update_message_status_with_read_calls_mark_as_read_method()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_delivered
        );

        // Act
        $message->updateMessageStatus('read');

        // Assert
        $this->assertEquals(MessageStatus::is_read, $message->status);
        $this->assertTrue($message->is_read);
        $this->assertTrue($message->is_delivered); // Se foi lida, certamente foi entregue
        $this->assertFalse($message->is_fail);
    }

    public function test_update_message_status_with_failed_calls_fail_method()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_sending
        );

        // Act
        $message->updateMessageStatus('failed');

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $message->status);
        $this->assertTrue($message->is_fail);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
        $this->assertNotNull($message->sent_at);
    }

    public function test_update_message_status_with_unknown_status_does_nothing()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        $originalStatus = $message->status;
        $originalIsSent = $message->is_sent;
        $originalIsDelivered = $message->is_delivered;
        $originalIsFail = $message->is_fail;
        $originalIsRead = $message->is_read;

        // Act
        $message->updateMessageStatus('unknown_status');

        // Assert - Nothing should change
        $this->assertEquals($originalStatus, $message->status);
        $this->assertEquals($originalIsSent, $message->is_sent);
        $this->assertEquals($originalIsDelivered, $message->is_delivered);
        $this->assertEquals($originalIsFail, $message->is_fail);
        $this->assertEquals($originalIsRead, $message->is_read);
    }

    public function test_update_message_status_handles_empty_string()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        $originalStatus = $message->status;

        // Act
        $message->updateMessageStatus('');

        // Assert - Nothing should change
        $this->assertEquals($originalStatus, $message->status);
    }

    public function test_update_message_status_is_case_sensitive()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: null,
            message: 'Test message',
            status: MessageStatus::is_draft
        );

        $originalStatus = $message->status;

        // Act
        $message->updateMessageStatus('SENT'); // Uppercase
        $message->updateMessageStatus('Delivered'); // Mixed case

        // Assert - Nothing should change because it's case sensitive
        $this->assertEquals($originalStatus, $message->status);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
    }

    public function test_update_message_status_handles_all_valid_statuses()
    {
        // Arrange
        $validStatuses = ['sent', 'delivered', 'read', 'failed'];

        foreach ($validStatuses as $status) {
            $message = new Message(
                id: 1,
                organization_id: 1,
                campaign_id: null,
                template_id: null,
                client_id: null,
                message: 'Test message',
                status: MessageStatus::is_draft
            );

            // Act
            $message->updateMessageStatus($status);

            // Assert - Status should change for all valid statuses
            $this->assertNotEquals(MessageStatus::is_draft, $message->status, "Status should change for: {$status}");
        }
    }
}
