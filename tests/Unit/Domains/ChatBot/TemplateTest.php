<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\PhoneNumber;
use App\Enums\ChatBot\ComponentFormat;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class TemplateTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();
        $phoneNumber = new PhoneNumber(
            1,
            1,
            null, // user_id
            null, // client_id
            null, // flow_id
            '+1234567890',
            'Test Phone',
            'Test Description',
            true, // is_active
            'whatsapp_id',
            'test-token',
            $createdAt,
            $updatedAt
        );

        $template = new Template(
            1,
            1,
            1,
            1,
            1,
            'test_template',
            'UTILITY',
            'TEXT',
            'en_US',
            'library_template',
            'ext_123',
            'PENDING',
            $createdAt,
            $updatedAt,
            true,
            [],
            $phoneNumber
        );

        $this->assertEquals(1, $template->id);
        $this->assertEquals(1, $template->organization_id);
        $this->assertEquals(1, $template->phone_number_id);
        $this->assertEquals(1, $template->user_id);
        $this->assertEquals(1, $template->client_id);
        $this->assertEquals('test_template', $template->name);
        $this->assertEquals('UTILITY', $template->category);
        $this->assertEquals('TEXT', $template->parameter_format);
        $this->assertEquals('en_US', $template->language);
        $this->assertEquals('library_template', $template->library_template_name);
        $this->assertEquals('ext_123', $template->id_external);
        $this->assertEquals('PENDING', $template->status);
        $this->assertEquals($createdAt, $template->created_at);
        $this->assertEquals($updatedAt, $template->updated_at);
        $this->assertTrue($template->is_whatsapp_published);
        $this->assertIsArray($template->components);
        $this->assertInstanceOf(PhoneNumber::class, $template->phone_number);
    }

    public function test_to_array_method()
    {
        $template = $this->createDomainInstance();
        $array = $template->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals('test_template', $array['name']);
        $this->assertEquals('UTILITY', $array['category']);
        $this->assertEquals('en_US', $array['language']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertIsBool($array['is_whatsapp_published']);
    }

    public function test_is_whatsapp_published_method()
    {
        $template = $this->createDomainInstance();
        $this->assertTrue($template->isWhatsAppPublished());

        $template->is_whatsapp_published = false;
        $this->assertFalse($template->isWhatsAppPublished());
    }

    public function test_get_component_by_type()
    {
        $bodyComponent = new Component(
            1,
            1,
            null,
            1,
            'Body Component',
            'BODY',
            'Hello {{1}}',
            ComponentFormat::TEXT,
            null
        );

        $headerComponent = new Component(
            2,
            1,
            null,
            1,
            'Header Component',
            'HEADER',
            'Welcome',
            ComponentFormat::TEXT,
            null
        );

        $template = $this->createDomainInstance();
        $template->components = [$bodyComponent, $headerComponent];

        $foundBody = $template->getComponentByType('BODY');
        $foundHeader = $template->getComponentByType('HEADER');
        $notFound = $template->getComponentByType('FOOTER');

        $this->assertInstanceOf(Component::class, $foundBody);
        $this->assertEquals('BODY', $foundBody->type);
        $this->assertInstanceOf(Component::class, $foundHeader);
        $this->assertEquals('HEADER', $foundHeader->type);
        $this->assertNull($notFound);
    }

    public function test_validate_for_whatsapp_with_valid_components()
    {
        $validComponent = $this->createMockComponent(true);

        $template = $this->createDomainInstance();
        $template->components = [$validComponent];

        $this->assertTrue($template->validateForWhatsApp());
    }

    public function test_validate_for_whatsapp_with_invalid_components()
    {
        $invalidComponent = $this->createMockComponent(false);

        $template = $this->createDomainInstance();
        $template->components = [$invalidComponent];

        $this->assertFalse($template->validateForWhatsApp());
    }

    public function test_validate_for_whatsapp_with_no_components()
    {
        $template = $this->createDomainInstance();
        $template->components = [];

        $this->assertTrue($template->validateForWhatsApp());
    }

    public function test_to_whatsapp_payload()
    {
        $component = new Component(
            1,
            1,
            null,
            1,
            'Body Component',
            'BODY',
            'Hello {{1}}',
            ComponentFormat::TEXT,
            null
        );

        $template = $this->createDomainInstance();
        $template->components = [$component];

        $payload = $template->toWhatsAppPayload();

        $this->assertIsArray($payload);
        $this->assertArrayHasKey('name', $payload);
        $this->assertArrayHasKey('category', $payload);
        $this->assertArrayHasKey('language', $payload);
        $this->assertArrayHasKey('components', $payload);
        $this->assertEquals('test_template', $payload['name']);
        $this->assertEquals('UTILITY', $payload['category']);
        $this->assertEquals('en_US', $payload['language']);
        $this->assertIsArray($payload['components']);
    }

    public function test_to_whatsapp_raw_message()
    {
        $template = $this->createDomainInstance();
        $rawMessage = $template->toWhatsAppRawMessage();

        $this->assertIsString($rawMessage);
        $this->assertJson($rawMessage);

        $decoded = json_decode($rawMessage, true);
        $this->assertArrayHasKey('name', $decoded);
        $this->assertArrayHasKey('category', $decoded);
        $this->assertArrayHasKey('language', $decoded);
        $this->assertArrayHasKey('components', $decoded);
    }

    protected function createDomainInstance()
    {
        $phoneNumber = new PhoneNumber(
            1,
            1,
            null, // user_id
            null, // client_id
            null, // flow_id
            '+1234567890',
            'Test Phone',
            'Test Description',
            true, // is_active
            'whatsapp_id',
            'test-token',
            Carbon::now(),
            Carbon::now()
        );

        return new Template(
            1,
            1,
            1,
            1,
            1,
            'test_template',
            'UTILITY',
            'TEXT',
            'en_US',
            'library_template',
            'ext_123',
            'PENDING',
            Carbon::now(),
            Carbon::now(),
            true,
            [],
            $phoneNumber
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'phone_number_id',
            'user_id',
            'client_id',
            'name',
            'category',
            'parameter_format',
            'language',
            'library_template_name',
            'id_external',
            'status',
            'created_at',
            'updated_at',
            'is_whatsapp_published',
            'component',
            'phone_number'
        ];
    }

    private function createMockComponent(bool $isValid): Component
    {
        $component = $this->createMock(Component::class);
        $component->method('validateForWhatsApp')->willReturn($isValid);
        $component->method('toWhatsAppTemplatePayload')->willReturn([]);
        return $component;
    }
}
