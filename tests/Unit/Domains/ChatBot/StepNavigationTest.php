<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\StepNavigation;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class StepNavigationTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();
        
        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->step_id);
        $this->assertEquals(StepNavigation::BUTTON_CLICK, $domain->condition_type);
        $this->assertEquals(['button_id' => 'test_button'], $domain->condition_data);
        $this->assertEquals('next_step', $domain->target_step_identifier);
        $this->assertEquals(0, $domain->priority);
        $this->assertTrue($domain->is_active);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_matches_condition_button_click()
    {
        $domain = StepNavigation::createButtonClickRule(
            1, 1, 'test_button', 'next_step', 0
        );

        $this->assertTrue($domain->matchesCondition('test_button'));
        $this->assertFalse($domain->matchesCondition('other_button'));
    }

    public function test_matches_condition_text_match()
    {
        $domain = StepNavigation::createTextMatchRule(
            1, 1, 'yes', 'next_step', 0, false
        );

        $this->assertTrue($domain->matchesCondition('yes'));
        $this->assertTrue($domain->matchesCondition('YES')); // case insensitive
        $this->assertFalse($domain->matchesCondition('no'));
    }

    public function test_matches_condition_text_match_case_sensitive()
    {
        $domain = StepNavigation::createTextMatchRule(
            1, 1, 'Yes', 'next_step', 0, true
        );

        $this->assertTrue($domain->matchesCondition('Yes'));
        $this->assertFalse($domain->matchesCondition('yes')); // case sensitive
        $this->assertFalse($domain->matchesCondition('YES'));
    }

    public function test_matches_condition_regex()
    {
        $domain = StepNavigation::createRegexRule(
            1, 1, '\d+', 'next_step', 0
        );

        $this->assertTrue($domain->matchesCondition('123'));
        $this->assertTrue($domain->matchesCondition('456'));
        $this->assertFalse($domain->matchesCondition('abc'));
    }

    public function test_matches_condition_default()
    {
        $domain = StepNavigation::createDefaultRule(
            1, 1, 'next_step', 999
        );

        $this->assertTrue($domain->matchesCondition('anything'));
        $this->assertTrue($domain->matchesCondition(''));
        $this->assertTrue($domain->matchesCondition('123'));
    }

    public function test_validate_condition_data_button_click()
    {
        $domain = StepNavigation::createButtonClickRule(
            1, 1, 'test_button', 'next_step', 0
        );

        $this->assertTrue($domain->validateConditionData());

        // Test invalid data
        $domain->condition_data = [];
        $this->assertFalse($domain->validateConditionData());
    }

    public function test_validate_condition_data_text_match()
    {
        $domain = StepNavigation::createTextMatchRule(
            1, 1, 'test', 'next_step', 0
        );

        $this->assertTrue($domain->validateConditionData());

        // Test invalid data
        $domain->condition_data = ['text' => ''];
        $this->assertFalse($domain->validateConditionData());
    }

    public function test_validate_condition_data_regex()
    {
        $domain = StepNavigation::createRegexRule(
            1, 1, '\d+', 'next_step', 0
        );

        $this->assertTrue($domain->validateConditionData());

        // Test invalid regex
        $domain->condition_data = ['pattern' => '['];
        $this->assertFalse($domain->validateConditionData());
    }

    public function test_validate_condition_data_default()
    {
        $domain = StepNavigation::createDefaultRule(
            1, 1, 'next_step', 999
        );

        $this->assertTrue($domain->validateConditionData());
    }

    public function test_get_condition_types()
    {
        $types = StepNavigation::getConditionTypes();
        
        $this->assertIsArray($types);
        $this->assertContains(StepNavigation::BUTTON_CLICK, $types);
        $this->assertContains(StepNavigation::TEXT_MATCH, $types);
        $this->assertContains(StepNavigation::REGEX, $types);
        $this->assertContains(StepNavigation::DEFAULT, $types);
    }

    public function test_inactive_rule_does_not_match()
    {
        $domain = StepNavigation::createButtonClickRule(
            1, 1, 'test_button', 'next_step', 0
        );
        $domain->is_active = false;

        $this->assertFalse($domain->matchesCondition('test_button'));
    }

    public function test_button_click_with_context()
    {
        $domain = StepNavigation::createButtonClickRule(
            1, 1, 'test_button', 'next_step', 0, 'Test Button'
        );

        $context = [
            'button' => [
                'id' => 'test_button',
                'text' => 'Test Button'
            ]
        ];

        $this->assertTrue($domain->matchesCondition('anything', $context));
    }

    protected function createDomainInstance()
    {
        return new StepNavigation(
            1, // id
            1, // organization_id
            1, // step_id
            StepNavigation::BUTTON_CLICK, // condition_type
            ['button_id' => 'test_button'], // condition_data
            'next_step', // target_step_identifier
            0, // priority
            true, // is_active
            Carbon::now(), // created_at
            Carbon::now(), // updated_at
            null // step
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'step_id',
            'condition_type',
            'condition_data',
            'target_step_identifier',
            'priority',
            'is_active',
            'created_at',
            'updated_at',
        ];
    }
}
