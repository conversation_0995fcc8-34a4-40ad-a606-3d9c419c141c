<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Group;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class GroupTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Group::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Group', $domain->name);
        $this->assertEquals('Test group description', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_null_values()
    {
        $domain = new Group(null, null, null, null, null, null);

        $this->assertInstanceOf(Group::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('Test Group', $array['name']);
        $this->assertEquals('Test group description', $array['description']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
    }

    public function test_to_array_method_with_null_dates()
    {
        $domain = new Group(1, 1, 'Test Group', 'Test description', null, null);
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        $this->assertArrayHasKey('organization_id', $storeArray);
        $this->assertArrayHasKey('name', $storeArray);
        $this->assertArrayHasKey('description', $storeArray);

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals('Test Group', $storeArray['name']);
        $this->assertEquals('Test group description', $storeArray['description']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        $this->assertArrayHasKey('name', $updateArray);
        $this->assertArrayHasKey('description', $updateArray);

        $this->assertEquals('Test Group', $updateArray['name']);
        $this->assertEquals('Test group description', $updateArray['description']);
    }

    public function test_domain_with_empty_description()
    {
        $domain = new Group(1, 1, 'Group Without Description', null, Carbon::now(), Carbon::now());

        $this->assertInstanceOf(Group::class, $domain);
        $this->assertEquals('Group Without Description', $domain->name);
        $this->assertNull($domain->description);

        $array = $domain->toArray();
        $this->assertNull($array['description']);
    }

    public function test_domain_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description. ', 20);
        $domain = new Group(1, 1, 'Group With Long Description', $longDescription, Carbon::now(), Carbon::now());

        $this->assertInstanceOf(Group::class, $domain);
        $this->assertEquals($longDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($longDescription, $array['description']);
    }

    protected function createDomainInstance()
    {
        return new Group(
            1,
            1,
            'Test Group',
            'Test group description',
            Carbon::now(),
            Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'description',
            'created_at',
            'updated_at',
        ];
    }
}
