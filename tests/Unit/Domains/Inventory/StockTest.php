<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\Stock;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;
use Exception;

class StockTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->brand_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(100, $domain->quantity);
        $this->assertEquals(999.99, $domain->value);
        $this->assertEquals('Test stock description', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->product);
        $this->assertNull($domain->shop);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Stock(
            id: null,
            organization_id: 1,
            shop_id: null,
            brand_id: null,
            product_id: 1,
            quantity: 10,
            value: null,
            description: null
        );

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->brand_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(10, $domain->quantity);
        $this->assertNull($domain->value);
        $this->assertNull($domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->product);
        $this->assertNull($domain->shop);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $product = $this->createMockProduct();
        $shop = $this->createMockShop();

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 50,
            value: 499.99,
            description: 'Stock with relationships',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            product: $product,
            shop: $shop
        );

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertInstanceOf(Product::class, $domain->product);
        $this->assertInstanceOf(Shop::class, $domain->shop);
        $this->assertEquals('Test Product', $domain->product->name);
        $this->assertEquals('Test Shop', $domain->shop->name);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(1, $domain->shop_id);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['shop_id']);
        $this->assertEquals(1, $array['brand_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertEquals(100, $array['quantity']);
        $this->assertEquals(999.99, $array['value']);
        $this->assertEquals('Test stock description', $array['description']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertNull($array['product']);
        $this->assertNull($array['shop']);
    }

    public function test_to_array_with_relationships()
    {
        $product = $this->createMockProduct();
        $shop = $this->createMockShop();

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 50,
            value: 499.99,
            description: 'Stock with relationships',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            product: $product,
            shop: $shop
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['product']);
        $this->assertIsArray($array['shop']);
        $this->assertEquals('Test Product', $array['product']['name']);
        $this->assertEquals('Test Shop', $array['shop']['name']);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 999.99,
            description: 'Test stock description',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'shop_id', 'brand_id', 'product_id',
            'quantity', 'value', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['shop_id']);
        $this->assertEquals(1, $storeArray['brand_id']);
        $this->assertEquals(1, $storeArray['product_id']);
        $this->assertEquals(100, $storeArray['quantity']);
        $this->assertEquals(999.99, $storeArray['value']);
        $this->assertEquals('Test stock description', $storeArray['description']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('shop_id', $updateArray);
        $this->assertArrayNotHasKey('brand_id', $updateArray);
        $this->assertArrayNotHasKey('product_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain only updatable fields
        $expectedKeys = ['quantity', 'value', 'description'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(100, $updateArray['quantity']);
        $this->assertEquals(999.99, $updateArray['value']);
        $this->assertEquals('Test stock description', $updateArray['description']);
    }

    public function test_increase_stock_method()
    {
        $product = $this->createMockProduct();
        $product->price = 10.0;

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 50,
            value: 500.0,
            description: 'Test stock',
            product: $product
        );

        $this->assertEquals(50, $domain->quantity);
        $this->assertEquals(500.0, $domain->value);

        $domain->increaseStock(25);

        $this->assertEquals(75, $domain->quantity);
        $this->assertEquals(750.0, $domain->value); // 75 * 10.0
    }

    public function test_decrease_stock_method_successfully()
    {
        $product = $this->createMockProduct();
        $product->price = 10.0;

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock',
            product: $product
        );

        $this->assertEquals(100, $domain->quantity);
        $this->assertEquals(1000.0, $domain->value);

        $domain->decreaseStock(30);

        $this->assertEquals(70, $domain->quantity);
        $this->assertEquals(700.0, $domain->value); // 70 * 10.0
    }

    public function test_decrease_stock_method_throws_exception_when_insufficient_stock()
    {
        $product = $this->createMockProduct();
        $product->price = 10.0;

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 20,
            value: 200.0,
            description: 'Test stock',
            product: $product
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("There is not enough stock available of this product!");

        $domain->decreaseStock(30); // Trying to decrease more than available
    }

    public function test_decrease_stock_method_throws_exception_when_negative_result()
    {
        $product = $this->createMockProduct();
        $product->price = 10.0;

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 10,
            value: 100.0,
            description: 'Test stock',
            product: $product
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("There is not enough stock available of this product!");

        $domain->decreaseStock(15); // Would result in negative quantity
    }

    public function test_refresh_from_price_method()
    {
        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 50,
            value: 500.0,
            description: 'Test stock'
        );

        $this->assertEquals(500.0, $domain->value);

        $domain->refreshFromPrice(15.0);

        $this->assertEquals(750.0, $domain->value); // 50 * 15.0
        $this->assertEquals(50, $domain->quantity); // Quantity should remain unchanged
    }

    public function test_stock_with_zero_quantity()
    {
        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Empty stock'
        );

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertEquals(0, $domain->quantity);
        $this->assertEquals(0.0, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(0, $array['quantity']);
        $this->assertEquals(0.0, $array['value']);
    }

    public function test_stock_with_high_quantity()
    {
        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 9999999.99,
            description: 'Large stock'
        );

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertEquals(999999, $domain->quantity);
        $this->assertEquals(9999999.99, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(999999, $array['quantity']);
        $this->assertEquals(9999999.99, $array['value']);
    }

    public function test_stock_operations_with_zero_price()
    {
        $product = $this->createMockProduct();
        $product->price = 0.0;

        $domain = new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 0.0,
            description: 'Free product stock',
            product: $product
        );

        // Increase stock with zero price
        $domain->increaseStock(50);
        $this->assertEquals(150, $domain->quantity);
        $this->assertEquals(0.0, $domain->value); // 150 * 0.0

        // Decrease stock with zero price
        $domain->decreaseStock(25);
        $this->assertEquals(125, $domain->quantity);
        $this->assertEquals(0.0, $domain->value); // 125 * 0.0

        // Refresh from zero price
        $domain->refreshFromPrice(0.0);
        $this->assertEquals(0.0, $domain->value); // 125 * 0.0
    }

    protected function createDomainInstance()
    {
        return new Stock(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 999.99,
            description: 'Test stock description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'shop_id',
            'brand_id',
            'product_id',
            'quantity',
            'value',
            'description',
            'created_at',
            'updated_at',
            'product',
            'shop'
        ];
    }

    private function createMockProduct(): Product
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: Carbon::now(),
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockShop(): Shop
    {
        return new Shop(
            id: 1,
            organization_id: 1,
            name: 'Test Shop',
            description: 'Test shop description',
            is_active: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }
}
