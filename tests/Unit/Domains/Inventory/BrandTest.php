<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Brand;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class BrandTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Brand', $domain->name);
        $this->assertEquals('Test brand description', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Brand(
            id: null,
            organization_id: 1,
            name: 'Minimal Brand',
            description: null
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Minimal Brand', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('Test Brand', $array['name']);
        $this->assertEquals('Test brand description', $array['description']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: 'Null Dates Brand',
            description: 'Brand with null dates',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = ['organization_id', 'name', 'description'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals('Test Brand', $storeArray['name']);
        $this->assertEquals('Test brand description', $storeArray['description']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = ['name', 'description'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals('Test Brand', $updateArray['name']);
        $this->assertEquals('Test brand description', $updateArray['description']);
    }

    public function test_brand_with_empty_description()
    {
        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: 'No Description Brand',
            description: ''
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals('', $domain->description);

        $array = $domain->toArray();
        $this->assertEquals('', $array['description']);
    }

    public function test_brand_with_long_name()
    {
        $longName = str_repeat('A', 255);

        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: $longName,
            description: 'Brand with long name'
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($longName, $domain->name);

        $array = $domain->toArray();
        $this->assertEquals($longName, $array['name']);
    }

    public function test_brand_with_long_description()
    {
        $longDescription = str_repeat('B', 1000);

        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: 'Long Description Brand',
            description: $longDescription
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($longDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($longDescription, $array['description']);
    }

    public function test_brand_with_special_characters()
    {
        $specialName = 'Brand & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';

        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: $specialName,
            description: $specialDescription
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($specialName, $domain->name);
        $this->assertEquals($specialDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($specialName, $array['name']);
        $this->assertEquals($specialDescription, $array['description']);
    }

    public function test_brand_with_unicode_characters()
    {
        $unicodeName = 'Bränd Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';

        $domain = new Brand(
            id: 1,
            organization_id: 1,
            name: $unicodeName,
            description: $unicodeDescription
        );

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($unicodeName, $domain->name);
        $this->assertEquals($unicodeDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($unicodeName, $array['name']);
        $this->assertEquals($unicodeDescription, $array['description']);
    }

    protected function createDomainInstance()
    {
        return new Brand(
            id: 1,
            organization_id: 1,
            name: 'Test Brand',
            description: 'Test brand description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'description',
            'created_at',
            'updated_at'
        ];
    }
}
