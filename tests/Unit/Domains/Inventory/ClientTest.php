<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Client;
use App\Services\ASAAS\Domains\AsaasClient;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ClientTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('<PERSON>', $domain->name);
        $this->assertEquals('+1234567890', $domain->phone);
        $this->assertEquals('<EMAIL>', $domain->email);
        $this->assertEquals('Engineer', $domain->profession);
        $this->assertEquals('1990-01-01', $domain->birthdate);
        $this->assertEquals('12345678901', $domain->cpf);
        $this->assertNull($domain->cnpj);
        $this->assertEquals('Software Development', $domain->service);
        $this->assertEquals('123 Main St', $domain->address);
        $this->assertEquals('123', $domain->number);
        $this->assertEquals('Downtown', $domain->neighborhood);
        $this->assertEquals('12345-678', $domain->cep);
        $this->assertEquals('Apt 1', $domain->complement);
        $this->assertEquals('single', $domain->civil_state);
        $this->assertEquals('Test client description', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->asaas);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Client(
            id: null,
            organization_id: 1,
            name: 'Minimal Client',
            phone: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Minimal Client', $domain->name);
        $this->assertNull($domain->phone);
        $this->assertNull($domain->email);
        $this->assertNull($domain->cpf);
        $this->assertNull($domain->cnpj);
    }

    public function test_domain_instantiation_with_asaas_client()
    {
        $asaasClient = $this->createMockAsaasClient();
        $domain = new Client(
            id: 1,
            organization_id: 1,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client description',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            asaas: $asaasClient
        );

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertInstanceOf(AsaasClient::class, $domain->asaas);
        $this->assertEquals('cus_123456', $domain->asaas->asaas_customer_id);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('John Doe', $array['name']);
        $this->assertEquals('+1234567890', $array['phone']);
        $this->assertEquals('<EMAIL>', $array['email']);
        $this->assertEquals('Engineer', $array['profession']);
        $this->assertEquals('1990-01-01', $array['birthdate']);
        $this->assertEquals('12345678901', $array['cpf']);
        $this->assertNull($array['cnpj']);
        $this->assertEquals('Software Development', $array['service']);
        $this->assertEquals('123 Main St', $array['address']);
        $this->assertEquals('123', $array['number']);
        $this->assertEquals('Downtown', $array['neighborhood']);
        $this->assertEquals('12345-678', $array['cep']);
        $this->assertEquals('Apt 1', $array['complement']);
        $this->assertEquals('single', $array['civil_state']);
        $this->assertEquals('Test client description', $array['description']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertArrayHasKey('international_transformed_phone', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Client(
            id: 1,
            organization_id: 1,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client description',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    protected function createDomainInstance()
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'phone',
            'email',
            'profession',
            'birthdate',
            'cpf',
            'cnpj',
            'service',
            'address',
            'number',
            'neighborhood',
            'cep',
            'complement',
            'civil_state',
            'description',
            'created_at',
            'updated_at',
            'international_transformed_phone'
        ];
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'name', 'phone', 'email', 'profession',
            'birthdate', 'cpf', 'cnpj', 'service', 'address', 'number',
            'neighborhood', 'cep', 'complement', 'civil_state', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals('John Doe', $storeArray['name']);
        $this->assertEquals('+1234567890', $storeArray['phone']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'name', 'phone', 'email', 'profession', 'birthdate', 'cpf', 'cnpj',
            'service', 'address', 'number', 'neighborhood', 'cep', 'complement',
            'civil_state', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals('John Doe', $updateArray['name']);
        $this->assertEquals('+1234567890', $updateArray['phone']);
    }

    public function test_international_phone_method()
    {
        // Test with Brazilian phone
        $domain = new Client(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: '11999999999',
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $internationalPhone = $domain->internationalPhone();
        $this->assertIsString($internationalPhone);

        // Test with null phone
        $domainWithNullPhone = new Client(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $internationalPhoneNull = $domainWithNullPhone->internationalPhone();
        $this->assertEquals('', $internationalPhoneNull);
    }

    public function test_company_client_instantiation()
    {
        $domain = new Client(
            id: 1,
            organization_id: 1,
            name: 'ACME Corp',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: '12345678000195',
            service: 'Technology Services',
            address: '123 Business Ave',
            number: '456',
            neighborhood: 'Business District',
            cep: '12345-678',
            complement: 'Suite 100',
            civil_state: null,
            description: 'Technology company'
        );

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertEquals('ACME Corp', $domain->name);
        $this->assertNull($domain->cpf);
        $this->assertEquals('12345678000195', $domain->cnpj);
        $this->assertNull($domain->profession);
        $this->assertNull($domain->birthdate);
        $this->assertNull($domain->civil_state);
    }

    private function createMockAsaasClient(): AsaasClient
    {
        return new AsaasClient(
            id: 1,
            client_id: 1,
            organization_id: 1,
            asaas_customer_id: 'cus_123456',
            asaas_synced_at: Carbon::now(),
            asaas_sync_errors: null,
            sync_status: 'synced',
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+1234567890',
            mobile_phone: null,
            address: '123 Main St',
            address_number: '123',
            complement: 'Apt 1',
            province: 'Downtown',
            city_name: 'Test City',
            state: 'SP',
            country: 'Brasil',
            postal_code: '12345-678',
            cpf_cnpj: '12345678901',
            person_type: 'FISICA',
            external_reference: null,
            notification_disabled: false,
            additional_emails: null,
            observations: null,
            foreign_customer: false,
            deleted: false,
            client: null
        );
    }
}
