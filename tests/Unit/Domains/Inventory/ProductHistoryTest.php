<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\ProductHistory;
use App\Domains\Inventory\Product;
use App\Domains\User;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ProductHistoryTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('price', $domain->field);
        $this->assertEquals('Preço', $domain->alias);
        $this->assertEquals('10.50', $domain->old);
        $this->assertEquals('15.75', $domain->new);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new ProductHistory(
            id: null,
            user_id: 1,
            product_id: 1,
            field: 'name',
            alias: 'Nome',
            old: 'Old Product Name',
            new: 'New Product Name'
        );

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('name', $domain->field);
        $this->assertEquals('Nome', $domain->alias);
        $this->assertEquals('Old Product Name', $domain->old);
        $this->assertEquals('New Product Name', $domain->new);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->user);
        $this->assertNull($domain->product);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['user_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertEquals('price', $array['field']);
        $this->assertEquals('Preço', $array['alias']);
        $this->assertEquals('10.50', $array['old']);
        $this->assertEquals('15.75', $array['new']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'user_id', 'product_id', 'field', 'alias', 'old', 'new'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['user_id']);
        $this->assertEquals(1, $storeArray['product_id']);
        $this->assertEquals('price', $storeArray['field']);
        $this->assertEquals('Preço', $storeArray['alias']);
        $this->assertEquals('10.50', $storeArray['old']);
        $this->assertEquals('15.75', $storeArray['new']);
    }

    public function test_product_history_with_different_fields()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
            ['field' => 'unity', 'alias' => 'Unidade', 'old' => '1', 'new' => '2'],
        ];

        foreach ($fields as $fieldData) {
            $domain = new ProductHistory(
                id: 1,
                user_id: 1,
                product_id: 1,
                field: $fieldData['field'],
                alias: $fieldData['alias'],
                old: $fieldData['old'],
                new: $fieldData['new']
            );

            $this->assertEquals($fieldData['field'], $domain->field);
            $this->assertEquals($fieldData['alias'], $domain->alias);
            $this->assertEquals($fieldData['old'], $domain->old);
            $this->assertEquals($fieldData['new'], $domain->new);

            $array = $domain->toArray();
            $this->assertEquals($fieldData['field'], $array['field']);
            $this->assertEquals($fieldData['alias'], $array['alias']);
            $this->assertEquals($fieldData['old'], $array['old']);
            $this->assertEquals($fieldData['new'], $array['new']);
        }
    }

    public function test_product_history_with_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '0.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $priceChange) {
            $domain = new ProductHistory(
                id: 1,
                user_id: 1,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: $priceChange['old'],
                new: $priceChange['new']
            );

            $this->assertEquals($priceChange['old'], $domain->old);
            $this->assertEquals($priceChange['new'], $domain->new);

            $array = $domain->toArray();
            $this->assertEquals($priceChange['old'], $array['old']);
            $this->assertEquals($priceChange['new'], $array['new']);
        }
    }

    public function test_to_array_with_relationships()
    {
        $user = new User(
            id: 1,
            profile_id: 1,
            organization_id: 1,
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser',
            email: '<EMAIL>',
            password: 'hashed_password',
            cpf: '12345678901',
            phone: '11999999999'
        );

        $product = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 15.75,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $domain = new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            user: $user,
            product: $product
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['user']);
        $this->assertIsArray($array['product']);
        $this->assertEquals('Test User', $array['user']['name']);
        $this->assertEquals('Test Product', $array['product']['name']);
    }

    public function test_to_array_with_null_relationships()
    {
        $domain = new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75',
            user: null,
            product: null
        );

        $array = $domain->toArray();

        $this->assertNull($array['user']);
        $this->assertNull($array['product']);
    }

    public function test_product_history_with_long_values()
    {
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);

        $domain = new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: $longOldValue,
            new: $longNewValue
        );

        $this->assertEquals($longOldValue, $domain->old);
        $this->assertEquals($longNewValue, $domain->new);

        $array = $domain->toArray();
        $this->assertEquals($longOldValue, $array['old']);
        $this->assertEquals($longNewValue, $array['new']);
    }

    public function test_product_history_with_null_values()
    {
        $domain = new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: null,
            new: null
        );

        $this->assertNull($domain->old);
        $this->assertNull($domain->new);

        $array = $domain->toArray();
        $this->assertNull($array['old']);
        $this->assertNull($array['new']);
    }

    public function test_product_history_with_different_user_and_product_ids()
    {
        $testCases = [
            ['user_id' => 1, 'product_id' => 1],
            ['user_id' => 5, 'product_id' => 3],
            ['user_id' => 10, 'product_id' => 7],
            ['user_id' => 999, 'product_id' => 888],
        ];

        foreach ($testCases as $testCase) {
            $domain = new ProductHistory(
                id: 1,
                user_id: $testCase['user_id'],
                product_id: $testCase['product_id'],
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            );

            $this->assertEquals($testCase['user_id'], $domain->user_id);
            $this->assertEquals($testCase['product_id'], $domain->product_id);

            $array = $domain->toArray();
            $this->assertEquals($testCase['user_id'], $array['user_id']);
            $this->assertEquals($testCase['product_id'], $array['product_id']);
        }
    }

    protected function createDomainInstance()
    {
        return new ProductHistory(
            id: 1,
            user_id: 1,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'user_id',
            'product_id',
            'field',
            'alias',
            'old',
            'new',
            'created_at',
            'updated_at',
            'user',
            'product'
        ];
    }
}
