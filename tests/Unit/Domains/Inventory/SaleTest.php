<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Sale;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\Client;
use App\Domains\Inventory\Item;
use App\Domains\User;
use App\Services\ASAAS\Domains\AsaasSale;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class SaleTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(100.50, $domain->total_value);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Sale(
            id: null,
            organization_id: 1,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 50.00
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->client_id);
        $this->assertEquals(50.00, $domain->total_value);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $user = new User(
            id: 1,
            profile_id: 1,
            organization_id: 1,
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser',
            email: '<EMAIL>',
            password: 'password',
            cpf: '12345678901',
            phone: '11999999999'
        );

        $shop = new Shop(
            id: 1,
            organization_id: 1,
            name: 'Test Shop',
            description: 'Test shop description',
            is_active: true
        );

        $client = new Client(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: '123456789',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: '12345678901',
            cnpj: null,
            service: null,
            address: 'Test Address',
            number: '123',
            neighborhood: 'Test Neighborhood',
            cep: '12345678',
            complement: null,
            civil_state: null,
            description: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );

        $items = [
            new Item(
                id: 1,
                organization_id: 1,
                sale_id: 1,
                product_id: 1,
                quantity: 2,
                value: 50.00
            )
        ];

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            user: $user,
            shop: $shop,
            client: $client,
            items: $items
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertInstanceOf(User::class, $domain->user);
        $this->assertInstanceOf(Shop::class, $domain->shop);
        $this->assertInstanceOf(Client::class, $domain->client);
        $this->assertIsArray($domain->items);
        $this->assertCount(1, $domain->items);
        $this->assertInstanceOf(Item::class, $domain->items[0]);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['user_id']);
        $this->assertEquals(1, $array['shop_id']);
        $this->assertEquals(1, $array['client_id']);
        $this->assertEquals(100.50, $array['total_value']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = ['organization_id', 'user_id', 'shop_id', 'client_id', 'total_value'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['user_id']);
        $this->assertEquals(1, $storeArray['shop_id']);
        $this->assertEquals(1, $storeArray['client_id']);
        $this->assertEquals(100.50, $storeArray['total_value']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = ['user_id', 'shop_id', 'client_id', 'total_value'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(1, $updateArray['user_id']);
        $this->assertEquals(1, $updateArray['shop_id']);
        $this->assertEquals(1, $updateArray['client_id']);
        $this->assertEquals(100.50, $updateArray['total_value']);
    }

    public function test_sale_with_zero_total_value()
    {
        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertEquals(0.00, $domain->total_value);

        $array = $domain->toArray();
        $this->assertEquals(0.00, $array['total_value']);
    }

    public function test_sale_with_high_total_value()
    {
        $highValue = 999999.99;

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertEquals($highValue, $domain->total_value);

        $array = $domain->toArray();
        $this->assertEquals($highValue, $array['total_value']);
    }

    public function test_sale_with_decimal_total_value()
    {
        $decimalValue = 123.456;

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $decimalValue
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertEquals($decimalValue, $domain->total_value);

        $array = $domain->toArray();
        $this->assertEquals($decimalValue, $array['total_value']);
    }

    public function test_sale_with_null_optional_fields()
    {
        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 100.00
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->shop);
        $this->assertNull($domain->client);

        $array = $domain->toArray();
        $this->assertNull($array['shop_id']);
        $this->assertNull($array['client_id']);
    }

    public function test_sale_with_asaas_integration()
    {
        $asaasSale = new AsaasSale(
            id: 1,
            sale_id: 1,
            organization_id: 1,
            client_id: 1,
            asaas_payment_id: 'pay_123',
            asaas_customer_id: 'cus_123',
            value: 100.50,
            net_value: 95.50,
            original_value: 100.50,
            interest_value: 0.00,
            discount_value: 0.00,
            description: 'Test payment',
            billing_type: 'BOLETO',
            due_date: Carbon::now()->addDays(7),
            payment_date: null,
            original_due_date: null,
            client_payment_date: null,
            status: 'PENDING',
            invoice_url: null,
            invoice_number: null,
            bank_slip_url: null,
            pix_qr_code_id: null,
            external_reference: '1',
            installment_id: null,
            installment_count: null,
            installment_value: null,
            installment_number: null,
            credit_date: null,
            estimated_credit_date: null,
            anticipated: false,
            anticipable: false,
            can_be_paid_after_due_date: true,
            deleted: false,
            nosso_numero: null,
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            asaas_webhook_data: null,
            discount_config: null,
            fine_config: null,
            interest_config: null,
            split_config: null,
            credit_card_data: null,
            chargeback_data: null,
            escrow_data: null,
            refunds_data: null
        );

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            asaas: $asaasSale
        );

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertInstanceOf(AsaasSale::class, $domain->asaas);
        $this->assertEquals('pay_123', $domain->asaas->asaas_payment_id);
    }

    public function test_has_asaas_payment_method()
    {
        // Create mock AsaasSale that has ASAAS integration
        $asaasSale = $this->createMockObject(AsaasSale::class);
        $asaasSale->method('hasAsaasIntegration')->willReturn(true);

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            asaas: $asaasSale
        );

        $this->assertTrue($domain->hasAsaasPayment());
    }

    public function test_has_asaas_payment_method_without_asaas()
    {
        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            asaas: null
        );

        $this->assertFalse($domain->hasAsaasPayment());
    }

    public function test_is_paid_method()
    {
        // Create mock AsaasSale that is paid
        $asaasSale = $this->createMockObject(AsaasSale::class);
        $asaasSale->method('isPaid')->willReturn(true);

        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            asaas: $asaasSale
        );

        $this->assertTrue($domain->isPaid());
    }

    public function test_is_paid_method_without_asaas()
    {
        $domain = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            asaas: null
        );

        $this->assertFalse($domain->isPaid());
    }

    protected function createDomainInstance()
    {
        return new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'user_id',
            'shop_id',
            'client_id',
            'total_value',
            'created_at',
            'updated_at'
        ];
    }

    private function createMockObject($className)
    {
        return $this->getMockBuilder($className)
                    ->disableOriginalConstructor()
                    ->getMock();
    }
}
