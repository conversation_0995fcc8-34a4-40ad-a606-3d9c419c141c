<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\StockEntry;
use App\Domains\Inventory\Product;
use App\Domains\User;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class StockEntryTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->brand_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(1, $domain->batch_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(1, $domain->project_id);
        $this->assertEquals(100, $domain->quantity);
        $this->assertEquals(1500.50, $domain->value);
        $this->assertEquals('Test stock entry', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new StockEntry(
            id: null,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 50,
            value: 750.25,
            description: null
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(50, $domain->quantity);
        $this->assertEquals(750.25, $domain->value);
        $this->assertNull($domain->description);
        $this->assertNull($domain->batch_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->project_id);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['shop_id']);
        $this->assertEquals(1, $array['user_id']);
        $this->assertEquals(1, $array['brand_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertEquals(1, $array['batch_id']);
        $this->assertEquals(1, $array['client_id']);
        $this->assertEquals(1, $array['project_id']);
        $this->assertEquals(100, $array['quantity']);
        $this->assertEquals(1500.50, $array['value']);
        $this->assertEquals('Test stock entry', $array['description']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Null dates entry',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'shop_id', 'user_id', 'brand_id', 'product_id',
            'batch_id', 'client_id', 'project_id', 'quantity', 'value', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(100, $storeArray['quantity']);
        $this->assertEquals(1500.50, $storeArray['value']);
        $this->assertEquals('Test stock entry', $storeArray['description']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('shop_id', $updateArray);
        $this->assertArrayNotHasKey('user_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'brand_id', 'product_id', 'batch_id', 'client_id', 'project_id',
            'quantity', 'value', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(100, $updateArray['quantity']);
        $this->assertEquals(1500.50, $updateArray['value']);
        $this->assertEquals('Test stock entry', $updateArray['description']);
    }

    public function test_calculate_value_method()
    {
        $product = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 15.50,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null, // Will be calculated
            description: 'Calculate value test',
            product: $product
        );

        $domain->calculateValue();

        $this->assertEquals(1550.0, $domain->value); // 15.50 * 100
    }

    public function test_calculate_value_with_different_quantities()
    {
        $product = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 25.75,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: null,
            description: 'Different quantity test',
            product: $product
        );

        $domain->calculateValue();

        $this->assertEquals(1287.5, $domain->value); // 25.75 * 50
    }

    public function test_stock_entry_with_null_optional_fields()
    {
        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 25,
            value: 375.0,
            description: null
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertNull($domain->batch_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->project_id);
        $this->assertNull($domain->description);

        $array = $domain->toArray();
        $this->assertNull($array['batch_id']);
        $this->assertNull($array['client_id']);
        $this->assertNull($array['project_id']);
        $this->assertNull($array['description']);
    }

    public function test_stock_entry_with_zero_quantity()
    {
        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity entry'
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals(0, $domain->quantity);
        $this->assertEquals(0.0, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(0, $array['quantity']);
        $this->assertEquals(0.0, $array['value']);
    }

    public function test_stock_entry_with_high_quantity()
    {
        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High quantity entry'
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals(999999, $domain->quantity);
        $this->assertEquals(99999999.99, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(999999, $array['quantity']);
        $this->assertEquals(99999999.99, $array['value']);
    }

    public function test_stock_entry_with_decimal_values()
    {
        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value entry'
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals(33, $domain->quantity);
        $this->assertEquals(123.456789, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(33, $array['quantity']);
        $this->assertEquals(123.456789, $array['value']);
    }

    public function test_stock_entry_with_special_characters_in_description()
    {
        $specialDescription = 'Entry with special chars: @#$%^&*()';

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 10,
            value: 150.0,
            description: $specialDescription
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals($specialDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($specialDescription, $array['description']);
    }

    public function test_stock_entry_with_unicode_characters_in_description()
    {
        $unicodeDescription = 'Entrada con caracteres especiales: ñáéíóú 中文';

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 15,
            value: 225.0,
            description: $unicodeDescription
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals($unicodeDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($unicodeDescription, $array['description']);
    }

    public function test_stock_entry_with_long_description()
    {
        $longDescription = str_repeat('A', 1000);

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 20,
            value: 300.0,
            description: $longDescription
        );

        $this->assertInstanceOf(StockEntry::class, $domain);
        $this->assertEquals($longDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($longDescription, $array['description']);
    }

    public function test_to_array_with_relationships()
    {
        $user = new User(
            id: 1,
            profile_id: 1,
            organization_id: 1,
            first_name: 'Test',
            last_name: 'User',
            username: 'testuser',
            email: '<EMAIL>',
            password: 'password',
            cpf: '12345678901',
            phone: '11999999999'
        );

        $product = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 15.50,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $domain = new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Entry with relationships',
            user: $user,
            product: $product
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['user']);
        $this->assertIsArray($array['product']);
        $this->assertEquals('Test User', $array['user']['name']);
        $this->assertEquals('Test Product', $array['product']['name']);
    }

    protected function createDomainInstance()
    {
        return new StockEntry(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Test stock entry',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'shop_id',
            'user_id',
            'brand_id',
            'product_id',
            'batch_id',
            'client_id',
            'project_id',
            'quantity',
            'value',
            'description',
            'created_at',
            'updated_at',
            'user',
            'product',
            'project',
            'batch',
            'shop'
        ];
    }
}
