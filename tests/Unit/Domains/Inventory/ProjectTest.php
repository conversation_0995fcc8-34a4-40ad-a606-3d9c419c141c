<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\Client;
use App\Domains\Inventory\CustomProduct;
use App\Domains\Inventory\Project;
use App\Domains\Inventory\ProjectProduct;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ProjectTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Project::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(1, $domain->budget_id);
        $this->assertEquals('Test Project', $domain->name);
        $this->assertEquals('Test project description', $domain->description);
        $this->assertEquals(10000.50, $domain->value);
        $this->assertEquals(7500.25, $domain->cost);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->client);
        $this->assertNull($domain->budget);
        $this->assertNull($domain->products);
        $this->assertNull($domain->customProducts);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Project(
            id: null,
            organization_id: 1,
            client_id: null,
            budget_id: null,
            name: 'Minimal Project',
            description: null,
            value: null,
            cost: null
        );

        $this->assertInstanceOf(Project::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->budget_id);
        $this->assertEquals('Minimal Project', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $client = $this->createMockClient();
        $budget = $this->createMockBudget();
        $products = [$this->createMockProjectProduct()];
        $customProducts = [$this->createMockCustomProduct()];

        $domain = new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Project with Relationships',
            description: 'Project with all relationships',
            value: 15000.00,
            cost: 12000.00,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            client: $client,
            budget: $budget,
            products: $products,
            customProducts: $customProducts
        );

        $this->assertInstanceOf(Project::class, $domain);
        $this->assertInstanceOf(Client::class, $domain->client);
        $this->assertInstanceOf(Budget::class, $domain->budget);
        $this->assertIsArray($domain->products);
        $this->assertCount(1, $domain->products);
        $this->assertInstanceOf(ProjectProduct::class, $domain->products[0]);
        $this->assertIsArray($domain->customProducts);
        $this->assertCount(1, $domain->customProducts);
        $this->assertInstanceOf(CustomProduct::class, $domain->customProducts[0]);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['client_id']);
        $this->assertEquals(1, $array['budget_id']);
        $this->assertEquals('Test Project', $array['name']);
        $this->assertEquals('Test project description', $array['description']);
        $this->assertEquals(10000.50, $array['value']);
        $this->assertEquals(7500.25, $array['cost']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertNull($array['client']);
        $this->assertNull($array['budget']);
        $this->assertNull($array['products']);
        $this->assertNull($array['customProducts']);
    }

    public function test_to_array_with_relationships()
    {
        $client = $this->createMockClient();
        $budget = $this->createMockBudget();
        $products = [$this->createMockProjectProduct()];
        $customProducts = [$this->createMockCustomProduct()];

        $domain = new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Project with Relationships',
            description: 'Project with all relationships',
            value: 15000.00,
            cost: 12000.00,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            client: $client,
            budget: $budget,
            products: $products,
            customProducts: $customProducts
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['client']);
        $this->assertEquals('Test Client', $array['client']['name']);
        $this->assertIsArray($array['budget']);
        $this->assertEquals('Test Budget', $array['budget']['name']);
        $this->assertIsArray($array['products']);
        $this->assertCount(1, $array['products']);
        $this->assertIsArray($array['customProducts']);
        $this->assertCount(1, $array['customProducts']);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'client_id', 'budget_id', 'name',
            'description', 'value', 'cost'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['client_id']);
        $this->assertEquals(1, $storeArray['budget_id']);
        $this->assertEquals('Test Project', $storeArray['name']);
        $this->assertEquals('Test project description', $storeArray['description']);
        $this->assertEquals(10000.50, $storeArray['value']);
        $this->assertEquals(7500.25, $storeArray['cost']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'client_id', 'budget_id', 'name', 'description', 'value', 'cost'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(1, $updateArray['client_id']);
        $this->assertEquals(1, $updateArray['budget_id']);
        $this->assertEquals('Test Project', $updateArray['name']);
        $this->assertEquals('Test project description', $updateArray['description']);
        $this->assertEquals(10000.50, $updateArray['value']);
        $this->assertEquals(7500.25, $updateArray['cost']);
    }

    public function test_project_with_zero_values()
    {
        $domain = new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Project with zero values',
            value: 0.0,
            cost: 0.0
        );

        $this->assertInstanceOf(Project::class, $domain);
        $this->assertEquals(0.0, $domain->value);
        $this->assertEquals(0.0, $domain->cost);

        $array = $domain->toArray();
        $this->assertEquals(0.0, $array['value']);
        $this->assertEquals(0.0, $array['cost']);
    }

    public function test_project_with_negative_values()
    {
        $domain = new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Negative Value Project',
            description: 'Project with negative values',
            value: -1000.0,
            cost: -500.0
        );

        $this->assertInstanceOf(Project::class, $domain);
        $this->assertEquals(-1000.0, $domain->value);
        $this->assertEquals(-500.0, $domain->cost);
    }

    protected function createDomainInstance()
    {
        return new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'client_id',
            'budget_id',
            'name',
            'description',
            'value',
            'cost',
            'created_at',
            'updated_at',
            'client',
            'budget',
            'products',
            'customProducts'
        ];
    }

    private function createMockClient(): Client
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );
    }

    private function createMockBudget(): Budget
    {
        return new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 15000.00,
            cost: 12000.00,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockProjectProduct(): ProjectProduct
    {
        return new ProjectProduct(
            id: 1,
            project_id: 1,
            product_id: 1,
            quantity: 5,
            value: 100.00,
            description: 'Test project product',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockCustomProduct(): CustomProduct
    {
        return new CustomProduct(
            id: 1,
            project_id: 1,
            budget_id: null,
            quantity: 2,
            value: 250.00,
            description: 'Test custom product',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }
}
