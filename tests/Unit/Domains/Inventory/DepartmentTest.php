<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Department;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class DepartmentTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Department::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Department', $domain->name);
        $this->assertTrue($domain->is_active);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_null_values()
    {
        $domain = new Department(null, null, null, null, null, null);

        $this->assertInstanceOf(Department::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->is_active);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('Test Department', $array['name']);
        $this->assertTrue($array['is_active']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
    }

    public function test_to_array_method_with_null_dates()
    {
        $domain = new Department(1, 1, 'Test Department', true, null, null);
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        $this->assertArrayHasKey('organization_id', $storeArray);
        $this->assertArrayHasKey('name', $storeArray);
        $this->assertArrayHasKey('is_active', $storeArray);

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals('Test Department', $storeArray['name']);
        $this->assertTrue($storeArray['is_active']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        $this->assertArrayHasKey('name', $updateArray);
        $this->assertArrayHasKey('is_active', $updateArray);

        $this->assertEquals('Test Department', $updateArray['name']);
        $this->assertTrue($updateArray['is_active']);
    }

    protected function createDomainInstance()
    {
        return new Department(
            1,
            1,
            'Test Department',
            true,
            Carbon::now(),
            Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'is_active',
            'created_at',
            'updated_at',
        ];
    }
}
