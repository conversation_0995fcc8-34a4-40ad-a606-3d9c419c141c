<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Brand;
use App\Domains\Inventory\Product;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ProductTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->brand_id);
        $this->assertEquals('Test Product', $domain->name);
        $this->assertEquals('1234567890123', $domain->barcode);
        $this->assertEquals('Test product description', $domain->description);
        $this->assertEquals(99.99, $domain->price);
        $this->assertEquals(1, $domain->unity);
        $this->assertInstanceOf(Carbon::class, $domain->last_priced_at);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->brand);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Product(
            id: null,
            organization_id: 1,
            brand_id: null,
            name: 'Minimal Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: null
        );

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->brand_id);
        $this->assertEquals('Minimal Product', $domain->name);
        $this->assertNull($domain->barcode);
        $this->assertNull($domain->description);
        $this->assertNull($domain->price);
        $this->assertNull($domain->unity);
        $this->assertNull($domain->last_priced_at);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->brand);
    }

    public function test_domain_instantiation_with_brand_relationship()
    {
        $brand = $this->createMockBrand();

        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Product with Brand',
            barcode: '1234567890123',
            description: 'Product with brand relationship',
            price: 149.99,
            unity: 2,
            last_priced_at: Carbon::now(),
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            brand: $brand
        );

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertInstanceOf(Brand::class, $domain->brand);
        $this->assertEquals('Test Brand', $domain->brand->name);
        $this->assertEquals(1, $domain->brand_id);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['brand_id']);
        $this->assertEquals('Test Product', $array['name']);
        $this->assertEquals('1234567890123', $array['barcode']);
        $this->assertEquals('Test product description', $array['description']);
        $this->assertEquals(99.99, $array['price']);
        $this->assertEquals(1, $array['unity']);
        $this->assertArrayHasKey('last_priced_at', $array);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertNull($array['brand']);
    }

    public function test_to_array_with_brand_relationship()
    {
        $brand = $this->createMockBrand();

        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Product with Brand',
            barcode: '1234567890123',
            description: 'Product with brand relationship',
            price: 149.99,
            unity: 2,
            last_priced_at: Carbon::now(),
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            brand: $brand
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['brand']);
        $this->assertEquals('Test Brand', $array['brand']['name']);
        $this->assertEquals('Test brand description', $array['brand']['description']);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: null,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['last_priced_at']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['last_priced_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'brand_id', 'name', 'barcode',
            'description', 'price', 'unity', 'last_priced_at'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['brand_id']);
        $this->assertEquals('Test Product', $storeArray['name']);
        $this->assertEquals('1234567890123', $storeArray['barcode']);
        $this->assertEquals('Test product description', $storeArray['description']);
        $this->assertEquals(99.99, $storeArray['price']);
        $this->assertEquals(1, $storeArray['unity']);
        $this->assertIsString($storeArray['last_priced_at']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'brand_id', 'name', 'barcode', 'description', 'price', 'unity'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(1, $updateArray['brand_id']);
        $this->assertEquals('Test Product', $updateArray['name']);
        $this->assertEquals('1234567890123', $updateArray['barcode']);
        $this->assertEquals('Test product description', $updateArray['description']);
        $this->assertEquals(99.99, $updateArray['price']);
        $this->assertEquals(1, $updateArray['unity']);

        // last_priced_at should be included when not null
        $this->assertArrayHasKey('last_priced_at', $updateArray);
    }

    public function test_to_update_array_method_without_last_priced_at()
    {
        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: null
        );

        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('last_priced_at', $updateArray);
    }

    public function test_update_last_priced_at_method()
    {
        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: null
        );

        $this->assertNull($domain->last_priced_at);

        $domain->updateLastPricedAt();

        $this->assertInstanceOf(Carbon::class, $domain->last_priced_at);
        $this->assertTrue($domain->last_priced_at->isToday());
    }

    public function test_product_with_zero_price()
    {
        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Product with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertEquals(0.0, $domain->price);

        $array = $domain->toArray();
        $this->assertEquals(0.0, $array['price']);
    }

    public function test_product_with_high_price()
    {
        $domain = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Expensive Product',
            barcode: '1234567890123',
            description: 'Product with high price',
            price: 999999.99,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertEquals(999999.99, $domain->price);

        $array = $domain->toArray();
        $this->assertEquals(999999.99, $array['price']);
    }

    protected function createDomainInstance()
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: Carbon::now(),
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'brand_id',
            'name',
            'barcode',
            'description',
            'price',
            'unity',
            'last_priced_at',
            'created_at',
            'updated_at',
            'brand'
        ];
    }

    private function createMockBrand(): Brand
    {
        return new Brand(
            id: 1,
            organization_id: 1,
            name: 'Test Brand',
            description: 'Test brand description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }
}
