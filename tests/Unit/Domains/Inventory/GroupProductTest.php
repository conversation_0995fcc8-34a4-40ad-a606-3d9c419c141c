<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\GroupProduct;
use App\Domains\Inventory\Group;
use App\Domains\Inventory\Product;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class GroupProductTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(GroupProduct::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->group_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->group);
        $this->assertNull($domain->product);
    }

    public function test_domain_instantiation_with_null_values()
    {
        $domain = new GroupProduct(null, null, null, null, null, null, null);

        $this->assertInstanceOf(GroupProduct::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->group_id);
        $this->assertNull($domain->product_id);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->group);
        $this->assertNull($domain->product);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $group = $this->createMockGroup();
        $product = $this->createMockProduct();

        $domain = new GroupProduct(
            1,
            1,
            1,
            Carbon::now(),
            Carbon::now(),
            $group,
            $product
        );

        $this->assertInstanceOf(GroupProduct::class, $domain);
        $this->assertInstanceOf(Group::class, $domain->group);
        $this->assertInstanceOf(Product::class, $domain->product);
        $this->assertEquals('Test Group', $domain->group->name);
        $this->assertEquals('Test Product', $domain->product->name);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['group_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertNull($array['group']);
        $this->assertNull($array['product']);
    }

    public function test_to_array_method_with_null_dates()
    {
        $domain = new GroupProduct(1, 1, 1, null, null);
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_array_method_with_relationships()
    {
        $group = $this->createMockGroup();
        $product = $this->createMockProduct();

        $domain = new GroupProduct(
            1,
            1,
            1,
            Carbon::now(),
            Carbon::now(),
            $group,
            $product
        );

        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertIsArray($array['group']);
        $this->assertIsArray($array['product']);
        $this->assertEquals('Test Group', $array['group']['name']);
        $this->assertEquals('Test Product', $array['product']['name']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('group', $storeArray);
        $this->assertArrayNotHasKey('product', $storeArray);

        $this->assertArrayHasKey('group_id', $storeArray);
        $this->assertArrayHasKey('product_id', $storeArray);

        $this->assertEquals(1, $storeArray['group_id']);
        $this->assertEquals(1, $storeArray['product_id']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);
        $this->assertArrayNotHasKey('group', $updateArray);
        $this->assertArrayNotHasKey('product', $updateArray);

        $this->assertArrayHasKey('group_id', $updateArray);
        $this->assertArrayHasKey('product_id', $updateArray);

        $this->assertEquals(1, $updateArray['group_id']);
        $this->assertEquals(1, $updateArray['product_id']);
    }

    public function test_domain_with_different_group_and_product_ids()
    {
        $domain = new GroupProduct(5, 10, 20, Carbon::now(), Carbon::now());

        $this->assertInstanceOf(GroupProduct::class, $domain);
        $this->assertEquals(5, $domain->id);
        $this->assertEquals(10, $domain->group_id);
        $this->assertEquals(20, $domain->product_id);

        $array = $domain->toArray();
        $this->assertEquals(10, $array['group_id']);
        $this->assertEquals(20, $array['product_id']);
    }

    protected function createDomainInstance()
    {
        return new GroupProduct(
            1,
            1,
            1,
            Carbon::now(),
            Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'group_id',
            'product_id',
            'created_at',
            'updated_at',
            'group',
            'product',
        ];
    }

    private function createMockGroup(): Group
    {
        return new Group(
            1,
            1,
            'Test Group',
            'Test group description',
            Carbon::now(),
            Carbon::now()
        );
    }

    private function createMockProduct(): Product
    {
        return new Product(
            1,
            1,
            1,
            'Test Product',
            null,
            'Test product description',
            100.00,
            null,
            null,
            Carbon::now(),
            Carbon::now()
        );
    }
}
