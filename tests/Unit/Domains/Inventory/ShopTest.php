<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Shop;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ShopTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Shop', $domain->name);
        $this->assertEquals('Test shop description', $domain->description);
        $this->assertTrue($domain->is_active);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Shop(
            id: null,
            organization_id: 1,
            name: 'Minimal Shop',
            description: null,
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Minimal Shop', $domain->name);
        $this->assertNull($domain->description);
        $this->assertTrue($domain->is_active);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('Test Shop', $array['name']);
        $this->assertEquals('Test shop description', $array['description']);
        $this->assertTrue($array['is_active']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: 'Null Dates Shop',
            description: 'Shop with null dates',
            is_active: true,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = ['organization_id', 'name', 'description', 'is_active'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals('Test Shop', $storeArray['name']);
        $this->assertEquals('Test shop description', $storeArray['description']);
        $this->assertTrue($storeArray['is_active']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = ['name', 'description', 'is_active'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals('Test Shop', $updateArray['name']);
        $this->assertEquals('Test shop description', $updateArray['description']);
        $this->assertTrue($updateArray['is_active']);
    }

    public function test_shop_with_empty_description()
    {
        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: 'No Description Shop',
            description: '',
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals('', $domain->description);

        $array = $domain->toArray();
        $this->assertEquals('', $array['description']);
    }

    public function test_shop_with_inactive_status()
    {
        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertFalse($domain->is_active);

        $array = $domain->toArray();
        $this->assertFalse($array['is_active']);
    }

    public function test_shop_with_long_name()
    {
        $longName = str_repeat('A', 255);

        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: $longName,
            description: 'Shop with long name',
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($longName, $domain->name);

        $array = $domain->toArray();
        $this->assertEquals($longName, $array['name']);
    }

    public function test_shop_with_long_description()
    {
        $longDescription = str_repeat('B', 1000);

        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: 'Long Description Shop',
            description: $longDescription,
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($longDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($longDescription, $array['description']);
    }

    public function test_shop_with_special_characters()
    {
        $specialName = 'Shop & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';

        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: $specialName,
            description: $specialDescription,
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($specialName, $domain->name);
        $this->assertEquals($specialDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($specialName, $array['name']);
        $this->assertEquals($specialDescription, $array['description']);
    }

    public function test_shop_with_unicode_characters()
    {
        $unicodeName = 'Shöp Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';

        $domain = new Shop(
            id: 1,
            organization_id: 1,
            name: $unicodeName,
            description: $unicodeDescription,
            is_active: true
        );

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($unicodeName, $domain->name);
        $this->assertEquals($unicodeDescription, $domain->description);

        $array = $domain->toArray();
        $this->assertEquals($unicodeName, $array['name']);
        $this->assertEquals($unicodeDescription, $array['description']);
    }

    public function test_shop_status_variations()
    {
        // Test active shop
        $activeShop = new Shop(
            id: 1,
            organization_id: 1,
            name: 'Active Shop',
            description: 'This shop is active',
            is_active: true
        );

        $this->assertTrue($activeShop->is_active);

        // Test inactive shop
        $inactiveShop = new Shop(
            id: 2,
            organization_id: 1,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $this->assertFalse($inactiveShop->is_active);
    }

    protected function createDomainInstance()
    {
        return new Shop(
            id: 1,
            organization_id: 1,
            name: 'Test Shop',
            description: 'Test shop description',
            is_active: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'description',
            'is_active',
            'created_at',
            'updated_at'
        ];
    }
}
