<?php

namespace Tests\Unit\Domains;

use App\Domains\User;
use App\Domains\Organization;
use Carbon\Carbon;

class UserTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null, // default_flow_id
            Carbon::now(),
            Carbon::now()
        );

        $user = new User(
            1,
            1,
            1,
            '<PERSON>',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $organization
        );

        $this->assertEquals(1, $user->id);
        $this->assertEquals(1, $user->profile_id);
        $this->assertEquals(1, $user->organization_id);
        $this->assertEquals('John', $user->first_name);
        $this->assertEquals('Doe', $user->last_name);
        $this->assertEquals('<PERSON>', $user->name);
        $this->assertEquals('johndo<PERSON>', $user->username);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('password123', $user->password);
        $this->assertEquals('12345678901', $user->cpf);
        $this->assertEquals('+1234567890', $user->phone);
        $this->assertEquals('test-token', $user->token);
        $this->assertInstanceOf(Organization::class, $user->organization);
    }

    public function test_to_array_method()
    {
        $user = $this->createDomainInstance();
        $array = $user->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals('John Doe', $array['name']);
        $this->assertArrayNotHasKey('password', $array);
        $this->assertArrayNotHasKey('token', $array);
    }

    public function test_to_array_with_token_method()
    {
        $user = $this->createDomainInstance();
        $array = $user->toArrayWithToken();

        $this->assertIsArray($array);
        $this->assertArrayHasKey('token', $array);
        $this->assertEquals('test-token', $array['token']);
    }

    public function test_get_name_method()
    {
        $user = $this->createDomainInstance();
        $name = $user->getName();

        $this->assertEquals('John Doe', $name);
    }

    public function test_set_name_method()
    {
        $user = $this->createDomainInstance();
        $name = $user->setName();

        $this->assertEquals('John Doe', $name);
        $this->assertEquals('John Doe', $user->name);
    }

    public function test_to_store_array_excludes_sensitive_data()
    {
        $user = $this->createDomainInstance();
        $storeArray = $user->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('token', $storeArray);
        $this->assertArrayHasKey('password', $storeArray);
        // Password should be hashed
        $this->assertNotEquals('password123', $storeArray['password']);
    }

    public function test_to_update_array_with_password()
    {
        $user = $this->createDomainInstance();
        $updateArray = $user->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayHasKey('password', $updateArray);
    }

    public function test_to_update_array_without_password()
    {
        $user = new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            null, // No password
            '12345678901',
            '+1234567890'
        );

        $updateArray = $user->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('password', $updateArray);
    }

    protected function createDomainInstance()
    {
        $organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null, // default_flow_id
            Carbon::now(),
            Carbon::now()
        );

        return new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $organization
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'profile_id',
            'organization_id',
            'name',
            'first_name',
            'last_name',
            'username',
            'email',
            'cpf',
            'phone',
            'organization'
        ];
    }
}
