<?php

namespace Tests\Unit\Domains;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

abstract class BaseDomainTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that domain object can be instantiated with all properties
     */
    abstract public function test_domain_instantiation();

    /**
     * Test toArray method returns correct structure
     */
    abstract public function test_to_array_method();

    /**
     * Test toStoreArray method if it exists
     */
    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        
        if (method_exists($domain, 'toStoreArray')) {
            $storeArray = $domain->toStoreArray();
            $this->assertIsArray($storeArray);
            $this->assertArrayNotHasKey('id', $storeArray);
            $this->assertArrayNotHasKey('created_at', $storeArray);
            $this->assertArrayNotHasKey('updated_at', $storeArray);
        } else {
            $this->markTestSkipped('toStoreArray method not implemented');
        }
    }

    /**
     * Test toUpdateArray method if it exists
     */
    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        
        if (method_exists($domain, 'toUpdateArray')) {
            $updateArray = $domain->toUpdateArray();
            $this->assertIsArray($updateArray);
            $this->assertArrayNotHasKey('id', $updateArray);
            $this->assertArrayNotHasKey('created_at', $updateArray);
            $this->assertArrayNotHasKey('updated_at', $updateArray);
        } else {
            $this->markTestSkipped('toUpdateArray method not implemented');
        }
    }

    /**
     * Create a domain instance for testing
     * Must be implemented by child classes
     */
    abstract protected function createDomainInstance();

    /**
     * Get expected array keys for toArray method
     * Must be implemented by child classes
     */
    abstract protected function getExpectedArrayKeys(): array;

    /**
     * Helper method to assert array structure
     */
    protected function assertArrayStructure(array $expected, array $actual)
    {
        foreach ($expected as $key) {
            $this->assertArrayHasKey($key, $actual, "Missing key: {$key}");
        }
    }
}
