<?php

namespace Tests\Unit\Factories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Factories\WhatsAppWebhookLogFactory;
use App\Models\WhatsAppWebhookLog as WhatsAppWebhookLogModel;
use App\Domains\WhatsAppWebhookLog as WhatsAppWebhookLogDomain;

class WhatsAppWebhookLogFactoryTest extends TestCase
{
    use RefreshDatabase;

    private WhatsAppWebhookLogFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new WhatsAppWebhookLogFactory();
    }

    public function test_build_from_model()
    {
        $model = WhatsAppWebhookLogModel::create([
            'organization_id' => null, // Use null to avoid foreign key constraint
            'phone_number_id' => '***************',
            'event_type' => 'message',
            'webhook_payload' => ['test' => 'data'],
            'processing_status' => 'pending',
            'error_message' => 'Test error',
        ]);

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertNull($domain->organization_id); // Should be null
        $this->assertEquals($model->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($model->event_type, $domain->event_type);
        $this->assertEquals($model->webhook_payload, $domain->webhook_payload);
        $this->assertEquals($model->processing_status, $domain->processing_status);
        $this->assertEquals($model->error_message, $domain->error_message);
    }

    public function test_build_collection()
    {
        $model1 = WhatsAppWebhookLogModel::create([
            'phone_number_id' => '***************',
            'event_type' => 'message',
            'webhook_payload' => ['test' => 'data1'],
            'processing_status' => 'pending',
        ]);

        $model2 = WhatsAppWebhookLogModel::create([
            'phone_number_id' => '***************',
            'event_type' => 'status',
            'webhook_payload' => ['test' => 'data2'],
            'processing_status' => 'success',
        ]);

        $models = collect([$model1, $model2]);
        $domains = $this->factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(2, $domains);
        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domains[0]);
        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domains[1]);
        $this->assertEquals('message', $domains[0]->event_type);
        $this->assertEquals('status', $domains[1]->event_type);
    }

    public function test_create_for_message()
    {
        $payload = ['message' => 'test'];
        $domain = $this->factory->createForMessage(
            organizationId: 123,
            phoneNumberId: '***************',
            webhookPayload: $payload,
            processingStatus: 'pending',
            errorMessage: 'Test error'
        );

        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        $this->assertEquals(123, $domain->organization_id);
        $this->assertEquals('***************', $domain->phone_number_id);
        $this->assertEquals('message', $domain->event_type);
        $this->assertEquals($payload, $domain->webhook_payload);
        $this->assertEquals('pending', $domain->processing_status);
        $this->assertEquals('Test error', $domain->error_message);
    }

    public function test_create_for_status()
    {
        $payload = ['status' => 'delivered'];
        $domain = $this->factory->createForStatus(
            organizationId: 456,
            phoneNumberId: '***************',
            webhookPayload: $payload,
            processingStatus: 'success'
        );

        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        $this->assertEquals(456, $domain->organization_id);
        $this->assertEquals('***************', $domain->phone_number_id);
        $this->assertEquals('status', $domain->event_type);
        $this->assertEquals($payload, $domain->webhook_payload);
        $this->assertEquals('success', $domain->processing_status);
        $this->assertNull($domain->error_message);
    }

    public function test_create_for_other()
    {
        $payload = ['other' => 'data'];
        $domain = $this->factory->createForOther(
            organizationId: null,
            phoneNumberId: '***************',
            webhookPayload: $payload,
            processingStatus: 'failed',
            errorMessage: 'Unknown event'
        );

        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        $this->assertNull($domain->organization_id);
        $this->assertEquals('***************', $domain->phone_number_id);
        $this->assertEquals('other', $domain->event_type);
        $this->assertEquals($payload, $domain->webhook_payload);
        $this->assertEquals('failed', $domain->processing_status);
        $this->assertEquals('Unknown event', $domain->error_message);
    }

    public function test_create_sample_message_payload()
    {
        $payload = $this->factory->createSampleMessagePayload('msg_123', '*************');

        $this->assertIsArray($payload);
        $this->assertEquals('whatsapp_business_account', $payload['object']);
        $this->assertArrayHasKey('entry', $payload);
        $this->assertIsArray($payload['entry']);
        $this->assertCount(1, $payload['entry']);

        $entry = $payload['entry'][0];
        $this->assertArrayHasKey('changes', $entry);
        $this->assertIsArray($entry['changes']);

        $change = $entry['changes'][0];
        $this->assertEquals('messages', $change['field']);
        $this->assertArrayHasKey('value', $change);

        $value = $change['value'];
        $this->assertEquals('whatsapp', $value['messaging_product']);
        $this->assertArrayHasKey('messages', $value);

        $message = $value['messages'][0];
        $this->assertEquals('msg_123', $message['id']);
        $this->assertEquals('*************', $message['from']);
        $this->assertEquals('text', $message['type']);
    }

    public function test_create_sample_status_payload()
    {
        $payload = $this->factory->createSampleStatusPayload('msg_456', 'read');

        $this->assertIsArray($payload);
        $this->assertEquals('whatsapp_business_account', $payload['object']);
        $this->assertArrayHasKey('entry', $payload);

        $entry = $payload['entry'][0];
        $change = $entry['changes'][0];
        $this->assertEquals('messages', $change['field']);

        $value = $change['value'];
        $this->assertArrayHasKey('statuses', $value);

        $status = $value['statuses'][0];
        $this->assertEquals('msg_456', $status['id']);
        $this->assertEquals('read', $status['status']);
        $this->assertEquals('*************', $status['recipient_id']);
    }

    public function test_create_fake()
    {
        $domain = $this->factory->createFake();

        $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        $this->assertNotNull($domain->id);
        $this->assertNotNull($domain->organization_id);
        $this->assertEquals('***************', $domain->phone_number_id);
        $this->assertContains($domain->event_type, ['message', 'status', 'other']);
        $this->assertIsArray($domain->webhook_payload);
        $this->assertContains($domain->processing_status, ['pending', 'success', 'failed']);
        $this->assertNotNull($domain->created_at);
        $this->assertNotNull($domain->updated_at);
    }

    public function test_create_fake_with_overrides()
    {
        $overrides = [
            'event_type' => 'message',
            'processing_status' => 'success',
            'organization_id' => 999,
        ];

        $domain = $this->factory->createFake($overrides);

        $this->assertEquals('message', $domain->event_type);
        $this->assertEquals('success', $domain->processing_status);
        $this->assertEquals(999, $domain->organization_id);
    }

    public function test_create_fake_collection()
    {
        $domains = $this->factory->createFakeCollection(3);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(WhatsAppWebhookLogDomain::class, $domain);
        }
    }

    public function test_create_fake_collection_with_overrides()
    {
        $overrides = ['event_type' => 'status'];
        $domains = $this->factory->createFakeCollection(2, $overrides);

        $this->assertCount(2, $domains);
        $this->assertEquals('status', $domains[0]->event_type);
        $this->assertEquals('status', $domains[1]->event_type);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(WhatsAppWebhookLogFactory::class);
        $this->assertInstanceOf(WhatsAppWebhookLogFactory::class, $factory);
    }
}
