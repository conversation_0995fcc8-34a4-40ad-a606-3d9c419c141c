<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\ButtonFactory;
use App\Domains\ChatBot\Button;
use App\Models\Button as ButtonModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ButtonFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Button::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->text, $domain->text);
        $this->assertEquals($model->type, $domain->type);
        $this->assertEquals($model->internal_type, $domain->internal_type);
        $this->assertEquals($model->callback_data, $domain->callback_data);
        $this->assertEquals($model->json, $domain->json);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_models_collection()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Button::class, $domain);
        }
    }

    public function test_build_from_models_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(collect());

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_store_request()
    {
        // ButtonFactory doesn't have buildFromStoreRequest method
        $this->markTestSkipped('ButtonFactory does not implement buildFromStoreRequest');
    }

    public function test_build_from_update_request()
    {
        // ButtonFactory doesn't have buildFromUpdateRequest method
        $this->markTestSkipped('ButtonFactory does not implement buildFromUpdateRequest');
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ButtonFactory::class);

        $this->assertInstanceOf(ButtonFactory::class, $factory);
    }

    public function test_factory_can_handle_different_button_types()
    {
        $quickReplyButton = ButtonModel::factory()->quickReply()->create();
        $urlButton = ButtonModel::factory()->url()->create();
        $conditionButton = ButtonModel::factory()->condition()->create();

        $factory = $this->createFactoryInstance();

        $quickReplyDomain = $factory->buildFromModel($quickReplyButton);
        $urlDomain = $factory->buildFromModel($urlButton);
        $conditionDomain = $factory->buildFromModel($conditionButton);

        $this->assertEquals('QUICK_REPLY', $quickReplyDomain->type);
        $this->assertEquals('URL', $urlDomain->type);
        $this->assertEquals('condition', $conditionDomain->internal_type);
    }

    protected function createFactoryInstance()
    {
        return new ButtonFactory();
    }

    protected function getDomainClass(): string
    {
        return Button::class;
    }

    protected function createModelInstance()
    {
        return ButtonModel::factory()->create();
    }
}
