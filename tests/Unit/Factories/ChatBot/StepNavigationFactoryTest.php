<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\StepNavigationFactory;
use App\Factories\ChatBot\StepFactory;
use App\Domains\ChatBot\StepNavigation;
use App\Models\StepNavigation as StepNavigationModel;
use App\Http\Requests\StepNavigation\StoreRequest;
use App\Http\Requests\StepNavigation\UpdateRequest;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class StepNavigationFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->step_id, $domain->step_id);
        $this->assertEquals($model->condition_type, $domain->condition_type);
        $this->assertEquals($model->condition_data, $domain->condition_data);
        $this->assertEquals($model->target_step_identifier, $domain->target_step_identifier);
        $this->assertEquals($model->priority, $domain->priority);
        $this->assertEquals($model->is_active, $domain->is_active);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_models_collection()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(StepNavigation::class, $domain);
        }
    }

    public function test_build_from_models_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(collect());

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_store_request()
    {
        $request = Mockery::mock(StoreRequest::class);
        $request->organization_id = 1;
        $request->step_id = 1;
        $request->condition_type = 'button_click';
        $request->condition_data = ['button_id' => 'test'];
        $request->target_step_identifier = 'next_step';
        $request->priority = 0;
        $request->is_active = true;

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->step_id);
        $this->assertEquals('button_click', $domain->condition_type);
        $this->assertEquals(['button_id' => 'test'], $domain->condition_data);
        $this->assertEquals('next_step', $domain->target_step_identifier);
        $this->assertEquals(0, $domain->priority);
        $this->assertTrue($domain->is_active);
    }

    public function test_build_from_update_request()
    {
        $request = Mockery::mock(UpdateRequest::class);
        $request->condition_type = 'text_match';
        $request->condition_data = ['text' => 'yes'];
        $request->target_step_identifier = 'updated_step';
        $request->priority = 1;
        $request->is_active = false;

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals('text_match', $domain->condition_type);
        $this->assertEquals(['text' => 'yes'], $domain->condition_data);
        $this->assertEquals('updated_step', $domain->target_step_identifier);
        $this->assertEquals(1, $domain->priority);
        $this->assertFalse($domain->is_active);
    }

    public function test_build_from_array()
    {
        $data = [
            'condition_type' => 'regex',
            'condition_data' => ['pattern' => '\d+'],
            'target_step_identifier' => 'number_step',
            'priority' => 2,
            'is_active' => true,
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data, 1, 1, 123);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(123, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->step_id);
        $this->assertEquals('regex', $domain->condition_type);
        $this->assertEquals(['pattern' => '\d+'], $domain->condition_data);
        $this->assertEquals('number_step', $domain->target_step_identifier);
        $this->assertEquals(2, $domain->priority);
        $this->assertTrue($domain->is_active);
    }

    public function test_build_from_arrays()
    {
        $rules = [
            [
                'condition_type' => 'button_click',
                'condition_data' => ['button_id' => 'btn1'],
                'target_step_identifier' => 'step1',
                'priority' => 0,
            ],
            [
                'condition_type' => 'text_match',
                'condition_data' => ['text' => 'yes'],
                'target_step_identifier' => 'step2',
                'priority' => 1,
            ],
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromArrays($rules, 1, 1);

        $this->assertIsArray($domains);
        $this->assertCount(2, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(StepNavigation::class, $domain);
            $this->assertEquals(1, $domain->organization_id);
            $this->assertEquals(1, $domain->step_id);
        }
    }

    public function test_create_button_click_rule()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->createButtonClickRule(1, 1, 'test_btn', 'next_step', 0, 'Test Button');

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(StepNavigation::BUTTON_CLICK, $domain->condition_type);
        $this->assertEquals(['button_id' => 'test_btn', 'button_text' => 'Test Button'], $domain->condition_data);
        $this->assertEquals('next_step', $domain->target_step_identifier);
    }

    public function test_create_text_match_rule()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->createTextMatchRule(1, 1, 'yes', 'next_step', 0, true);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(StepNavigation::TEXT_MATCH, $domain->condition_type);
        $this->assertEquals(['text' => 'yes', 'case_sensitive' => true], $domain->condition_data);
        $this->assertEquals('next_step', $domain->target_step_identifier);
    }

    public function test_create_regex_rule()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->createRegexRule(1, 1, '\d+', 'number_step', 0, 'i');

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(StepNavigation::REGEX, $domain->condition_type);
        $this->assertEquals(['pattern' => '\d+', 'flags' => 'i'], $domain->condition_data);
        $this->assertEquals('number_step', $domain->target_step_identifier);
    }

    public function test_create_default_rule()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->createDefaultRule(1, 1, 'default_step', 999);

        $this->assertInstanceOf(StepNavigation::class, $domain);
        $this->assertEquals(StepNavigation::DEFAULT, $domain->condition_type);
        $this->assertNull($domain->condition_data);
        $this->assertEquals('default_step', $domain->target_step_identifier);
        $this->assertEquals(999, $domain->priority);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(StepNavigationFactory::class);

        $this->assertInstanceOf(StepNavigationFactory::class, $factory);
    }

    protected function createFactoryInstance()
    {
        $stepFactory = Mockery::mock(StepFactory::class);
        return new StepNavigationFactory($stepFactory);
    }

    protected function getDomainClass(): string
    {
        return StepNavigation::class;
    }

    protected function createModelInstance()
    {
        return StepNavigationModel::factory()->create();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
