<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\CampaignStatusHistoryFactory;
use App\Domains\ChatBot\CampaignStatusHistory;
use App\Models\CampaignStatusHistory as CampaignStatusHistoryModel;
use App\Models\Campaign as CampaignModel;
use App\Models\User;
use App\Enums\CampaignStatus;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class CampaignStatusHistoryFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(CampaignStatusHistoryFactory::class);

        $this->assertInstanceOf(CampaignStatusHistoryFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(CampaignStatusHistoryFactory::class);
        $factory2 = app()->make(CampaignStatusHistoryFactory::class);

        $this->assertInstanceOf(CampaignStatusHistoryFactory::class, $factory1);
        $this->assertInstanceOf(CampaignStatusHistoryFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->campaign_id, $domain->campaign_id);
        $this->assertEquals($model->old_status, $domain->old_status);
        $this->assertEquals($model->new_status, $domain->new_status);
        $this->assertEquals($model->reason, $domain->reason);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->metadata, $domain->metadata);
        $this->assertNull($domain->campaign);
        $this->assertNull($domain->user);
    }

    public function test_build_from_model_with_relations()
    {
        $campaign = CampaignModel::factory()->make(['id' => 1]);
        $user = User::factory()->make(['id' => 2]);

        $model = $this->createModelInstance([
            'campaign_id' => $campaign->id,
            'user_id' => $user->id
        ]);

        $model->setRelation('campaign', $campaign);
        $model->setRelation('user', $user);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->campaign_id, $domain->campaign_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        // Relations should be loaded properly now that circular dependency is resolved
        $this->assertNotNull($domain->campaign);
        $this->assertInstanceOf(\App\Domains\ChatBot\Campaign::class, $domain->campaign);
        $this->assertNotNull($domain->user);
        $this->assertInstanceOf(\App\Domains\User::class, $domain->user);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'campaign_id' => 2,
            'old_status' => CampaignStatus::DRAFT->value,
            'new_status' => CampaignStatus::SENDING->value,
            'reason' => 'Campaign activated',
            'user_id' => 3,
            'metadata' => ['key' => 'value'],
            'created_at' => '2023-01-01 12:00:00',
            'updated_at' => '2023-01-01 12:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['campaign_id'], $domain->campaign_id);
        $this->assertEquals(CampaignStatus::DRAFT, $domain->old_status);
        $this->assertEquals(CampaignStatus::SENDING, $domain->new_status);
        $this->assertEquals($data['reason'], $domain->reason);
        $this->assertEquals($data['user_id'], $domain->user_id);
        $this->assertEquals($data['metadata'], $domain->metadata);
    }

    public function test_build_from_array_with_nulls()
    {
        $data = [];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->campaign_id);
        $this->assertNull($domain->old_status);
        $this->assertNull($domain->new_status);
        $this->assertNull($domain->reason);
        $this->assertNull($domain->user_id);
        $this->assertNull($domain->metadata);
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1]),
            $this->createModelInstance(['id' => 2]),
            $this->createModelInstance(['id' => 3]),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createFactoryInstance()
    {
        return new CampaignStatusHistoryFactory();
    }

    protected function getDomainClass(): string
    {
        return CampaignStatusHistory::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return CampaignStatusHistoryModel::factory()->make(array_merge([
            'id' => 1,
            'campaign_id' => 1,
            'old_status' => CampaignStatus::DRAFT,
            'new_status' => CampaignStatus::SENDING,
            'reason' => 'Status changed',
            'user_id' => 1,
            'metadata' => ['test' => 'data'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }
}
