<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\LeadFactory;
use App\Domains\ChatBot\Lead;
use App\Models\Lead as LeadModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class LeadFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(LeadFactory::class);
        
        $this->assertInstanceOf(LeadFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(LeadFactory::class);
        $factory2 = app()->make(LeadFactory::class);
        
        $this->assertInstanceOf(LeadFactory::class, $factory1);
        $this->assertInstanceOf(LeadFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->source, $domain->source);
        $this->assertEquals($model->status, $domain->status);
        $this->assertEquals($model->priority, $domain->priority);
        $this->assertEquals($model->title, $domain->title);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->notes, $domain->notes);
        $this->assertEquals($model->estimated_value, $domain->estimated_value);
        $this->assertEquals($model->service_type, $domain->service_type);
        $this->assertEquals($model->budget_range, $domain->budget_range);
        $this->assertEquals($model->timeline, $domain->timeline);
        $this->assertEquals($model->company, $domain->company);
        $this->assertEquals($model->custom_fields, $domain->custom_fields);
        $this->assertEquals($model->created_via, $domain->created_via);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'organization_id' => 2,
            'client_id' => 3,
            'source' => 'website',
            'status' => 'qualified',
            'priority' => 'high',
            'title' => 'Test Lead',
            'description' => 'Test Description',
            'notes' => 'Test Notes',
            'estimated_value' => 1000.50,
            'service_type' => 'consulting',
            'budget_range' => '1000-5000',
            'timeline' => '1-3 months',
            'company' => 'Test Company',
            'custom_fields' => ['key' => 'value'],
            'created_via' => 'manual',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['organization_id'], $domain->organization_id);
        $this->assertEquals($data['client_id'], $domain->client_id);
        $this->assertEquals($data['source'], $domain->source);
        $this->assertEquals($data['status'], $domain->status);
        $this->assertEquals($data['priority'], $domain->priority);
        $this->assertEquals($data['title'], $domain->title);
        $this->assertEquals($data['description'], $domain->description);
        $this->assertEquals($data['notes'], $domain->notes);
        $this->assertEquals($data['estimated_value'], $domain->estimated_value);
        $this->assertEquals($data['service_type'], $domain->service_type);
        $this->assertEquals($data['budget_range'], $domain->budget_range);
        $this->assertEquals($data['timeline'], $domain->timeline);
        $this->assertEquals($data['company'], $domain->company);
        $this->assertEquals($data['custom_fields'], $domain->custom_fields);
        $this->assertEquals($data['created_via'], $domain->created_via);
    }

    public function test_build_from_array_with_defaults()
    {
        $data = [
            'organization_id' => 1,
            'client_id' => 2,
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['organization_id'], $domain->organization_id);
        $this->assertEquals($data['client_id'], $domain->client_id);
        $this->assertEquals('manual', $domain->source);
        $this->assertEquals('new', $domain->status);
        $this->assertEquals('medium', $domain->priority);
        $this->assertEquals('manual', $domain->created_via);
    }

    public function test_build_for_chatbot()
    {
        $organizationId = 1;
        $clientId = 2;
        $conversationData = [
            'service_interest' => 'web development',
            'budget' => '5000-10000',
            'timeline' => '2-4 months',
            'company_name' => 'Test Company',
            'additional_notes' => 'Urgent project',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildForChatBot($organizationId, $clientId, $conversationData);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($organizationId, $domain->organization_id);
        $this->assertEquals($clientId, $domain->client_id);
        $this->assertEquals('chatbot_whatsapp', $domain->source);
        $this->assertEquals('new', $domain->status);
        $this->assertEquals('medium', $domain->priority);
        $this->assertEquals('chatbot', $domain->created_via);
        $this->assertEquals('web development', $domain->service_type);
        $this->assertEquals('5000-10000', $domain->budget_range);
        $this->assertEquals('2-4 months', $domain->timeline);
        $this->assertEquals('Test Company', $domain->company);
        $this->assertEquals('Urgent project', $domain->notes);
        $this->assertEquals('Interesse em web development', $domain->title);
        $this->assertIsArray($domain->custom_fields);
        $this->assertEquals($conversationData, $domain->custom_fields['chatbot_conversation_data']);
        $this->assertTrue($domain->custom_fields['created_via_chatbot']);
    }

    public function test_build_minimal()
    {
        $organizationId = 1;
        $clientId = 2;

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildMinimal($organizationId, $clientId);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals($organizationId, $domain->organization_id);
        $this->assertEquals($clientId, $domain->client_id);
        $this->assertEquals('manual', $domain->source);
        $this->assertEquals('new', $domain->status);
        $this->assertEquals('medium', $domain->priority);
        $this->assertEquals('manual', $domain->created_via);
    }

    public function test_build_with_custom_fields()
    {
        $organizationId = 1;
        $clientId = 2;
        $customFields = ['custom_key' => 'custom_value'];
        $baseData = ['title' => 'Custom Lead'];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildWithCustomFields($organizationId, $clientId, $customFields, $baseData);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($organizationId, $domain->organization_id);
        $this->assertEquals($clientId, $domain->client_id);
        $this->assertEquals($customFields, $domain->custom_fields);
        $this->assertEquals('Custom Lead', $domain->title);
    }

    public function test_clone_lead()
    {
        $originalLead = new Lead(
            id: 1,
            organization_id: 1,
            client_id: 2,
            source: 'website',
            status: 'qualified',
            priority: 'high',
            title: 'Original Lead',
            created_via: 'manual'
        );

        $newData = ['title' => 'Cloned Lead', 'status' => 'new'];

        $factory = $this->createFactoryInstance();
        $clonedLead = $factory->cloneLead($originalLead, $newData);

        $this->assertInstanceOf($this->getDomainClass(), $clonedLead);
        $this->assertNull($clonedLead->id); // Should be null for new lead
        $this->assertEquals($originalLead->organization_id, $clonedLead->organization_id);
        $this->assertEquals($originalLead->client_id, $clonedLead->client_id);
        $this->assertEquals($originalLead->source, $clonedLead->source);
        $this->assertEquals('new', $clonedLead->status); // Should use new status
        $this->assertEquals($originalLead->priority, $clonedLead->priority);
        $this->assertEquals('Cloned Lead', $clonedLead->title); // Should use new title
        $this->assertEquals('cloned', $clonedLead->created_via);
    }

    protected function createFactoryInstance()
    {
        return new LeadFactory();
    }

    protected function getDomainClass(): string
    {
        return Lead::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return LeadModel::factory()->make(array_merge([
            'id' => 1,
            'organization_id' => 1,
            'client_id' => 1,
            'source' => 'manual',
            'status' => 'new',
            'priority' => 'medium',
            'title' => 'Test Lead',
            'description' => 'Test Description',
            'notes' => 'Test Notes',
            'estimated_value' => 1000.00,
            'service_type' => 'consulting',
            'budget_range' => '1000-5000',
            'timeline' => '1-3 months',
            'company' => 'Test Company',
            'custom_fields' => ['test' => 'data'],
            'created_via' => 'manual',
            'contacted_at' => Carbon::now(),
            'qualified_at' => null,
            'closed_at' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'deleted_at' => null,
        ], $attributes));
    }
}
