<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\ComponentFactory;
use App\Factories\ChatBot\StepFactory;
use App\Factories\ChatBot\TemplateFactory;
use App\Factories\ChatBot\ButtonFactory;
use App\Factories\ChatBot\ParameterFactory;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Template;
use App\Models\Component as ComponentModel;
use App\Enums\ComponentFormat;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ComponentFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, false, false, false);

        $this->assertInstanceOf(Component::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->step_id, $domain->step_id);
        $this->assertEquals($model->template_id, $domain->template_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->type, $domain->type);
        $this->assertEquals($model->text, $domain->text);
        $this->assertEquals($model->format, $domain->format);
        $this->assertEquals($model->json, $domain->json);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_save_full_template_component()
    {
        $template = new Template(
            1,
            1,
            null,
            null,
            null,
            'Test Template',
            'UTILITY',
            'NAMED',
            'en_US',
            null,
            null,
            'PENDING'
        );

        $componentData = [
            'name' => 'Test Component',
            'type' => 'BODY',
            'text' => 'Hello {{1}}',
            'format' => 'TEXT'
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromSaveFullTemplateComponent(
            $componentData,
            $template,
            '{"test": "data"}',
            123
        );

        $this->assertInstanceOf(Component::class, $domain);
        $this->assertEquals(123, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->template_id);
        $this->assertEquals('Test Component', $domain->name);
        $this->assertEquals('BODY', $domain->type);
        $this->assertEquals('Hello {{1}}', $domain->text);
        $this->assertEquals(ComponentFormat::TEXT, $domain->format);
        $this->assertEquals('{"test": "data"}', $domain->json);
    }

    public function test_build_from_store_request()
    {
        // ComponentFactory has buildFromStoreRequest but requires specific request classes
        $this->markTestSkipped('Store request test requires specific request class implementation');
    }

    public function test_build_from_update_request()
    {
        // ComponentFactory has buildFromUpdateRequest but requires specific request classes
        $this->markTestSkipped('Update request test requires specific request class implementation');
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ComponentFactory::class);

        $this->assertInstanceOf(ComponentFactory::class, $factory);
    }

    public function test_factory_can_handle_different_component_types()
    {
        $headerComponent = ComponentModel::factory()->header()->create();
        $bodyComponent = ComponentModel::factory()->body()->create();
        $footerComponent = ComponentModel::factory()->footer()->create();
        $buttonsComponent = ComponentModel::factory()->buttons()->create();

        $factory = $this->createFactoryInstance();

        $headerDomain = $factory->buildFromModel($headerComponent, false, false, false, false);
        $bodyDomain = $factory->buildFromModel($bodyComponent, false, false, false, false);
        $footerDomain = $factory->buildFromModel($footerComponent, false, false, false, false);
        $buttonsDomain = $factory->buildFromModel($buttonsComponent, false, false, false, false);

        $this->assertEquals('HEADER', $headerDomain->type);
        $this->assertEquals('BODY', $bodyDomain->type);
        $this->assertEquals('FOOTER', $footerDomain->type);
        $this->assertEquals('BUTTONS', $buttonsDomain->type);
    }

    protected function createFactoryInstance()
    {
        // Use mocks to avoid circular dependency issues
        $buttonFactory = $this->createMock(ButtonFactory::class);
        $templateFactory = $this->createMock(TemplateFactory::class);

        return new ComponentFactory(
            $buttonFactory,
            $templateFactory
        );
    }

    protected function getDomainClass(): string
    {
        return Component::class;
    }

    protected function createModelInstance()
    {
        return ComponentModel::factory()->create();
    }
}
