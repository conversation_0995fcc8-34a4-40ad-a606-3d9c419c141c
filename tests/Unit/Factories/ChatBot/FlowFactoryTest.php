<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\FlowFactory;
use App\Factories\ChatBot\StepFactory;
use App\Domains\ChatBot\Flow;
use App\Models\Flow as FlowModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FlowFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false); // without steps to avoid complexity

        $this->assertInstanceOf(Flow::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->steps_count, $domain->steps_count);
        $this->assertEquals($model->json, $domain->json);
        $this->assertEquals($model->is_default_flow, $domain->is_default_flow);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_save_full_flow()
    {
        $flowData = [
            'name' => 'Test Flow',
            'description' => 'Test Description',
            'is_default_flow' => true
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromSaveFullFlow(
            $flowData,
            '{"flow": "data"}',
            5,
            1,
            123
        );

        $this->assertInstanceOf(Flow::class, $domain);
        $this->assertEquals(123, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Flow', $domain->name);
        $this->assertEquals('Test Description', $domain->description);
        $this->assertEquals(5, $domain->steps_count);
        $this->assertEquals('{"flow": "data"}', $domain->json);
        $this->assertTrue($domain->is_default_flow);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(FlowFactory::class);

        $this->assertInstanceOf(FlowFactory::class, $factory);
    }

    public function test_factory_can_handle_different_flow_types()
    {
        $defaultFlow = FlowModel::factory()->default()->create();
        $simpleFlow = FlowModel::factory()->simple()->create();
        $complexFlow = FlowModel::factory()->complex()->create();

        $factory = $this->createFactoryInstance();

        $defaultDomain = $factory->buildFromModel($defaultFlow, false);
        $simpleDomain = $factory->buildFromModel($simpleFlow, false);
        $complexDomain = $factory->buildFromModel($complexFlow, false);

        $this->assertTrue($defaultDomain->is_default_flow);
        $this->assertEquals('Default Flow', $defaultDomain->name);

        $this->assertEquals('Simple Flow', $simpleDomain->name);
        $this->assertLessThanOrEqual(3, $simpleDomain->steps_count);

        $this->assertEquals('Complex Flow', $complexDomain->name);
        $this->assertGreaterThanOrEqual(10, $complexDomain->steps_count);
    }

    public function test_build_from_save_full_flow_with_defaults()
    {
        $flowData = [
            'name' => 'Minimal Flow'
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromSaveFullFlow(
            $flowData,
            '{}',
            null,
            1,
            null
        );

        $this->assertInstanceOf(Flow::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Minimal Flow', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->steps_count);
        $this->assertEquals('{}', $domain->json);
        $this->assertFalse($domain->is_default_flow);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve circular dependencies
        return app()->make(FlowFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Flow::class;
    }

    protected function createModelInstance()
    {
        return FlowModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Flow\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'Test Flow',
            'description' => 'Test Description',
            'steps_count' => 5,
            'json' => '{"flow": "data"}',
            'is_default_flow' => false,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Flow\UpdateRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'Updated Flow',
            'description' => 'Updated Description',
            'steps_count' => 3,
            'json' => '{"updated": "data"}',
            'is_default_flow' => true,
        ]);
        return $request;
    }
}
