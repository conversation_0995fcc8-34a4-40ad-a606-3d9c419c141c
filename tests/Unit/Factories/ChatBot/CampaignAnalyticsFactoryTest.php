<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\CampaignAnalyticsFactory;
use App\Domains\ChatBot\CampaignAnalytics;
use App\Models\CampaignAnalytics as CampaignAnalyticsModel;
use App\Models\Campaign as CampaignModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class CampaignAnalyticsFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(CampaignAnalyticsFactory::class);

        $this->assertInstanceOf(CampaignAnalyticsFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(CampaignAnalyticsFactory::class);
        $factory2 = app()->make(CampaignAnalyticsFactory::class);

        $this->assertInstanceOf(CampaignAnalyticsFactory::class, $factory1);
        $this->assertInstanceOf(CampaignAnalyticsFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->campaign_id, $domain->campaign_id);
        $this->assertEquals($model->total_messages, $domain->total_messages);
        $this->assertEquals($model->sent_count, $domain->sent_count);
        $this->assertEquals($model->delivered_count, $domain->delivered_count);
        $this->assertEquals($model->failed_count, $domain->failed_count);
        $this->assertEquals($model->read_count, $domain->read_count);
        $this->assertEquals($model->response_count, $domain->response_count);
        $this->assertEquals($model->delivery_rate, $domain->delivery_rate);
        $this->assertEquals($model->read_rate, $domain->read_rate);
        $this->assertEquals($model->response_rate, $domain->response_rate);
        $this->assertEquals($model->failure_rate, $domain->failure_rate);
    }

    public function test_build_from_model_with_campaign()
    {
        $campaign = CampaignModel::factory()->make(['id' => 1]);
        $model = $this->createModelInstance(['campaign_id' => $campaign->id]);
        $model->setRelation('campaign', $campaign);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        // Campaign should be loaded properly now that circular dependency is resolved
        $this->assertNotNull($domain->campaign);
        $this->assertInstanceOf(\App\Domains\ChatBot\Campaign::class, $domain->campaign);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'campaign_id' => 2,
            'total_messages' => 100,
            'sent_count' => 95,
            'delivered_count' => 90,
            'failed_count' => 5,
            'read_count' => 80,
            'response_count' => 20,
            'avg_delivery_time' => 30.5,
            'delivery_rate' => 0.95,
            'read_rate' => 0.8,
            'response_rate' => 0.2,
            'failure_rate' => 0.05,
            'calculated_at' => '2023-01-01 12:00:00',
            'created_at' => '2023-01-01 10:00:00',
            'updated_at' => '2023-01-01 11:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['campaign_id'], $domain->campaign_id);
        $this->assertEquals($data['total_messages'], $domain->total_messages);
        $this->assertEquals($data['sent_count'], $domain->sent_count);
        $this->assertEquals($data['delivered_count'], $domain->delivered_count);
        $this->assertEquals($data['failed_count'], $domain->failed_count);
        $this->assertEquals($data['read_count'], $domain->read_count);
        $this->assertEquals($data['response_count'], $domain->response_count);
        $this->assertEquals($data['delivery_rate'], $domain->delivery_rate);
        $this->assertEquals($data['read_rate'], $domain->read_rate);
        $this->assertEquals($data['response_rate'], $domain->response_rate);
        $this->assertEquals($data['failure_rate'], $domain->failure_rate);
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1]),
            $this->createModelInstance(['id' => 2]),
            $this->createModelInstance(['id' => 3]),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createFactoryInstance()
    {
        return new CampaignAnalyticsFactory();
    }

    protected function getDomainClass(): string
    {
        return CampaignAnalytics::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return CampaignAnalyticsModel::factory()->make(array_merge([
            'id' => 1,
            'campaign_id' => 1,
            'total_messages' => 100,
            'sent_count' => 95,
            'delivered_count' => 90,
            'failed_count' => 5,
            'read_count' => 80,
            'response_count' => 20,
            'avg_delivery_time' => 30.5,
            'delivery_rate' => 0.95,
            'read_rate' => 0.8,
            'response_rate' => 0.2,
            'failure_rate' => 0.05,
            'calculated_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }
}
