<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\TemplateFactory;
use App\Domains\ChatBot\Template;
use App\Models\Template as TemplateModel;
use App\Models\Organization as OrganizationModel;
use App\Models\PhoneNumber as PhoneNumberModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TemplateFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $organization = OrganizationModel::factory()->create();
        $phoneNumber = PhoneNumberModel::factory()->create(['organization_id' => $organization->id]);

        $template = TemplateModel::factory()->create([
            'organization_id' => $organization->id,
            'phone_number_id' => $phoneNumber->id,
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'status' => 'PENDING',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($template);

        $this->assertInstanceOf(Template::class, $domain);
        $this->assertEquals($template->id, $domain->id);
        $this->assertEquals($template->organization_id, $domain->organization_id);
        $this->assertEquals($template->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($template->name, $domain->name);
        $this->assertEquals($template->category, $domain->category);
        $this->assertEquals($template->language, $domain->language);
        $this->assertEquals($template->status, $domain->status);
        $this->assertFalse($domain->is_whatsapp_published); // No WhatsApp template exists
    }

    public function test_build_from_model_with_whatsapp_template()
    {
        $organization = OrganizationModel::factory()->create();
        $template = TemplateModel::factory()->create(['organization_id' => $organization->id]);

        // Create WhatsApp template relationship
        $template->whatsAppTemplate()->create([
            'status' => 'APPROVED',
            'external_id' => 'wa_123',
            'json' => '{"test": "data"}'
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($template);

        $this->assertInstanceOf(Template::class, $domain);
        $this->assertTrue($domain->is_whatsapp_published);
    }

    public function test_build_from_store_request()
    {
        // Skip this test since it requires specific request classes
        $this->markTestSkipped('Store request test requires specific request class implementation');
    }

    public function test_build_from_update_request()
    {
        // Skip this test since it requires specific request classes
        $this->markTestSkipped('Update request test requires specific request class implementation');
    }

    public function test_build_from_save_full_template()
    {
        $templateData = [
            'name' => 'full_template',
            'category' => 'UTILITY',
            'language' => ['code' => 'pt_BR']
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromSaveFullTemplate($templateData, 1, 123);

        $this->assertInstanceOf(Template::class, $domain);
        $this->assertEquals(123, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('full_template', $domain->name);
        $this->assertEquals('UTILITY', $domain->category);
        $this->assertEquals('pt_BR', $domain->language);
        $this->assertEquals('PENDING', $domain->status);
    }

    public function test_build_from_save_full_template_with_defaults()
    {
        $templateData = [
            'name' => 'minimal_template'
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromSaveFullTemplate($templateData, 1);

        $this->assertInstanceOf(Template::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('minimal_template', $domain->name);
        $this->assertEquals('UTILITY', $domain->category); // Default
        $this->assertEquals('en_US', $domain->language); // Default
        $this->assertEquals('PENDING', $domain->status); // Default
    }

    protected function createFactoryInstance()
    {
        return new TemplateFactory();
    }

    protected function getDomainClass(): string
    {
        return Template::class;
    }

    protected function createModelInstance()
    {
        $organization = OrganizationModel::factory()->create();
        return TemplateModel::factory()->create(['organization_id' => $organization->id]);
    }

    protected function createStoreRequest()
    {
        return new class {
            public $organization_id = 1;
            public $phone_number_id = 1;
            public $user_id = 1;
            public $client_id = 1;
            public $name = 'test_template';
            public $category = 'UTILITY';
            public $parameter_format = 'TEXT';
            public $language = 'en_US';
            public $library_template_name = 'library_template';
            public $id_external = 'ext_123';
            public $status = 'PENDING';
        };
    }

    protected function createUpdateRequest()
    {
        return new class {
            public $organization_id = 1;
            public $phone_number_id = 2;
            public $user_id = 2;
            public $client_id = 2;
            public $name = 'updated_template';
            public $category = 'MARKETING';
            public $parameter_format = 'IMAGE';
            public $language = 'es_ES';
            public $library_template_name = 'updated_library';
            public $id_external = 'ext_456';
            public $status = 'APPROVED';
        };
    }
}
