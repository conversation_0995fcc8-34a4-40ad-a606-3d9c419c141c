<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\CategoryFactory;
use App\Domains\ChatBot\Category;
use App\Models\Category as CategoryModel;
use App\Models\Campaign as CampaignModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class CategoryFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(CategoryFactory::class);

        $this->assertInstanceOf(CategoryFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(CategoryFactory::class);
        $factory2 = app()->make(CategoryFactory::class);

        $this->assertInstanceOf(CategoryFactory::class, $factory1);
        $this->assertInstanceOf(CategoryFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->color, $domain->color);
        $this->assertEquals($model->type, $domain->type);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
        $this->assertNull($domain->campaigns);
    }

    public function test_build_from_model_with_campaigns()
    {
        $campaigns = new Collection([
            CampaignModel::factory()->make(['id' => 1]),
            CampaignModel::factory()->make(['id' => 2]),
        ]);

        $model = $this->createModelInstance();
        $model->setRelation('campaigns', $campaigns);
        $model->campaigns_count = 2;

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        // Campaigns should be loaded properly now that circular dependency is resolved
        $this->assertNotNull($domain->campaigns);
        $this->assertIsArray($domain->campaigns);
        $this->assertCount(2, $domain->campaigns);
        foreach ($domain->campaigns as $campaign) {
            $this->assertInstanceOf(\App\Domains\ChatBot\Campaign::class, $campaign);
        }
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'organization_id' => 2,
            'name' => 'Test Category',
            'description' => 'Test Description',
            'color' => '#ff0000',
            'type' => 'campaign',
            'usage_count' => 5,
            'created_at' => '2023-01-01 12:00:00',
            'updated_at' => '2023-01-01 12:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['organization_id'], $domain->organization_id);
        $this->assertEquals($data['name'], $domain->name);
        $this->assertEquals($data['description'], $domain->description);
        $this->assertEquals($data['color'], $domain->color);
        $this->assertEquals($data['type'], $domain->type);
        $this->assertEquals($data['usage_count'], $domain->usage_count);
    }

    public function test_build_from_array_with_defaults()
    {
        $data = [
            'id' => 1,
            'name' => 'Test Category',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['name'], $domain->name);
        $this->assertEquals('#007bff', $domain->color); // Default color
        $this->assertEquals('campaign', $domain->type); // Default type
        $this->assertEquals(0, $domain->usage_count); // Default usage count
    }

    public function test_build_from_array_with_nulls()
    {
        $data = [];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->description);
        $this->assertEquals('#007bff', $domain->color); // Default color
        $this->assertEquals('campaign', $domain->type); // Default type
        $this->assertEquals(0, $domain->usage_count); // Default usage count
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1, 'name' => 'Category 1']),
            $this->createModelInstance(['id' => 2, 'name' => 'Category 2']),
            $this->createModelInstance(['id' => 3, 'name' => 'Category 3']),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
            $this->assertEquals($models[$index]->name, $domain->name);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createFactoryInstance()
    {
        return new CategoryFactory();
    }

    protected function getDomainClass(): string
    {
        return Category::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return CategoryModel::factory()->make(array_merge([
            'id' => 1,
            'organization_id' => 1,
            'name' => 'Test Category',
            'description' => 'Test Description',
            'color' => '#007bff',
            'type' => 'campaign',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }
}
