<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\MessageDeliveryAttemptFactory;
use App\Domains\ChatBot\MessageDeliveryAttempt;
use App\Models\MessageDeliveryAttempt as MessageDeliveryAttemptModel;
use App\Models\Message as MessageModel;
use App\Enums\MessageStatus;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class MessageDeliveryAttemptFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(MessageDeliveryAttemptFactory::class);

        $this->assertInstanceOf(MessageDeliveryAttemptFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(MessageDeliveryAttemptFactory::class);
        $factory2 = app()->make(MessageDeliveryAttemptFactory::class);

        $this->assertInstanceOf(MessageDeliveryAttemptFactory::class, $factory1);
        $this->assertInstanceOf(MessageDeliveryAttemptFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->message_id, $domain->message_id);
        $this->assertEquals($model->attempt_number, $domain->attempt_number);
        $this->assertEquals($model->status, $domain->status);
        $this->assertEquals($model->error_message, $domain->error_message);
        $this->assertEquals($model->whatsapp_response_json, $domain->whatsapp_response_json);
        $this->assertEquals($model->attempted_at, $domain->attempted_at);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
        $this->assertNull($domain->message);
    }

    public function test_build_from_model_with_message()
    {
        $message = MessageModel::factory()->make(['id' => 1]);

        $model = $this->createModelInstance(['message_id' => $message->id]);
        $model->setRelation('message', $message);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->message_id, $domain->message_id);
        // Message should be loaded properly now that circular dependency is resolved
        $this->assertNotNull($domain->message);
        $this->assertInstanceOf(\App\Domains\ChatBot\Message::class, $domain->message);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'message_id' => 2,
            'attempt_number' => 1,
            'status' => MessageStatus::is_sent->value,
            'error_message' => 'Test error',
            'whatsapp_response_json' => ['response' => 'data'],
            'attempted_at' => '2023-01-01 12:00:00',
            'created_at' => '2023-01-01 10:00:00',
            'updated_at' => '2023-01-01 11:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['message_id'], $domain->message_id);
        $this->assertEquals($data['attempt_number'], $domain->attempt_number);
        $this->assertEquals(MessageStatus::is_sent, $domain->status);
        $this->assertEquals($data['error_message'], $domain->error_message);
        $this->assertEquals($data['whatsapp_response_json'], $domain->whatsapp_response_json);
        $this->assertInstanceOf(Carbon::class, $domain->attempted_at);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_array_with_nulls()
    {
        $data = [];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->message_id);
        $this->assertNull($domain->attempt_number);
        $this->assertNull($domain->status);
        $this->assertNull($domain->error_message);
        $this->assertNull($domain->whatsapp_response_json);
        // Domain constructor sets default value to now() when attempted_at is null
        $this->assertInstanceOf(Carbon::class, $domain->attempted_at);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1, 'attempt_number' => 1]),
            $this->createModelInstance(['id' => 2, 'attempt_number' => 2]),
            $this->createModelInstance(['id' => 3, 'attempt_number' => 3]),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
            $this->assertEquals($models[$index]->attempt_number, $domain->attempt_number);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_collection_with_message()
    {
        $message = MessageModel::factory()->make(['id' => 1]);

        $models = [
            $this->createModelInstance(['id' => 1, 'message_id' => $message->id]),
            $this->createModelInstance(['id' => 2, 'message_id' => $message->id]),
        ];

        foreach ($models as $model) {
            $model->setRelation('message', $message);
        }

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models, true);

        $this->assertIsArray($domains);
        $this->assertCount(2, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            // Message should be loaded properly now that circular dependency is resolved
            $this->assertNotNull($domain->message);
            $this->assertInstanceOf(\App\Domains\ChatBot\Message::class, $domain->message);
        }
    }

    protected function createFactoryInstance()
    {
        return new MessageDeliveryAttemptFactory();
    }

    protected function getDomainClass(): string
    {
        return MessageDeliveryAttempt::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return MessageDeliveryAttemptModel::factory()->make(array_merge([
            'id' => 1,
            'message_id' => 1,
            'attempt_number' => 1,
            'status' => MessageStatus::is_sent,
            'error_message' => null,
            'whatsapp_response_json' => ['status' => 'sent'],
            'attempted_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }
}
