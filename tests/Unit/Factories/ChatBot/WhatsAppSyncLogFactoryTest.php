<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\WhatsAppSyncLogFactory;
use App\Domains\ChatBot\WhatsAppSyncLog;
use App\Models\WhatsAppSyncLog as WhatsAppSyncLogModel;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class WhatsAppSyncLogFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(WhatsAppSyncLogFactory::class);

        $this->assertInstanceOf(WhatsAppSyncLogFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(WhatsAppSyncLogFactory::class);
        $factory2 = app()->make(WhatsAppSyncLogFactory::class);

        $this->assertInstanceOf(WhatsAppSyncLogFactory::class, $factory1);
        $this->assertInstanceOf(WhatsAppSyncLogFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->sync_type, $domain->sync_type);
        $this->assertEquals($model->entity_id, $domain->entity_id);
        $this->assertEquals($model->status, $domain->status);
        $this->assertEquals($model->response_data_json, $domain->response_data_json);
        $this->assertEquals($model->error_message, $domain->error_message);
        $this->assertEquals($model->messages_synced, $domain->messages_synced);
        $this->assertEquals($model->messages_updated, $domain->messages_updated);
        $this->assertEquals($model->synced_at, $domain->synced_at);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'sync_type' => SyncType::MESSAGE->value,
            'entity_id' => 123,
            'status' => SyncStatus::SUCCESS->value,
            'response_data_json' => ['response' => 'data'],
            'error_message' => null,
            'messages_synced' => 10,
            'messages_updated' => 5,
            'synced_at' => '2023-01-01 12:00:00',
            'created_at' => '2023-01-01 10:00:00',
            'updated_at' => '2023-01-01 11:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals(SyncType::MESSAGE, $domain->sync_type);
        $this->assertEquals($data['entity_id'], $domain->entity_id);
        $this->assertEquals(SyncStatus::SUCCESS, $domain->status);
        $this->assertEquals($data['response_data_json'], $domain->response_data_json);
        $this->assertEquals($data['error_message'], $domain->error_message);
        $this->assertEquals($data['messages_synced'], $domain->messages_synced);
        $this->assertEquals($data['messages_updated'], $domain->messages_updated);
        $this->assertInstanceOf(Carbon::class, $domain->synced_at);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_array_with_defaults()
    {
        $data = [
            'id' => 1,
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertNull($domain->sync_type);
        $this->assertNull($domain->entity_id);
        $this->assertNull($domain->status);
        $this->assertNull($domain->response_data_json);
        $this->assertNull($domain->error_message);
        $this->assertEquals(0, $domain->messages_synced); // Default value
        $this->assertEquals(0, $domain->messages_updated); // Default value
        // Domain constructor sets default value to now() when synced_at is null
        $this->assertInstanceOf(Carbon::class, $domain->synced_at);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_array_with_nulls()
    {
        $data = [];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->sync_type);
        $this->assertNull($domain->entity_id);
        $this->assertNull($domain->status);
        $this->assertNull($domain->response_data_json);
        $this->assertNull($domain->error_message);
        $this->assertEquals(0, $domain->messages_synced); // Default value
        $this->assertEquals(0, $domain->messages_updated); // Default value
        // Domain constructor sets default value to now() when synced_at is null
        $this->assertInstanceOf(Carbon::class, $domain->synced_at);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1, 'messages_synced' => 10]),
            $this->createModelInstance(['id' => 2, 'messages_synced' => 20]),
            $this->createModelInstance(['id' => 3, 'messages_synced' => 30]),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
            $this->assertEquals($models[$index]->messages_synced, $domain->messages_synced);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_array_with_error()
    {
        $data = [
            'id' => 1,
            'sync_type' => SyncType::CAMPAIGN->value,
            'entity_id' => 456,
            'status' => SyncStatus::FAILED->value,
            'error_message' => 'Sync failed due to API error',
            'messages_synced' => 0,
            'messages_updated' => 0,
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals(SyncType::CAMPAIGN, $domain->sync_type);
        $this->assertEquals($data['entity_id'], $domain->entity_id);
        $this->assertEquals(SyncStatus::FAILED, $domain->status);
        $this->assertEquals($data['error_message'], $domain->error_message);
        $this->assertEquals(0, $domain->messages_synced);
        $this->assertEquals(0, $domain->messages_updated);
    }

    protected function createFactoryInstance()
    {
        return new WhatsAppSyncLogFactory();
    }

    protected function getDomainClass(): string
    {
        return WhatsAppSyncLog::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return WhatsAppSyncLogModel::factory()->make(array_merge([
            'id' => 1,
            'sync_type' => SyncType::MESSAGE,
            'entity_id' => 123,
            'status' => SyncStatus::SUCCESS,
            'response_data_json' => ['status' => 'success'],
            'error_message' => null,
            'messages_synced' => 10,
            'messages_updated' => 5,
            'synced_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }
}
