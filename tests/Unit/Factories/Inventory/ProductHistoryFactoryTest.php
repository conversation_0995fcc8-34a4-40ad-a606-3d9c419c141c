<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ProductHistoryFactory;
use App\Domains\Inventory\ProductHistory;
use App\Models\ProductHistory as ProductHistoryModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductHistoryFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->product_id, $domain->product_id);
        $this->assertEquals($model->field, $domain->field);
        $this->assertEquals($model->alias, $domain->alias);
        $this->assertEquals($model->old, $domain->old);
        $this->assertEquals($model->new, $domain->new);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $model = $this->createModelInstance();
        $model->load(['user', 'product']);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);

        // Test relationships are loaded
        if ($model->user) {
            $this->assertNotNull($domain->user);
            $this->assertEquals($model->user->id, $domain->user->id);
        }

        if ($model->product) {
            $this->assertNotNull($domain->product);
            $this->assertEquals($model->product->id, $domain->product->id);
        }
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('price', $domain->field);
        $this->assertEquals('Preço', $domain->alias);
        $this->assertEquals('10.50', $domain->old);
        $this->assertEquals('12.00', $domain->new);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(2, $domain->product_id);
        $this->assertEquals('name', $domain->field);
        $this->assertEquals('Nome', $domain->alias);
        $this->assertEquals('Old Product', $domain->old);
        $this->assertEquals('New Product', $domain->new);
    }

    public function test_build_from_products()
    {
        $oldProduct = new \App\Domains\Inventory\Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 10.50,
            unity: 1,
            last_priced_at: \Carbon\Carbon::now()
        );

        $newProduct = new \App\Domains\Inventory\Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 15.75,
            unity: 1,
            last_priced_at: \Carbon\Carbon::now()
        );

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromProducts($newProduct, $oldProduct);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('price', $domain->field);
        $this->assertEquals('Preço', $domain->alias);
        $this->assertEquals(10.50, $domain->old);
        $this->assertEquals(15.75, $domain->new);
    }

    public function test_build_from_model_with_different_fields()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
            ['field' => 'unity', 'alias' => 'Unidade', 'old' => '1', 'new' => '2'],
        ];

        foreach ($fields as $fieldData) {
            $model = ProductHistoryModel::factory()->create($fieldData);
            $factory = $this->createFactoryInstance();
            $domain = $factory->buildFromModel($model);

            $this->assertInstanceOf(ProductHistory::class, $domain);
            $this->assertEquals($fieldData['field'], $domain->field);
            $this->assertEquals($fieldData['alias'], $domain->alias);
            $this->assertEquals($fieldData['old'], $domain->old);
            $this->assertEquals($fieldData['new'], $domain->new);
        }
    }

    public function test_build_from_model_with_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '0.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $priceChange) {
            $model = ProductHistoryModel::factory()->create([
                'field' => 'price',
                'alias' => 'Preço',
                'old' => $priceChange['old'],
                'new' => $priceChange['new']
            ]);

            $factory = $this->createFactoryInstance();
            $domain = $factory->buildFromModel($model);

            $this->assertInstanceOf(ProductHistory::class, $domain);
            $this->assertEquals($priceChange['old'], $domain->old);
            $this->assertEquals($priceChange['new'], $domain->new);
        }
    }

    public function test_build_from_model_with_long_values()
    {
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);

        $model = ProductHistoryModel::factory()->create([
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => $longOldValue,
            'new' => $longNewValue
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertEquals($longOldValue, $domain->old);
        $this->assertEquals($longNewValue, $domain->new);
    }

    public function test_build_from_model_with_null_values()
    {
        $model = ProductHistoryModel::factory()->create([
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => '',
            'new' => ''
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertEquals('', $domain->old);
        $this->assertEquals('', $domain->new);
    }

    public function test_build_from_store_request_with_different_fields()
    {
        $request = new \App\Http\Requests\ProductHistory\StoreRequest();
        $request->merge([
            'product_id' => 5,
            'field' => 'name',
            'alias' => 'Nome',
            'old' => 'Old Product Name',
            'new' => 'New Product Name',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertEquals(5, $domain->product_id);
        $this->assertEquals('name', $domain->field);
        $this->assertEquals('Nome', $domain->alias);
        $this->assertEquals('Old Product Name', $domain->old);
        $this->assertEquals('New Product Name', $domain->new);
    }

    public function test_build_from_update_request_with_different_fields()
    {
        $request = new \App\Http\Requests\ProductHistory\UpdateRequest();
        $request->merge([
            'product_id' => 7,
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => 'Old Description',
            'new' => 'New Description',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(ProductHistory::class, $domain);
        $this->assertEquals(7, $domain->product_id);
        $this->assertEquals('description', $domain->field);
        $this->assertEquals('Descrição', $domain->alias);
        $this->assertEquals('Old Description', $domain->old);
        $this->assertEquals('New Description', $domain->new);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve dependencies
        return app()->make(ProductHistoryFactory::class);
    }

    protected function getDomainClass(): string
    {
        return ProductHistory::class;
    }

    protected function createModelInstance()
    {
        return ProductHistoryModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\ProductHistory\StoreRequest();
        $request->merge([
            'user_id' => 1,
            'product_id' => 1,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '12.00',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\ProductHistory\UpdateRequest();
        $request->merge([
            'user_id' => 2,
            'product_id' => 2,
            'field' => 'name',
            'alias' => 'Nome',
            'old' => 'Old Product',
            'new' => 'New Product',
        ]);
        return $request;
    }
}
