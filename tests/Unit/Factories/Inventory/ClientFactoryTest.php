<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ClientFactory;
use App\Domains\Inventory\Client;
use App\Models\Client as ClientModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ClientFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->phone, $domain->phone);
        $this->assertEquals($model->email, $domain->email);
        $this->assertEquals($model->profession, $domain->profession);
        $this->assertEquals($model->birthdate, $domain->birthdate);
        $this->assertEquals($model->cpf, $domain->cpf);
        $this->assertEquals($model->cnpj, $domain->cnpj);
        $this->assertEquals($model->service, $domain->service);
        $this->assertEquals($model->address, $domain->address);
        $this->assertEquals($model->number, $domain->number);
        $this->assertEquals($model->neighborhood, $domain->neighborhood);
        $this->assertEquals($model->cep, $domain->cep);
        $this->assertEquals($model->complement, $domain->complement);
        $this->assertEquals($model->civil_state, $domain->civil_state);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals($request->organization_id, $domain->organization_id);
        $this->assertEquals($request->name, $domain->name);
        $this->assertEquals($request->phone, $domain->phone);
        $this->assertEquals($request->email, $domain->email);
        $this->assertEquals($request->profession, $domain->profession);
        $this->assertEquals($request->birthdate, $domain->birthdate);
        $this->assertEquals($request->cpf, $domain->cpf);
        $this->assertEquals($request->cnpj, $domain->cnpj);
        $this->assertEquals($request->service, $domain->service);
        $this->assertEquals($request->address, $domain->address);
        $this->assertEquals($request->number, $domain->number);
        $this->assertEquals($request->neighborhood, $domain->neighborhood);
        $this->assertEquals($request->cep, $domain->cep);
        $this->assertEquals($request->complement, $domain->complement);
        $this->assertEquals($request->civil_state, $domain->civil_state);
        $this->assertEquals($request->description, $domain->description);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals($request->name, $domain->name);
        $this->assertEquals($request->phone, $domain->phone);
        $this->assertEquals($request->email, $domain->email);
        $this->assertEquals($request->profession, $domain->profession);
        $this->assertEquals($request->birthdate, $domain->birthdate);
        $this->assertEquals($request->cpf, $domain->cpf);
        $this->assertEquals($request->cnpj, $domain->cnpj);
        $this->assertEquals($request->service, $domain->service);
        $this->assertEquals($request->address, $domain->address);
        $this->assertEquals($request->number, $domain->number);
        $this->assertEquals($request->neighborhood, $domain->neighborhood);
        $this->assertEquals($request->cep, $domain->cep);
        $this->assertEquals($request->complement, $domain->complement);
        $this->assertEquals($request->civil_state, $domain->civil_state);
        $this->assertEquals($request->description, $domain->description);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ClientFactory::class);

        $this->assertInstanceOf(ClientFactory::class, $factory);
    }

    public function test_factory_can_handle_different_client_types()
    {
        $individualClient = ClientModel::factory()->individual()->create();
        $companyClient = ClientModel::factory()->company()->create();
        $clientWithAddress = ClientModel::factory()->withCompleteAddress()->create();
        $minimalClient = ClientModel::factory()->minimal()->create();

        $factory = $this->createFactoryInstance();

        $individualDomain = $factory->buildFromModel($individualClient);
        $companyDomain = $factory->buildFromModel($companyClient);
        $addressDomain = $factory->buildFromModel($clientWithAddress);
        $minimalDomain = $factory->buildFromModel($minimalClient);

        $this->assertInstanceOf(Client::class, $individualDomain);
        $this->assertNotNull($individualDomain->cpf);
        $this->assertNull($individualDomain->cnpj);

        $this->assertInstanceOf(Client::class, $companyDomain);
        $this->assertNull($companyDomain->cpf);
        $this->assertNotNull($companyDomain->cnpj);

        $this->assertInstanceOf(Client::class, $addressDomain);
        $this->assertNotNull($addressDomain->address);
        $this->assertNotNull($addressDomain->cep);

        $this->assertInstanceOf(Client::class, $minimalDomain);
        $this->assertNotNull($minimalDomain->phone);
    }

    public function test_build_from_models_collection()
    {
        $client1 = ClientModel::factory()->individual()->create();
        $client2 = ClientModel::factory()->company()->create();
        $client3 = ClientModel::factory()->withCompleteAddress()->create();

        $collection = collect([$client1, $client2, $client3]);
        $factory = $this->createFactoryInstance();

        $domains = $factory->buildFromModels($collection);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Client::class, $domain);
        }

        // Test specific client types
        $this->assertNotNull($domains[0]->cpf);
        $this->assertNull($domains[0]->cnpj);

        $this->assertNull($domains[1]->cpf);
        $this->assertNotNull($domains[1]->cnpj);

        $this->assertNotNull($domains[2]->address);
        $this->assertNotNull($domains[2]->cep);
    }

    public function test_build_from_models_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(collect([]));

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_models_null_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(null);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Client\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'Test Client',
            'phone' => null,
            'email' => null,
            'profession' => null,
            'birthdate' => null,
            'cpf' => null,
            'cnpj' => null,
            'service' => null,
            'address' => null,
            'number' => null,
            'neighborhood' => null,
            'cep' => null,
            'complement' => null,
            'civil_state' => null,
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Client', $domain->name);
        $this->assertNull($domain->phone);
        $this->assertNull($domain->email);
        $this->assertNull($domain->cpf);
        $this->assertNull($domain->cnpj);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new \App\Http\Requests\Client\UpdateRequest();
        $request->merge([
            'name' => 'Updated Client',
            'phone' => null,
            'email' => null,
            'profession' => null,
            'birthdate' => null,
            'cpf' => null,
            'cnpj' => null,
            'service' => null,
            'address' => null,
            'number' => null,
            'neighborhood' => null,
            'cep' => null,
            'complement' => null,
            'civil_state' => null,
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals('Updated Client', $domain->name);
        $this->assertNull($domain->phone);
        $this->assertNull($domain->email);
        $this->assertNull($domain->cpf);
        $this->assertNull($domain->cnpj);
    }

    public function test_build_from_model_with_dates()
    {
        $model = ClientModel::factory()->create([
            'created_at' => now()->subDays(5),
            'updated_at' => now()->subDays(2),
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    protected function createFactoryInstance()
    {
        return new ClientFactory();
    }

    protected function getDomainClass(): string
    {
        return Client::class;
    }

    protected function createModelInstance()
    {
        return ClientModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Client\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'John Doe',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'profession' => 'Engineer',
            'birthdate' => '1990-01-01',
            'cpf' => '12345678901',
            'cnpj' => null,
            'service' => 'Software Development',
            'address' => '123 Main St',
            'number' => '123',
            'neighborhood' => 'Downtown',
            'cep' => '12345-678',
            'complement' => 'Apt 1',
            'civil_state' => 'single',
            'description' => 'Test client',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Client\UpdateRequest();
        $request->merge([
            'name' => 'Jane Smith',
            'phone' => '+0987654321',
            'email' => '<EMAIL>',
            'profession' => 'Designer',
            'birthdate' => '1985-05-15',
            'cpf' => '98765432109',
            'cnpj' => null,
            'service' => 'Graphic Design',
            'address' => '456 Oak Ave',
            'number' => '456',
            'neighborhood' => 'Uptown',
            'cep' => '98765-432',
            'complement' => 'Suite 2',
            'civil_state' => 'married',
            'description' => 'Updated client',
        ]);
        return $request;
    }
}
