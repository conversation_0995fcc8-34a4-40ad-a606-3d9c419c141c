<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\GroupFactory;
use App\Domains\Inventory\Group;
use App\Models\Group as GroupModel;
use App\Models\Organization;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class GroupFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_model_with_specific_values()
    {
        $organization = Organization::factory()->create();
        $model = GroupModel::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Specific Group',
            'description' => 'Specific group description',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($organization->id, $domain->organization_id);
        $this->assertEquals('Specific Group', $domain->name);
        $this->assertEquals('Specific group description', $domain->description);
    }

    public function test_build_from_model_with_null_description()
    {
        $model = GroupModel::factory()->create([
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->description);
    }

    public function test_build_from_store_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createStoreRequest();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Group', $domain->name);
        $this->assertEquals('Test group description', $domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Group\StoreRequest();
        $request->merge([]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->description);
    }

    public function test_build_from_store_request_with_empty_description()
    {
        $request = new \App\Http\Requests\Group\StoreRequest();
        $request->merge([
            'organization_id' => 2,
            'name' => 'Group Without Description',
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(2, $domain->organization_id);
        $this->assertEquals('Group Without Description', $domain->name);
        $this->assertNull($domain->description);
    }

    public function test_build_from_update_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createUpdateRequest();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id); // Should be null for update
        $this->assertEquals('Updated Group', $domain->name);
        $this->assertEquals('Updated group description', $domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_update_request_with_partial_data()
    {
        $request = new \App\Http\Requests\Group\UpdateRequest();
        $request->merge([
            'name' => 'Partially Updated Group',
            // description not provided
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals('Partially Updated Group', $domain->name);
        $this->assertNull($domain->description);
    }

    public function test_build_from_update_request_with_empty_description()
    {
        $request = new \App\Http\Requests\Group\UpdateRequest();
        $request->merge([
            'name' => 'Group With Empty Description',
            'description' => '',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals('Group With Empty Description', $domain->name);
        $this->assertEquals('', $domain->description);
    }

    public function test_build_from_update_request_description_only()
    {
        $request = new \App\Http\Requests\Group\UpdateRequest();
        $request->merge([
            'description' => 'Only description updated',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->name);
        $this->assertEquals('Only description updated', $domain->description);
    }

    protected function createFactoryInstance()
    {
        return new GroupFactory();
    }

    protected function getDomainClass(): string
    {
        return Group::class;
    }

    protected function createModelInstance()
    {
        return GroupModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Group\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'Test Group',
            'description' => 'Test group description',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Group\UpdateRequest();
        $request->merge([
            'name' => 'Updated Group',
            'description' => 'Updated group description',
        ]);
        return $request;
    }
}
