<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\BrandFactory;
use App\Domains\Inventory\Brand;
use App\Models\Brand as BrandModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BrandFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createMockStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Store Brand', $domain->name);
        $this->assertEquals('Store brand description', $domain->description);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new class extends \App\Http\Requests\Brand\StoreRequest {
            public $organization_id = null;
            public $name = null;
            public $description = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals('', $domain->name);
        $this->assertEquals('', $domain->description);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createMockUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Updated Brand', $domain->name);
        $this->assertEquals('Updated brand description', $domain->description);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new class extends \App\Http\Requests\Brand\UpdateRequest {
            public $organization_id = null;
            public $name = null;
            public $description = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals('', $domain->name);
        $this->assertEquals('', $domain->description);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(BrandFactory::class);

        $this->assertInstanceOf(BrandFactory::class, $factory);
    }

    public function test_factory_can_handle_different_brand_types()
    {
        $technologyBrand = BrandModel::factory()->technology()->create();
        $fashionBrand = BrandModel::factory()->fashion()->create();
        $customBrand = BrandModel::factory()->withName('Custom Brand')->create();

        $factory = $this->createFactoryInstance();

        $technologyDomain = $factory->buildFromModel($technologyBrand);
        $fashionDomain = $factory->buildFromModel($fashionBrand);
        $customDomain = $factory->buildFromModel($customBrand);

        $this->assertInstanceOf(Brand::class, $technologyDomain);
        $this->assertInstanceOf(Brand::class, $fashionDomain);
        $this->assertInstanceOf(Brand::class, $customDomain);
        $this->assertEquals('Custom Brand', $customDomain->name);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    protected function createFactoryInstance()
    {
        return new BrandFactory();
    }

    protected function getDomainClass(): string
    {
        return Brand::class;
    }

    protected function createModelInstance()
    {
        return BrandModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        return new class {
            public $organization_id = 1;
            public $name = 'Test Brand';
            public $description = 'Test Description';
        };
    }

    public function test_build_from_model_with_empty_description()
    {
        $model = BrandModel::factory()->create(['description' => '']);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals('', $domain->description);
    }

    public function test_build_from_model_with_null_description()
    {
        $model = BrandModel::factory()->create(['description' => null]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals('', $domain->description); // Factory converts null to empty string
    }

    public function test_build_from_model_with_long_name()
    {
        $longName = str_repeat('A', 255);
        $model = BrandModel::factory()->create(['name' => $longName]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($longName, $domain->name);
    }

    public function test_build_from_model_with_special_characters()
    {
        $specialName = 'Brand & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';
        $model = BrandModel::factory()->create([
            'name' => $specialName,
            'description' => $specialDescription
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($specialName, $domain->name);
        $this->assertEquals($specialDescription, $domain->description);
    }

    public function test_build_from_model_with_unicode_characters()
    {
        $unicodeName = 'Bränd Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';
        $model = BrandModel::factory()->create([
            'name' => $unicodeName,
            'description' => $unicodeDescription
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Brand::class, $domain);
        $this->assertEquals($unicodeName, $domain->name);
        $this->assertEquals($unicodeDescription, $domain->description);
    }

    public function test_factory_handles_different_organization_ids()
    {
        $org1Brand = BrandModel::factory()->create(['organization_id' => 1]);
        $org2Brand = BrandModel::factory()->create(['organization_id' => 999]);

        $factory = $this->createFactoryInstance();

        $org1Domain = $factory->buildFromModel($org1Brand);
        $org2Domain = $factory->buildFromModel($org2Brand);

        $this->assertEquals(1, $org1Domain->organization_id);
        $this->assertEquals(999, $org2Domain->organization_id);
    }

    protected function createUpdateRequest()
    {
        return new class {
            public $organization_id = 1;
            public $name = 'Updated Brand';
            public $description = 'Updated Description';
        };
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $organization_id = 1;
            public $name = 'Store Brand';
            public $description = 'Store brand description';
        };
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Brand\UpdateRequest {
            public $organization_id = 1;
            public $name = 'Updated Brand';
            public $description = 'Updated brand description';
        };
    }
}
