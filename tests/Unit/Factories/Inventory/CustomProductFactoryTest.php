<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\CustomProductFactory;
use App\Domains\Inventory\CustomProduct;
use App\Models\CustomProduct as CustomProductModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CustomProductFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->project_id, $domain->project_id);
        $this->assertEquals($model->budget_id, $domain->budget_id);
        $this->assertEquals($model->quantity, $domain->quantity);
        $this->assertEquals($model->value, $domain->value);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $model = $this->createModelInstance();
        $model->load(['project', 'budget']);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);

        // Test relationships are loaded
        if ($model->project) {
            $this->assertNotNull($domain->project);
            $this->assertEquals($model->project->id, $domain->project->id);
        }

        if ($model->budget) {
            $this->assertNotNull($domain->budget);
            $this->assertEquals($model->budget->id, $domain->budget->id);
        }
    }

    public function test_build_from_model_without_project()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        // Project should not be loaded when with_project = false
    }

    public function test_build_from_model_without_budget()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, false);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        // Budget should not be loaded when with_budget = false
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->project_id);
        $this->assertEquals(1, $domain->budget_id);
        $this->assertEquals(10, $domain->quantity);
        $this->assertEquals(150.50, $domain->value);
        $this->assertEquals('Test custom product', $domain->description);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(2, $domain->project_id);
        $this->assertEquals(2, $domain->budget_id);
        $this->assertEquals(20, $domain->quantity);
        $this->assertEquals(200.75, $domain->value);
        $this->assertEquals('Updated custom product', $domain->description);
    }

    public function test_build_from_model_with_different_quantities()
    {
        $model = CustomProductModel::factory()->create(['quantity' => 500]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(500, $domain->quantity);
    }

    public function test_build_from_model_with_different_values()
    {
        $model = CustomProductModel::factory()->create(['value' => 9999.99]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(9999.99, $domain->value);
    }

    public function test_build_from_model_with_zero_quantity()
    {
        $model = CustomProductModel::factory()->create(['quantity' => 0]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(0, $domain->quantity);
    }

    public function test_build_from_model_with_high_quantity()
    {
        $model = CustomProductModel::factory()->create(['quantity' => 999999]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(999999, $domain->quantity);
    }

    public function test_build_from_model_with_decimal_values()
    {
        $model = CustomProductModel::factory()->create(['value' => 123.456789]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(123.456789, $domain->value);
    }

    public function test_build_from_model_with_long_description()
    {
        $longDescription = str_repeat('Long description text. ', 20);
        $model = CustomProductModel::factory()->create(['description' => $longDescription]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals($longDescription, $domain->description);
    }

    public function test_build_from_model_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            $model = CustomProductModel::factory()->create($testCase);
            $factory = $this->createFactoryInstance();
            $domain = $factory->buildFromModel($model);

            $this->assertInstanceOf(CustomProduct::class, $domain);
            $this->assertEquals($testCase['quantity'], $domain->quantity);
            $this->assertEquals($testCase['value'], $domain->value);
        }
    }

    public function test_build_from_store_request_with_different_values()
    {
        $request = new \App\Http\Requests\CustomProduct\StoreRequest();
        $request->merge([
            'project_id' => 5,
            'budget_id' => 3,
            'quantity' => 25,
            'value' => 375.25,
            'description' => 'Different values custom product',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(5, $domain->project_id);
        $this->assertEquals(3, $domain->budget_id);
        $this->assertEquals(25, $domain->quantity);
        $this->assertEquals(375.25, $domain->value);
        $this->assertEquals('Different values custom product', $domain->description);
    }

    public function test_build_from_update_request_with_different_values()
    {
        $request = new \App\Http\Requests\CustomProduct\UpdateRequest();
        $request->merge([
            'project_id' => 7,
            'budget_id' => 4,
            'quantity' => 50,
            'value' => 750.00,
            'description' => 'Different update values custom product',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(CustomProduct::class, $domain);
        $this->assertEquals(7, $domain->project_id);
        $this->assertEquals(4, $domain->budget_id);
        $this->assertEquals(50, $domain->quantity);
        $this->assertEquals(750.00, $domain->value);
        $this->assertEquals('Different update values custom product', $domain->description);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve dependencies
        return app()->make(CustomProductFactory::class);
    }

    protected function getDomainClass(): string
    {
        return CustomProduct::class;
    }

    protected function createModelInstance()
    {
        return CustomProductModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\CustomProduct\StoreRequest();
        $request->merge([
            'budget_id' => 1,
            'project_id' => 1,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test custom product',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\CustomProduct\UpdateRequest();
        $request->merge([
            'budget_id' => 2,
            'project_id' => 2,
            'quantity' => 20,
            'value' => 200.75,
            'description' => 'Updated custom product',
        ]);
        return $request;
    }
}
