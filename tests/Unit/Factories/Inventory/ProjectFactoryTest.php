<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ProjectFactory;
use App\Domains\Inventory\Project;
use App\Models\Project as ProjectModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProjectFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->budget_id, $domain->budget_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->value, $domain->value);
        $this->assertEquals($model->cost, $domain->cost);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $model = $this->createModelInstance();
        $model->load(['client', 'budget']);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true, false, false);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        // Client and budget should be loaded based on the with_ parameters
        $this->assertNull($domain->products);
        $this->assertNull($domain->customProducts);
    }

    public function test_build_from_model_without_relationships()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, false, false, false);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->client);
        $this->assertNull($domain->budget);
        $this->assertNull($domain->products);
        $this->assertNull($domain->customProducts);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve dependencies
        return app()->make(ProjectFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Project::class;
    }

    protected function createModelInstance()
    {
        return ProjectModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Project\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'client_id' => 1,
            'budget_id' => 1,
            'name' => 'Test Project',
            'description' => 'Test project description',
            'value' => 5000.50,
            'cost' => 4000.00,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Project\UpdateRequest();
        $request->merge([
            'client_id' => 2,
            'budget_id' => 2,
            'name' => 'Updated Project',
            'description' => 'Updated project description',
            'value' => 7500.75,
            'cost' => 6000.00,
        ]);
        return $request;
    }

    public function test_build_from_budget()
    {
        $budget = new \App\Domains\Inventory\Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $request = new \Illuminate\Http\Request([
            'name' => 'Custom Project Name',
            'description' => 'Custom project description'
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromBudget($budget, $request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(1, $domain->budget_id);
        $this->assertEquals('Custom Project Name', $domain->name);
        $this->assertEquals('Custom project description', $domain->description);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
    }

    public function test_build_from_budget_with_default_values()
    {
        $budget = new \App\Domains\Inventory\Budget(
            id: 5,
            organization_id: 1,
            client_id: 2,
            name: 'Source Budget',
            description: 'Source budget description',
            value: 25000.00,
            cost: 18000.00,
            created_at: now(),
            updated_at: now()
        );

        $request = new \Illuminate\Http\Request(); // Empty request

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromBudget($budget, $request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals('Project from budget #5', $domain->name);
        $this->assertEquals('Source budget description', $domain->description);
        $this->assertEquals(2, $domain->client_id);
        $this->assertEquals(5, $domain->budget_id);
    }

    public function test_build_array_from_budget()
    {
        $budgetModel = \App\Models\Budget::factory()->create();

        // Create projects associated with the budget
        $project1 = \App\Models\Project::factory()->create(['budget_id' => $budgetModel->id]);
        $project2 = \App\Models\Project::factory()->create(['budget_id' => $budgetModel->id]);

        $budgetModel->load('projects');

        $factory = $this->createFactoryInstance();
        $projects = $factory->buildArrayFromBudget($budgetModel);

        $this->assertIsArray($projects);
        $this->assertCount(2, $projects);

        foreach ($projects as $project) {
            $this->assertInstanceOf($this->getDomainClass(), $project);
            $this->assertEquals($budgetModel->id, $project->budget_id);
        }
    }

    public function test_build_array_from_budget_empty()
    {
        $budgetModel = \App\Models\Budget::factory()->create();
        $budgetModel->load('projects');

        $factory = $this->createFactoryInstance();
        $projects = $factory->buildArrayFromBudget($budgetModel);

        $this->assertIsArray($projects);
        $this->assertEmpty($projects);
    }

    public function test_factory_handles_different_project_values()
    {
        // High value project
        $highValueProject = \App\Models\Project::factory()->create([
            'value' => 100000.00,
            'cost' => 75000.00
        ]);

        // Zero value project
        $zeroValueProject = \App\Models\Project::factory()->create([
            'value' => 0.00,
            'cost' => 0.00
        ]);

        $factory = $this->createFactoryInstance();

        $highValueDomain = $factory->buildFromModel($highValueProject);
        $zeroValueDomain = $factory->buildFromModel($zeroValueProject);

        $this->assertEquals(100000.00, $highValueDomain->value);
        $this->assertEquals(75000.00, $highValueDomain->cost);
        $this->assertEquals(0.00, $zeroValueDomain->value);
        $this->assertEquals(0.00, $zeroValueDomain->cost);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Project\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'client_id' => null,
            'budget_id' => null,
            'name' => 'Minimal Project',
            'description' => null,
            'value' => null,
            'cost' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->budget_id);
        $this->assertEquals('Minimal Project', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new \App\Http\Requests\Project\UpdateRequest();
        $request->merge([
            'client_id' => null,
            'budget_id' => null,
            'name' => 'Updated Project',
            'description' => null,
            'value' => null,
            'cost' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->budget_id);
        $this->assertEquals('Updated Project', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
    }
}
