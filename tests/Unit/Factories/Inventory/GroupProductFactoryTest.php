<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\GroupProductFactory;
use App\Domains\Inventory\GroupProduct;
use App\Models\GroupProduct as GroupProductModel;
use App\Models\Group;
use App\Models\Product;
use App\Models\Organization;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class GroupProductFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->group_id, $domain->group_id);
        $this->assertEquals($model->product_id, $domain->product_id);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $organization = Organization::factory()->create();
        $group = Group::factory()->create(['organization_id' => $organization->id]);
        $product = Product::factory()->create(['organization_id' => $organization->id]);

        $model = GroupProductModel::factory()->create([
            'group_id' => $group->id,
            'product_id' => $product->id,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($group->id, $domain->group_id);
        $this->assertEquals($product->id, $domain->product_id);

        // Note: Relationships are loaded but may be null depending on eager loading
        // This tests the factory's ability to handle relationship loading
    }

    public function test_build_from_model_with_null_model()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_model_with_specific_values()
    {
        $organization = Organization::factory()->create();
        $group = Group::factory()->create(['organization_id' => $organization->id]);
        $product = Product::factory()->create(['organization_id' => $organization->id]);

        $model = GroupProductModel::factory()->create([
            'group_id' => $group->id,
            'product_id' => $product->id,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($group->id, $domain->group_id);
        $this->assertEquals($product->id, $domain->product_id);
    }

    public function test_build_from_store_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createStoreRequest();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->group_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->group);
        $this->assertNull($domain->product);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->group_id);
        $this->assertNull($domain->product_id);
    }

    public function test_build_from_store_request_with_specific_ids()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([
            'group_id' => 5,
            'product_id' => 10,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(5, $domain->group_id);
        $this->assertEquals(10, $domain->product_id);
    }

    public function test_build_from_store_request_with_partial_data()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([
            'group_id' => 3,
            // product_id not provided
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(3, $domain->group_id);
        $this->assertNull($domain->product_id);
    }

    public function test_build_from_store_request_with_zero_ids()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([
            'group_id' => 0,
            'product_id' => 0,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(0, $domain->group_id);
        $this->assertEquals(0, $domain->product_id);
    }

    public function test_build_from_store_request_with_string_ids()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([
            'group_id' => '7',
            'product_id' => '14',
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals('7', $domain->group_id);
        $this->assertEquals('14', $domain->product_id);
    }

    protected function createFactoryInstance()
    {
        return app()->make(GroupProductFactory::class);
    }

    protected function getDomainClass(): string
    {
        return GroupProduct::class;
    }

    protected function createModelInstance()
    {
        return GroupProductModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\GroupProduct\StoreRequest();
        $request->merge([
            'group_id' => 1,
            'product_id' => 1,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\GroupProduct\UpdateRequest();
        $request->merge([
            'group_id' => 2,
            'product_id' => 2,
        ]);
        return $request;
    }
}
