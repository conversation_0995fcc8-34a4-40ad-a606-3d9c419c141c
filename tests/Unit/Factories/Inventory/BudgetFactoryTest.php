<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\BudgetFactory;
use App\Domains\Inventory\Budget;
use App\Models\Budget as BudgetModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BudgetFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->value, $domain->value);
        $this->assertEquals($model->cost, $domain->cost);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_client_relationship()
    {
        $client = \App\Models\Client::factory()->create();
        $model = BudgetModel::factory()->create(['client_id' => $client->id]);
        $model->load('client');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNotNull($domain->client);
        $this->assertEquals($client->name, $domain->client->name);
        $this->assertEquals($client->id, $domain->client_id);
    }

    public function test_build_from_model_without_client_relationship()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->client);
    }

    public function test_build_from_model_with_products_relationship()
    {
        $model = $this->createModelInstance();
        $product = \App\Models\Product::factory()->create();
        $budgetProduct = \App\Models\BudgetProduct::factory()->create([
            'budget_id' => $model->id,
            'product_id' => $product->id
        ]);
        $model->load('products');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNotNull($domain->products);
        $this->assertIsArray($domain->products);
        $this->assertCount(1, $domain->products);
    }

    public function test_build_from_model_with_custom_products_relationship()
    {
        $model = $this->createModelInstance();
        $customProduct = \App\Models\CustomProduct::factory()->create([
            'budget_id' => $model->id
        ]);
        $model->load('custom_products');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true, true, true);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNotNull($domain->customProducts);
        $this->assertIsArray($domain->customProducts);
        $this->assertCount(1, $domain->customProducts);
    }

    public function test_build_from_model_with_projects_relationship()
    {
        $model = $this->createModelInstance();
        $project = \App\Models\Project::factory()->create(['budget_id' => $model->id]);
        $model->load('projects');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNotNull($domain->projects);
        $this->assertIsArray($domain->projects);
        $this->assertCount(1, $domain->projects);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(1500.50, $domain->value);
        $this->assertEquals(1200.00, $domain->cost);
        $this->assertEquals('Test Budget', $domain->name);
        $this->assertEquals('Test budget description', $domain->description);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Budget\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'client_id' => null,
            'value' => null,
            'cost' => null,
            'name' => 'Minimal Budget',
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
        $this->assertEquals('Minimal Budget', $domain->name);
        $this->assertNull($domain->description);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve circular dependencies
        return app()->make(BudgetFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Budget::class;
    }

    protected function createModelInstance()
    {
        return BudgetModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Budget\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'client_id' => 1,
            'value' => 1500.50,
            'cost' => 1200.00,
            'name' => 'Test Budget',
            'description' => 'Test budget description',
        ]);
        return $request;
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals(2, $domain->client_id);
        $this->assertEquals(2000.75, $domain->value);
        $this->assertEquals(1800.00, $domain->cost);
        $this->assertEquals('Updated Budget', $domain->name);
        $this->assertEquals('Updated budget description', $domain->description);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new \App\Http\Requests\Budget\UpdateRequest();
        $request->merge([
            'client_id' => null,
            'value' => null,
            'cost' => null,
            'name' => null,
            'description' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
        $this->assertEquals("", $domain->name);
        $this->assertNull($domain->description);
    }

    public function test_build_from_model_array_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray(collect([]));

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_model_array_with_mixed_budgets()
    {
        $models = collect([
            BudgetModel::factory()->create(['value' => 1000.0, 'cost' => 800.0]),
            BudgetModel::factory()->create(['value' => 2000.0, 'cost' => 1500.0]),
            BudgetModel::factory()->create(['value' => 0.0, 'cost' => 0.0]),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Budget::class, $domain);
        }

        // Verify different values are preserved
        $this->assertEquals(1000.0, $domains[0]->value);
        $this->assertEquals(2000.0, $domains[1]->value);
        $this->assertEquals(0.0, $domains[2]->value);
    }

    public function test_factory_handles_different_value_ranges()
    {
        // Zero value budget
        $zeroBudget = BudgetModel::factory()->create(['value' => 0.0, 'cost' => 0.0]);
        // High value budget
        $highBudget = BudgetModel::factory()->create(['value' => 999999.99, 'cost' => 888888.88]);
        // Decimal value budget
        $decimalBudget = BudgetModel::factory()->create(['value' => 1234.56, 'cost' => 987.65]);

        $factory = $this->createFactoryInstance();

        $zeroDomain = $factory->buildFromModel($zeroBudget);
        $highDomain = $factory->buildFromModel($highBudget);
        $decimalDomain = $factory->buildFromModel($decimalBudget);

        $this->assertEquals(0.0, $zeroDomain->value);
        $this->assertEquals(999999.99, $highDomain->value);
        $this->assertEquals(1234.56, $decimalDomain->value);
    }

    public function test_factory_handles_null_client()
    {
        $nullClient = BudgetModel::factory()->create(['client_id' => null]);

        $factory = $this->createFactoryInstance();
        $nullClientDomain = $factory->buildFromModel($nullClient);

        $this->assertNull($nullClientDomain->client_id);
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Budget\UpdateRequest();
        $request->merge([
            'client_id' => 2,
            'value' => 2000.75,
            'cost' => 1800.00,
            'name' => 'Updated Budget',
            'description' => 'Updated budget description',
        ]);
        return $request;
    }


}
