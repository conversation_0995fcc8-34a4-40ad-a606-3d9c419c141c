<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\DepartmentFactory;
use App\Domains\Inventory\Department;
use App\Models\Department as DepartmentModel;
use App\Models\Organization;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class DepartmentFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->is_active, $domain->is_active);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_model_with_specific_values()
    {
        $organization = Organization::factory()->create();
        $model = DepartmentModel::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Specific Department',
            'is_active' => false,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($organization->id, $domain->organization_id);
        $this->assertEquals('Specific Department', $domain->name);
        $this->assertFalse($domain->is_active);
    }

    public function test_build_from_store_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createStoreRequest();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Department', $domain->name);
        $this->assertTrue($domain->is_active);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Department\StoreRequest();
        $request->merge([]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->is_active);
    }

    public function test_build_from_update_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createUpdateRequest();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id); // Should be null for update
        $this->assertEquals('Updated Department', $domain->name);
        $this->assertFalse($domain->is_active);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_update_request_with_partial_data()
    {
        $request = new \App\Http\Requests\Department\UpdateRequest();
        $request->merge([
            'name' => 'Partially Updated Department',
            // is_active not provided
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals('Partially Updated Department', $domain->name);
        $this->assertNull($domain->is_active);
    }

    protected function createFactoryInstance()
    {
        return new DepartmentFactory();
    }

    protected function getDomainClass(): string
    {
        return Department::class;
    }

    protected function createModelInstance()
    {
        return DepartmentModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Department\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'name' => 'Test Department',
            'is_active' => true,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Department\UpdateRequest();
        $request->merge([
            'name' => 'Updated Department',
            'is_active' => false,
        ]);
        return $request;
    }
}
