<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\BudgetProductFactory;
use App\Domains\Inventory\BudgetProduct;
use App\Models\BudgetProduct as BudgetProductModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BudgetProductFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->budget_id, $domain->budget_id);
        $this->assertEquals($model->product_id, $domain->product_id);
        $this->assertEquals($model->quantity, $domain->quantity);
        $this->assertEquals($model->value, $domain->value);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $model = $this->createModelInstance();
        $model->load(['budget', 'product']);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);

        // Test relationships are loaded
        if ($model->budget) {
            $this->assertNotNull($domain->budget);
            $this->assertEquals($model->budget->id, $domain->budget->id);
        }

        if ($model->product) {
            $this->assertNotNull($domain->product);
            $this->assertEquals($model->product->id, $domain->product->id);
        }
    }

    public function test_build_from_model_without_budget()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        // Budget should not be loaded when with_budget = false
    }

    public function test_build_from_model_without_product()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, false);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        // Product should not be loaded when with_product = false
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_model_with_different_quantities()
    {
        $model = BudgetProductModel::factory()->create(['quantity' => 500]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(BudgetProduct::class, $domain);
        $this->assertEquals(500, $domain->quantity);
    }

    public function test_build_from_model_with_different_values()
    {
        $model = BudgetProductModel::factory()->create(['value' => 9999.99]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(BudgetProduct::class, $domain);
        $this->assertEquals(9999.99, $domain->value);
    }

    public function test_build_from_model_with_zero_quantity()
    {
        $model = BudgetProductModel::factory()->create(['quantity' => 0]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(BudgetProduct::class, $domain);
        $this->assertEquals(0, $domain->quantity);
    }

    public function test_build_from_model_with_high_quantity()
    {
        $model = BudgetProductModel::factory()->create(['quantity' => 999999]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(BudgetProduct::class, $domain);
        $this->assertEquals(999999, $domain->quantity);
    }

    public function test_build_from_model_with_decimal_values()
    {
        $model = BudgetProductModel::factory()->create(['value' => 123.456789]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(BudgetProduct::class, $domain);
        $this->assertEquals(123.456789, $domain->value);
    }

    public function test_build_from_model_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            $model = BudgetProductModel::factory()->create($testCase);
            $factory = $this->createFactoryInstance();
            $domain = $factory->buildFromModel($model);

            $this->assertInstanceOf(BudgetProduct::class, $domain);
            $this->assertEquals($testCase['quantity'], $domain->quantity);
            $this->assertEquals($testCase['value'], $domain->value);
        }
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve circular dependencies
        return app()->make(BudgetProductFactory::class);
    }

    protected function getDomainClass(): string
    {
        return BudgetProduct::class;
    }

    protected function createModelInstance()
    {
        return BudgetProductModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\BudgetProduct\StoreRequest();
        $request->merge([
            'budget_id' => 1,
            'product_id' => 1,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test budget product',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\BudgetProduct\UpdateRequest();
        $request->merge([
            'budget_id' => 2,
            'product_id' => 2,
            'quantity' => 20,
            'value' => 200.75,
            'description' => 'Updated budget product',
        ]);
        return $request;
    }
}
