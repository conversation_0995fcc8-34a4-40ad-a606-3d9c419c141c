<?php

namespace Tests\Unit\Factories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

abstract class BaseFactoryTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test factory can build from model
     */
    abstract public function test_build_from_model();

    /**
     * Test factory returns null when model is null
     */
    public function test_build_from_null_model()
    {
        $factory = $this->createFactoryInstance();

        if (method_exists($factory, 'buildFromModel')) {
            $result = $factory->buildFromModel(null);
            $this->assertNull($result);
        } else {
            $this->markTestSkipped('buildFromModel method not implemented');
        }
    }

    /**
     * Test factory can build from store request if method exists
     */
    public function test_build_from_store_request()
    {
        $factory = $this->createFactoryInstance();

        if (method_exists($factory, 'buildFromStoreRequest')) {
            $request = $this->createStoreRequest();
            if ($request === null) {
                $this->markTestSkipped('Store request test requires complex setup');
                return;
            }
            $domain = $factory->buildFromStoreRequest($request);

            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertNull($domain->id);
        } else {
            $this->markTestSkipped('buildFromStoreRequest method not implemented');
        }
    }

    /**
     * Test factory can build from update request if method exists
     */
    public function test_build_from_update_request()
    {
        $factory = $this->createFactoryInstance();

        if (method_exists($factory, 'buildFromUpdateRequest')) {
            $request = $this->createUpdateRequest();
            if ($request === null) {
                $this->markTestSkipped('Update request test requires complex setup');
                return;
            }
            $domain = $factory->buildFromUpdateRequest($request);

            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertNull($domain->id);
        } else {
            $this->markTestSkipped('buildFromUpdateRequest method not implemented');
        }
    }

    /**
     * Create factory instance for testing
     * Must be implemented by child classes
     */
    abstract protected function createFactoryInstance();

    /**
     * Get the domain class that factory creates
     * Must be implemented by child classes
     */
    abstract protected function getDomainClass(): string;

    /**
     * Create a model instance for testing
     * Must be implemented by child classes
     */
    abstract protected function createModelInstance();

    /**
     * Create store request mock if needed
     */
    protected function createStoreRequest()
    {
        return null; // Override in child classes if needed
    }

    /**
     * Create update request mock if needed
     */
    protected function createUpdateRequest()
    {
        return null; // Override in child classes if needed
    }
}
