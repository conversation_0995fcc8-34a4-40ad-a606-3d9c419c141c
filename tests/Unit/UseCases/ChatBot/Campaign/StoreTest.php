<?php

namespace Tests\Unit\UseCases\ChatBot\Campaign;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\ChatBot\Campaign\Store;
use App\Repositories\CampaignRepository;
use App\Factories\ChatBot\CampaignFactory;
use App\Domains\ChatBot\Campaign;
use App\Models\Organization;
use App\Models\User;
use App\Models\Template;
use App\Models\PhoneNumber;
use App\Http\Requests\Campaign\StoreRequest;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private CampaignRepository $repository;
    private CampaignFactory $factory;
    private Organization $organization;
    private User $user;
    private Template $template;
    private PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->template = Template::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create(['organization_id' => $this->organization->id]);

        $this->repository = $this->createMock(CampaignRepository::class);
        $this->factory = $this->createMock(CampaignFactory::class);
        $this->useCase = new Store($this->repository, $this->factory);
    }

    public function test_can_store_campaign_successfully()
    {
        // Arrange
        $requestData = [
            'name' => 'Test Campaign',
            'description' => 'Test Description',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
            'is_direct_message' => false,
            'scheduled_at' => null,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        $expectedCampaign = new Campaign(
            id: null,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            template_id: $this->template->id,
            phone_number_id: $this->phoneNumber->id,
            name: 'Test Campaign',
            description: 'Test Description'
        );

        $storedCampaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            template_id: $this->template->id,
            phone_number_id: $this->phoneNumber->id,
            name: 'Test Campaign',
            description: 'Test Description'
        );

        // Mock expectations
        $this->factory->expects($this->once())
                     ->method('buildFromStoreRequest')
                     ->with($request)
                     ->willReturn($expectedCampaign);

        $this->repository->expects($this->once())
                        ->method('store')
                        ->with($expectedCampaign)
                        ->willReturn($storedCampaign);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertInstanceOf(Campaign::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('Test Campaign', $result->name);
    }

    public function test_validates_organization_access()
    {
        // Arrange
        $otherOrganization = Organization::factory()->create();
        $otherTemplate = Template::factory()->create(['organization_id' => $otherOrganization->id]);

        $requestData = [
            'name' => 'Test Campaign',
            'template_id' => $otherTemplate->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Template not found or access denied');

        $this->useCase->perform($request);
    }

    public function test_handles_repository_exceptions()
    {
        // Arrange
        $requestData = [
            'name' => 'Test Campaign',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        $campaign = new Campaign(
            null, // id
            $this->organization->id, // organization_id
            $this->user->id, // user_id
            null, // template_id
            null, // phone_number_id
            'Test Campaign' // name
        );

        $this->factory->method('buildFromStoreRequest')->willReturn($campaign);
        $this->repository->method('store')->willThrowException(new \Exception('Database error'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_sets_default_values_correctly()
    {
        // Arrange
        $requestData = [
            'name' => 'Test Campaign',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        $campaign = new Campaign(
            null, // id
            $this->organization->id, // organization_id
            $this->user->id, // user_id
            null, // template_id
            null, // phone_number_id
            'Test Campaign', // name
            null, // description
            false, // is_scheduled
            false, // is_sent
            false, // is_sending
            false, // is_direct_message
            0 // message_count
        );

        $this->factory->expects($this->once())
                     ->method('buildFromStoreRequest')
                     ->willReturn($campaign);

        $this->repository->expects($this->once())
                        ->method('store')
                        ->with($campaign)
                        ->willReturn($campaign);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertFalse($result->is_scheduled);
        $this->assertFalse($result->is_sent);
        $this->assertFalse($result->is_sending);
        $this->assertEquals(0, $result->message_count);
    }
}
