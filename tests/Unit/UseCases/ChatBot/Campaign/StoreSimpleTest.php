<?php

namespace Tests\Unit\UseCases\ChatBot\Campaign;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\ChatBot\Campaign\Store;
use App\Repositories\CampaignRepository;
use App\Factories\ChatBot\CampaignFactory;
use App\Models\Organization;
use App\Models\User;
use App\Models\Template;
use App\Models\PhoneNumber;
use App\Http\Requests\Campaign\StoreRequest;
use Illuminate\Support\Facades\DB;

class StoreSimpleTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private Template $template;
    private PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->template = Template::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_can_store_campaign_with_real_dependencies()
    {
        // Arrange
        $repository = app(CampaignRepository::class);
        $factory = app(CampaignFactory::class);
        $useCase = new Store($repository, $factory);

        $requestData = [
            'name' => 'Test Campaign',
            'description' => 'Test Description',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        // Act
        DB::beginTransaction();
        $result = $useCase->perform($request);
        DB::rollBack(); // Rollback to avoid side effects

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals('Test Campaign', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_validates_required_name_field()
    {
        // Arrange
        $requestData = [
            'description' => 'Test Description',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        // Act & Assert
        $this->expectException(\Illuminate\Validation\ValidationException::class);
        
        $request = new StoreRequest($requestData);
        $request->validateResolved();
    }

    public function test_sets_organization_id_from_authenticated_user()
    {
        // Arrange
        $repository = app(CampaignRepository::class);
        $factory = app(CampaignFactory::class);
        $useCase = new Store($repository, $factory);

        $requestData = [
            'name' => 'Test Campaign',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        // Mock the request helper to return our user
        $this->actingAs($this->user);

        // Act
        DB::beginTransaction();
        $result = $useCase->perform($request);
        DB::rollBack();

        // Assert
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_handles_database_transaction()
    {
        // Arrange
        $repository = app(CampaignRepository::class);
        $factory = app(CampaignFactory::class);
        $useCase = new Store($repository, $factory);

        $requestData = [
            'name' => 'Test Campaign',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);
        $this->actingAs($this->user);

        // Act - Should not throw exception
        DB::beginTransaction();
        $result = $useCase->perform($request);
        DB::rollBack();

        // Assert - If we get here, transaction was handled properly
        $this->assertNotNull($result);
    }

    public function test_factory_builds_domain_from_request()
    {
        // Arrange
        $factory = app(CampaignFactory::class);
        
        $requestData = [
            'name' => 'Factory Test Campaign',
            'description' => 'Factory Test Description',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);

        // Act
        $domain = $factory->buildFromStoreRequest($request);

        // Assert
        $this->assertEquals('Factory Test Campaign', $domain->name);
        $this->assertEquals('Factory Test Description', $domain->description);
        $this->assertEquals($this->template->id, $domain->template_id);
        $this->assertEquals($this->phoneNumber->id, $domain->phone_number_id);
    }

    public function test_repository_stores_domain()
    {
        // Arrange
        $repository = app(CampaignRepository::class);
        $factory = app(CampaignFactory::class);
        
        $requestData = [
            'name' => 'Repository Test Campaign',
            'template_id' => $this->template->id,
            'phone_number_id' => $this->phoneNumber->id,
        ];

        $request = new StoreRequest($requestData);
        $request->setUserResolver(fn() => $this->user);
        
        $domain = $factory->buildFromStoreRequest($request);
        $domain->organization_id = $this->organization->id;

        // Act
        $result = $repository->store($domain);

        // Assert
        $this->assertNotNull($result->id);
        $this->assertEquals('Repository Test Campaign', $result->name);
        
        // Verify in database
        $this->assertDatabaseHas('campaigns', [
            'id' => $result->id,
            'name' => 'Repository Test Campaign',
            'organization_id' => $this->organization->id
        ]);
    }
}
