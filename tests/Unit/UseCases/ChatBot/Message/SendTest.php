<?php

namespace Tests\Unit\UseCases\ChatBot\Message;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\ChatBot\Message\Send;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\MessageService;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\Campaign;
use App\Domains\Inventory\Client;
use App\Enums\MessageStatus;
use App\Models\Organization;

class SendTest extends TestCase
{
    use RefreshDatabase;

    private Send $useCase;
    private MessageRepository $repository;
    private MessageService $messageService;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->repository = $this->createMock(MessageRepository::class);
        $this->messageService = $this->createMock(MessageService::class);
        $this->useCase = new Send($this->repository, $this->messageService);
    }

    public function test_can_send_message_successfully()
    {
        // Arrange
        $client = new Client(
            id: 1,
            name: '<PERSON>',
            phone: '+5511999999999',
            organization_id: $this->organization->id
        );

        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Campaign'
        );

        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            campaign_id: 1,
            client_id: 1,
            message: 'Hello John Doe!',
            status: MessageStatus::is_draft,
            is_sent: false,
            is_fail: false,
            client: $client,
            campaign: $campaign
        );

        $whatsappResponse = [
            'messages' => [
                ['id' => 'wamid.123456789']
            ],
            'messaging_product' => 'whatsapp'
        ];

        // Mock expectations
        $this->messageService->expects($this->once())
                           ->method('send')
                           ->with($message)
                           ->willReturn($whatsappResponse);

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedMessage) {
                            return $savedMessage->is_sent === true &&
                                   $savedMessage->is_fail === false &&
                                   $savedMessage->status === MessageStatus::is_sent &&
                                   $savedMessage->sent_at !== null;
                        }), $this->organization->id);

        // Act
        $result = $this->useCase->execute($message);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['message_id']);
        $this->assertEquals('sent', $result['status']);
        $this->assertEquals('wamid.123456789', $result['whatsapp_message_id']);
    }

    public function test_handles_whatsapp_api_failure()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            status: MessageStatus::is_draft
        );

        $this->messageService->expects($this->once())
                           ->method('send')
                           ->with($message)
                           ->willThrowException(new \Exception('WhatsApp API Error'));

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedMessage) {
                            return $savedMessage->is_sent === false &&
                                   $savedMessage->is_fail === true &&
                                   $savedMessage->status === MessageStatus::is_failed &&
                                   $savedMessage->last_error_message === 'WhatsApp API Error';
                        }), $this->organization->id);

        // Act
        $result = $this->useCase->execute($message);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['message_id']);
        $this->assertEquals('failed', $result['status']);
        $this->assertEquals('WhatsApp API Error', $result['error_message']);
    }

    public function test_increments_delivery_attempts()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            delivery_attempts: 1,
            max_retries: 3
        );

        $this->messageService->method('send')
                           ->willThrowException(new \Exception('Network error'));

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedMessage) {
                            return $savedMessage->delivery_attempts === 2 &&
                                   $savedMessage->last_attempt_at !== null &&
                                   $savedMessage->next_retry_at !== null;
                        }), $this->organization->id);

        // Act
        $this->useCase->execute($message);
    }

    public function test_marks_as_permanently_failed_after_max_retries()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            delivery_attempts: 3,
            max_retries: 3
        );

        $this->messageService->method('send')
                           ->willThrowException(new \Exception('Final failure'));

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedMessage) {
                            return $savedMessage->delivery_attempts === 4 &&
                                   $savedMessage->is_fail === true &&
                                   $savedMessage->next_retry_at === null;
                        }), $this->organization->id);

        // Act
        $result = $this->useCase->execute($message);

        // Assert
        $this->assertEquals('permanently_failed', $result['status']);
    }

    public function test_validates_message_can_be_sent()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            is_sent: true,
            status: MessageStatus::is_sent
        );

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Message has already been sent');

        $this->useCase->execute($message);
    }

    public function test_handles_repository_save_failure()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            status: MessageStatus::is_draft
        );

        $this->messageService->method('send')->willReturn(['messages' => [['id' => 'test']]]);
        $this->repository->method('save')->willThrowException(new \Exception('Database error'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->execute($message);
    }

    public function test_calculates_retry_delay_correctly()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            delivery_attempts: 1
        );

        $this->messageService->method('send')
                           ->willThrowException(new \Exception('Temporary failure'));

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedMessage) {
                            // Should schedule retry in 15 minutes (2nd attempt)
                            $expectedRetryTime = now()->addMinutes(15);
                            $actualRetryTime = $savedMessage->next_retry_at;
                            
                            return abs($expectedRetryTime->diffInSeconds($actualRetryTime)) < 60;
                        }), $this->organization->id);

        // Act
        $this->useCase->execute($message);
    }
}
