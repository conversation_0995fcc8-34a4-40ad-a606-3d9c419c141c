<?php

namespace Tests\Unit\UseCases\ChatBot\Analytics;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\ChatBot\Analytics\GetCampaignAnalytics;
use App\Repositories\CampaignRepository;
use App\Repositories\CampaignAnalyticsRepository;
use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\CampaignAnalytics;
use App\Models\Organization;
use App\Enums\CampaignStatus;
use Carbon\Carbon;

class GetCampaignAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    private GetCampaignAnalytics $useCase;
    private CampaignRepository $campaignRepository;
    private CampaignAnalyticsRepository $analyticsRepository;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->campaignRepository = $this->createMock(CampaignRepository::class);
        $this->analyticsRepository = $this->createMock(CampaignAnalyticsRepository::class);
        $this->useCase = new GetCampaignAnalytics(
            $this->campaignRepository,
            $this->analyticsRepository
        );
    }

    public function test_can_get_campaign_analytics_successfully()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Campaign',
            status: CampaignStatus::COMPLETED
        );

        $analytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            total_messages: 100,
            sent_count: 95,
            delivered_count: 90,
            failed_count: 5,
            read_count: 75,
            response_count: 15,
            delivery_rate: 94.74,
            read_rate: 83.33,
            response_rate: 16.67,
            failure_rate: 5.26,
            performance_grade: 'A',
            calculated_at: Carbon::now()
        );

        // Mock expectations
        $this->campaignRepository->expects($this->once())
                                ->method('fetchById')
                                ->with(1)
                                ->willReturn($campaign);

        $this->analyticsRepository->expects($this->once())
                                 ->method('fetchByCampaign')
                                 ->with(1)
                                 ->willReturn($analytics);

        $this->analyticsRepository->expects($this->once())
                                 ->method('getOrganizationSummary')
                                 ->with($this->organization->id)
                                 ->willReturn([
                                     'total_campaigns' => 5,
                                     'avg_delivery_rate' => 85.0,
                                     'avg_read_rate' => 70.0,
                                     'avg_response_rate' => 12.0
                                 ]);

        // Act
        $result = $this->useCase->execute(1, $this->organization->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['campaign_id']);
        $this->assertEquals('Test Campaign', $result['campaign_name']);
        $this->assertEquals('completed', $result['campaign_status']);
        $this->assertArrayHasKey('analytics', $result);
        $this->assertArrayHasKey('insights', $result);
        $this->assertArrayHasKey('comparison', $result);
        $this->assertTrue($result['is_recent']);
    }

    public function test_validates_campaign_organization_access()
    {
        // Arrange
        $otherOrganization = Organization::factory()->create();
        $campaign = new Campaign(
            id: 1,
            organization_id: $otherOrganization->id,
            name: 'Other Campaign'
        );

        $this->campaignRepository->expects($this->once())
                                ->method('fetchById')
                                ->with(1)
                                ->willReturn($campaign);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign not found or access denied');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_recalculates_analytics_when_forced()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Campaign'
        );

        $oldAnalytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            calculated_at: Carbon::now()->subHours(2)
        );

        $newAnalytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            total_messages: 100,
            delivery_rate: 95.0,
            calculated_at: Carbon::now()
        );

        $this->campaignRepository->method('fetchById')->willReturn($campaign);
        $this->analyticsRepository->method('fetchByCampaign')->willReturn($oldAnalytics);

        $this->analyticsRepository->expects($this->once())
                                 ->method('calculateAndStore')
                                 ->with(1)
                                 ->willReturn($newAnalytics);

        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn(['total_campaigns' => 1]);

        // Act
        $result = $this->useCase->execute(1, $this->organization->id, true);

        // Assert
        $this->assertEquals(95.0, $result['analytics']['delivery_rate']);
    }

    public function test_recalculates_analytics_when_not_recent()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id
        );

        $oldAnalytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            calculated_at: Carbon::now()->subHours(25) // Old analytics
        );

        $newAnalytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            calculated_at: Carbon::now()
        );

        $this->campaignRepository->method('fetchById')->willReturn($campaign);
        $this->analyticsRepository->method('fetchByCampaign')->willReturn($oldAnalytics);
        $this->analyticsRepository->expects($this->once())
                                 ->method('calculateAndStore')
                                 ->willReturn($newAnalytics);
        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn(['total_campaigns' => 1]);

        // Act
        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_calculates_analytics_when_none_exist()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id
        );

        $newAnalytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            total_messages: 50,
            delivery_rate: 90.0
        );

        $this->campaignRepository->method('fetchById')->willReturn($campaign);
        $this->analyticsRepository->method('fetchByCampaign')->willReturn(null);
        $this->analyticsRepository->expects($this->once())
                                 ->method('calculateAndStore')
                                 ->willReturn($newAnalytics);
        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn(['total_campaigns' => 1]);

        // Act
        $result = $this->useCase->execute(1, $this->organization->id);

        // Assert
        $this->assertEquals(90.0, $result['analytics']['delivery_rate']);
    }

    public function test_get_dashboard_returns_organization_summary()
    {
        // Arrange
        $summary = [
            'total_campaigns' => 10,
            'total_messages' => 1000,
            'avg_delivery_rate' => 85.5,
            'avg_read_rate' => 70.2,
            'avg_response_rate' => 12.8
        ];

        $topCampaigns = [
            new CampaignAnalytics(id: 1, campaign_id: 1, delivery_rate: 95.0),
            new CampaignAnalytics(id: 2, campaign_id: 2, delivery_rate: 92.0)
        ];

        $this->analyticsRepository->expects($this->once())
                                 ->method('getOrganizationSummary')
                                 ->with($this->organization->id)
                                 ->willReturn($summary);

        $this->analyticsRepository->expects($this->once())
                                 ->method('getTopPerformingCampaigns')
                                 ->with($this->organization->id, 5)
                                 ->willReturn($topCampaigns);

        // Act
        $result = $this->useCase->getDashboard($this->organization->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('top_campaigns', $result);
        $this->assertArrayHasKey('trends', $result);
        $this->assertArrayHasKey('recommendations', $result);
        $this->assertEquals(10, $result['summary']['total_campaigns']);
        $this->assertCount(2, $result['top_campaigns']);
    }

    public function test_generates_recommendations_based_on_performance()
    {
        // Arrange
        $lowDeliveryRateSummary = [
            'total_campaigns' => 5,
            'avg_delivery_rate' => 75.0, // Below 80%
            'avg_read_rate' => 60.0,
            'avg_response_rate' => 8.0
        ];

        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn($lowDeliveryRateSummary);
        $this->analyticsRepository->method('getTopPerformingCampaigns')
                                 ->willReturn([]);

        // Act
        $result = $this->useCase->getDashboard($this->organization->id);

        // Assert
        $recommendations = $result['recommendations'];
        $this->assertNotEmpty($recommendations);
        
        $deliveryWarning = collect($recommendations)->firstWhere('type', 'warning');
        $this->assertNotNull($deliveryWarning);
        $this->assertStringContains('Improve Delivery Rate', $deliveryWarning['title']);
    }

    public function test_generates_positive_recommendations_for_good_performance()
    {
        // Arrange
        $goodPerformanceSummary = [
            'total_campaigns' => 5,
            'avg_delivery_rate' => 95.0,
            'avg_read_rate' => 80.0,
            'avg_response_rate' => 15.0 // Above 10%
        ];

        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn($goodPerformanceSummary);
        $this->analyticsRepository->method('getTopPerformingCampaigns')
                                 ->willReturn([]);

        // Act
        $result = $this->useCase->getDashboard($this->organization->id);

        // Assert
        $recommendations = $result['recommendations'];
        $successRecommendation = collect($recommendations)->firstWhere('type', 'success');
        $this->assertNotNull($successRecommendation);
        $this->assertStringContains('Great Engagement!', $successRecommendation['title']);
    }

    public function test_handles_comparison_data_for_single_campaign()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id
        );

        $analytics = new CampaignAnalytics(
            id: 1,
            campaign_id: 1,
            delivery_rate: 90.0
        );

        $this->campaignRepository->method('fetchById')->willReturn($campaign);
        $this->analyticsRepository->method('fetchByCampaign')->willReturn($analytics);
        $this->analyticsRepository->method('getOrganizationSummary')
                                 ->willReturn(['total_campaigns' => 1]); // Only one campaign

        // Act
        $result = $this->useCase->execute(1, $this->organization->id);

        // Assert
        $comparison = $result['comparison'];
        $this->assertFalse($comparison['has_comparison']);
        $this->assertEquals('Not enough campaigns for comparison', $comparison['message']);
    }
}
