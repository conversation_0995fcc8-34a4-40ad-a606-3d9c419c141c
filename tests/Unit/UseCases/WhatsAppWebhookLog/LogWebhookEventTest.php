<?php

namespace Tests\Unit\UseCases\WhatsAppWebhookLog;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\WhatsAppWebhookLog\LogWebhookEvent;
use App\Repositories\WhatsAppWebhookLogRepository;
use App\Factories\WhatsAppWebhookLogFactory;
use App\Domains\WhatsAppWebhookLog;

class LogWebhookEventTest extends TestCase
{
    use RefreshDatabase;

    private LogWebhookEvent $useCase;
    private WhatsAppWebhookLogRepository $repository;
    private WhatsAppWebhookLogFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new WhatsAppWebhookLogFactory();
        $this->repository = new WhatsAppWebhookLogRepository($this->factory);
        $this->useCase = new LogWebhookEvent($this->repository);
    }

    public function test_perform_creates_webhook_log()
    {
        $payload = ['test' => 'data'];

        $result = $this->useCase->perform(
            organizationId: null, // Use null to avoid foreign key constraint
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: $payload,
            processingStatus: 'pending',
            errorMessage: null
        );

        $this->assertInstanceOf(WhatsAppWebhookLog::class, $result);
        $this->assertNull($result->organization_id); // Should be null
        $this->assertEquals('569357716260641', $result->phone_number_id);
        $this->assertEquals('message', $result->event_type);
        $this->assertEquals($payload, $result->webhook_payload);
        $this->assertEquals('pending', $result->processing_status);
        $this->assertNull($result->error_message);
        $this->assertNotNull($result->id);
    }

    public function test_perform_with_default_status()
    {
        $payload = ['test' => 'data'];

        $result = $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'status',
            webhookPayload: $payload
        );

        $this->assertEquals('pending', $result->processing_status);
        $this->assertNull($result->error_message);
    }

    public function test_perform_validates_phone_number_id()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Phone number ID is required');

        $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '',
            eventType: 'message',
            webhookPayload: ['test' => 'data']
        );
    }

    public function test_perform_validates_event_type()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid event type');

        $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'invalid',
            webhookPayload: ['test' => 'data']
        );
    }

    public function test_perform_validates_webhook_payload()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Webhook payload cannot be empty');

        $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: []
        );
    }

    public function test_perform_validates_processing_status()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid processing status');

        $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: ['test' => 'data'],
            processingStatus: 'invalid'
        );
    }

    public function test_log_message_event()
    {
        $payload = ['message' => 'test'];

        $result = $this->useCase->logMessageEvent(
            organizationId: null,
            phoneNumberId: '569357716260641',
            webhookPayload: $payload,
            processingStatus: 'success'
        );

        $this->assertEquals('message', $result->event_type);
        $this->assertEquals('success', $result->processing_status);
        $this->assertNull($result->organization_id);
    }

    public function test_log_status_event()
    {
        $payload = ['status' => 'delivered'];

        $result = $this->useCase->logStatusEvent(
            organizationId: null,
            phoneNumberId: '569357716260641',
            webhookPayload: $payload,
            processingStatus: 'success'
        );

        $this->assertEquals('status', $result->event_type);
        $this->assertEquals('success', $result->processing_status);
        $this->assertNull($result->organization_id);
    }

    public function test_log_other_event()
    {
        $payload = ['other' => 'data'];

        $result = $this->useCase->logOtherEvent(
            organizationId: null,
            phoneNumberId: '569357716260641',
            webhookPayload: $payload,
            processingStatus: 'failed',
            errorMessage: 'Unknown event type'
        );

        $this->assertEquals('other', $result->event_type);
        $this->assertEquals('failed', $result->processing_status);
        $this->assertEquals('Unknown event type', $result->error_message);
        $this->assertNull($result->organization_id);
    }

    public function test_log_success()
    {
        $payload = ['test' => 'data'];

        $result = $this->useCase->logSuccess(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: $payload
        );

        $this->assertEquals('success', $result->processing_status);
        $this->assertNull($result->error_message);
    }

    public function test_log_failure()
    {
        $payload = ['test' => 'data'];
        $errorMessage = 'Processing failed';

        $result = $this->useCase->logFailure(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: $payload,
            errorMessage: $errorMessage
        );

        $this->assertEquals('failed', $result->processing_status);
        $this->assertEquals($errorMessage, $result->error_message);
    }

    public function test_mark_as_successful()
    {
        // First create a log
        $log = $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: ['test' => 'data'],
            processingStatus: 'pending'
        );

        // Then mark it as successful
        $result = $this->useCase->markAsSuccessful($log->id);

        $this->assertEquals('success', $result->processing_status);
        $this->assertNotNull($result->processed_at);
        $this->assertNull($result->error_message);
    }

    public function test_mark_as_failed()
    {
        // First create a log
        $log = $this->useCase->perform(
            organizationId: null,
            phoneNumberId: '569357716260641',
            eventType: 'message',
            webhookPayload: ['test' => 'data'],
            processingStatus: 'pending'
        );

        $errorMessage = 'Processing failed';

        // Then mark it as failed
        $result = $this->useCase->markAsFailed($log->id, $errorMessage);

        $this->assertEquals('failed', $result->processing_status);
        $this->assertNotNull($result->processed_at);
        $this->assertEquals($errorMessage, $result->error_message);
    }

    public function test_app_make_use_case()
    {
        $useCase = app()->make(LogWebhookEvent::class);
        $this->assertInstanceOf(LogWebhookEvent::class, $useCase);
    }
}
