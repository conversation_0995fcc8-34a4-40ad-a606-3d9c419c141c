<?php

namespace Tests\Unit\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\StockExit\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private StockExitRepository $mockRepository;
    private StockExitFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockExitRepository::class);
        $this->mockFactory = Mockery::mock(StockExitFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_stock_exit_successfully()
    {
        $exitId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingExit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Old description'
        );

        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: null,
            user_id: null,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated description'
        );

        $updatedExit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($existingExit);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($exit) use ($exitId) {
                return $exit instanceof StockExit &&
                       $exit->id === $exitId &&
                       $exit->organization_id === $this->organization->id &&
                       $exit->quantity === 100;
            }), $this->organization->id)
            ->andReturn($updatedExit);

        $result = $this->useCase->perform($exitId, $request);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($exitId, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Updated description', $result->description);
    }

    public function test_perform_throws_exception_when_exit_belongs_to_different_organization()
    {
        $exitId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingExit = new StockExit(
            id: $exitId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Other exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($existingExit);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock exit don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($exitId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $exitId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($exitId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $exitId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingExit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Existing exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($existingExit);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($exitId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $exitId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingExit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Existing exit'
        );

        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: null,
            user_id: null,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($existingExit);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($exitId, $request);
    }

    public function test_perform_preserves_original_id_organization_shop_and_user()
    {
        $exitId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingExit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Preserve exit'
        );

        $domainFromRequest = new StockExit(
            id: 999, // Different ID in request
            organization_id: 888, // Different organization in request
            shop_id: 777, // Different shop in request
            user_id: 666, // Different user in request
            brand_id: 2,
            product_id: 2,
            batch_id: 2,
            client_id: 2,
            project_id: 2,
            quantity: 200,
            value: 3000.75,
            description: 'Preserve updated exit'
        );

        $updatedExit = new StockExit(
            id: $exitId, // Should preserve original
            organization_id: $this->organization->id, // Should preserve original
            shop_id: 1, // Should preserve original
            user_id: $this->user->id, // Should preserve original
            brand_id: 2,
            product_id: 2,
            batch_id: 2,
            client_id: 2,
            project_id: 2,
            quantity: 200,
            value: 3000.75,
            description: 'Preserve updated exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($existingExit);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($exit) use ($exitId) {
                return $exit->id === $exitId && 
                       $exit->organization_id === $this->organization->id &&
                       $exit->shop_id === 1 &&
                       $exit->user_id === $this->user->id;
            }), $this->organization->id)
            ->andReturn($updatedExit);

        $result = $this->useCase->perform($exitId, $request);

        $this->assertEquals($exitId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\StockExit\UpdateRequest {
            public $brand_id = 1;
            public $product_id = 1;
            public $batch_id = 1;
            public $client_id = 1;
            public $project_id = 1;
            public $quantity = 100;
            public $value = 1500.50;
            public $description = 'Updated description';
        };
    }
}
