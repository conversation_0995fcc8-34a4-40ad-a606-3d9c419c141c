<?php

namespace Tests\Unit\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\Stock\DecreaseStock;
use App\UseCases\Inventory\StockExit\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private StockExitRepository $mockRepository;
    private StockExitFactory $mockFactory;
    private DecreaseStock $mockDecreaseStock;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockExitRepository::class);
        $this->mockFactory = Mockery::mock(StockExitFactory::class);
        $this->mockDecreaseStock = Mockery::mock(DecreaseStock::class);

        // Mock the app container to return our mock
        $this->app->instance(DecreaseStock::class, $this->mockDecreaseStock);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_stock_exit_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Store exit'
        );

        $storedExit = new StockExit(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Store exit'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($exit) {
                return $exit instanceof StockExit &&
                       $exit->organization_id === $this->organization->id &&
                       $exit->quantity === 100;
            }))
            ->andReturn($storedExit);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedExit);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: null,
            description: 'Organization test'
        );

        $storedExit = new StockExit(
            id: 2,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Organization test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($exit) {
                return $exit->organization_id === $this->organization->id;
            }))
            ->andReturn($storedExit);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedExit);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_calls_calculate_value()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = Mockery::mock(StockExit::class);
        $domainFromRequest->shouldReceive('calculateValue')->once();
        $domainFromRequest->organization_id = null;

        $storedExit = new StockExit(
            id: 3,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 75,
            value: 1125.75,
            description: 'Calculate value test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedExit);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedExit);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockExit::class, $result);
    }

    public function test_perform_calls_decrease_stock()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 25,
            value: null,
            description: 'Decrease stock test'
        );

        $storedExit = new StockExit(
            id: 4,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 25,
            value: 375.25,
            description: 'Decrease stock test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedExit);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedExit);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(25, $result->quantity);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_decrease_stock_exception()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new StockExit(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Decrease stock error test'
        );

        $storedExit = new StockExit(
            id: 5,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Decrease stock error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedExit);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedExit)
            ->andThrow(new Exception('Decrease stock error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Decrease stock error');

        $this->useCase->perform($request);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\StockExit\StoreRequest {
            public $shop_id = 1;
            public $brand_id = 1;
            public $product_id = 1;
            public $batch_id = 1;
            public $client_id = 1;
            public $project_id = 1;
            public $quantity = 100;
            public $value = 1500.50;
            public $description = 'Store exit';

            public function user()
            {
                return (object) ['id' => 1];
            }
        };
    }
}
