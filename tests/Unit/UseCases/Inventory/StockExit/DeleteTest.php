<?php

namespace Tests\Unit\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\Stock\IncreaseStock;
use App\UseCases\Inventory\StockExit\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private StockExitRepository $mockRepository;
    private IncreaseStock $mockIncreaseStock;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockExitRepository::class);
        $this->mockIncreaseStock = Mockery::mock(IncreaseStock::class);

        // Mock the app container to return our mock
        $this->app->instance(IncreaseStock::class, $this->mockIncreaseStock);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_stock_exit_successfully()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Delete exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($exit);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($exit)
            ->andReturn(true);

        $result = $this->useCase->perform($exitId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_exit_belongs_to_different_organization()
    {
        $exitId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Other exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldNotReceive('perform');

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock exit don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($exitId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $exitId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($exitId);
    }

    public function test_perform_handles_increase_stock_exception()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Increase stock error exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($exit)
            ->andThrow(new Exception('Increase stock error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Increase stock error');

        $this->useCase->perform($exitId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Delete error exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($exit);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($exit)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($exitId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Fail exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($exit);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($exit)
            ->andReturn(false);

        $result = $this->useCase->perform($exitId);

        $this->assertFalse($result);
    }

    public function test_perform_calls_increase_stock_before_delete()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Increase stock test'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($exit);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($exit)
            ->andReturn(true);

        $result = $this->useCase->perform($exitId);

        $this->assertTrue($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $exitId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id, // Exit belongs to original org
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Auth test exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        // Should throw exception because exit belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock exit don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($exitId);
    }
}
