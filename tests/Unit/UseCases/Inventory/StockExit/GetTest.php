<?php

namespace Tests\Unit\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\StockExit\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private StockExitRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockExitRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_stock_exit_successfully()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Get exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($exitId, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Get exit', $result->description);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $exitId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($exitId);
    }

    public function test_perform_with_minimal_exit()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 10,
            value: 150.50,
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertNull($result->batch_id);
        $this->assertNull($result->client_id);
        $this->assertNull($result->project_id);
        $this->assertNull($result->description);
    }

    public function test_perform_with_complete_exit()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 200,
            value: 3000.75,
            description: 'Complete exit with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($exitId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->brand_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(1, $result->batch_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(1, $result->project_id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals(3000.75, $result->value);
        $this->assertEquals('Complete exit with all fields', $result->description);
    }

    public function test_perform_with_special_characters_in_description()
    {
        $exitId = 1;
        $specialDescription = 'Exit with special chars: @#$%^&*()';
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: $specialDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($specialDescription, $result->description);
    }

    public function test_perform_with_unicode_characters_in_description()
    {
        $exitId = 1;
        $unicodeDescription = 'Salida con caracteres especiales: ñáéíóú 中文';
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 75,
            value: 1125.75,
            description: $unicodeDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($unicodeDescription, $result->description);
    }

    public function test_perform_with_zero_quantity()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High quantity exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_with_decimal_values()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value exit'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_returns_exit_with_all_properties()
    {
        $exitId = 1;
        
        $exit = new StockExit(
            id: $exitId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Properties exit',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($exitId)
            ->andReturn($exit);

        $result = $this->useCase->perform($exitId);

        $this->assertInstanceOf(StockExit::class, $result);
        $this->assertEquals($exitId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->brand_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(1, $result->batch_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(1, $result->project_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Properties exit', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }
}
