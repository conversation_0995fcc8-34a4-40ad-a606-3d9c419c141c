<?php

namespace Tests\Unit\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Factories\Inventory\ProjectProductFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectProductRepository;
use App\UseCases\Inventory\ProjectProduct\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ProjectProductRepository $mockRepository;
    private ProjectProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectProductRepository::class);
        $this->mockFactory = Mockery::mock(ProjectProductFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_project_product_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 1,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test project product', $result->description);
    }

    public function test_perform_with_different_quantities()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'High quantity project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 2,
            project_id: 1,
            product_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'High quantity project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(50, $result->quantity);
        $this->assertEquals(750.25, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 3,
            project_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'Very high quantity project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 4,
            project_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'Very high quantity project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_decimal_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 5,
            project_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $request = $this->createMockStoreRequest();
        $longDescription = str_repeat('Long description text. ', 20);
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $storedProjectProduct = new ProjectProduct(
            id: 6,
            project_id: 1,
            product_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\ProjectProduct\StoreRequest {
            public $project_id = 1;
            public $product_id = 1;
            public $quantity = 10;
            public $value = 150.50;
            public $description = 'Test project product';
        };
    }
}
