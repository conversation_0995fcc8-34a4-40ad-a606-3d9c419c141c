<?php

namespace Tests\Unit\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectProductRepository;
use App\UseCases\Inventory\ProjectProduct\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ProjectProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectProductRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_project_product_successfully()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $projectProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($projectProductId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($projectProductId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(false);

        $result = $this->useCase->perform($projectProductId);

        $this->assertFalse($result);
    }

    public function test_perform_with_custom_project_product()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: null, // Custom project product
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_different_project_product_types()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50, 'product_id' => 1],
            ['quantity' => 100, 'value' => 1050.00, 'product_id' => 2],
            ['quantity' => 999, 'value' => 9999.99, 'product_id' => null], // Custom
        ];

        foreach ($testCases as $index => $testCase) {
            $projectProductId = $index + 1;
            
            $projectProduct = new ProjectProduct(
                id: $projectProductId,
                project_id: 1,
                product_id: $testCase['product_id'],
                quantity: $testCase['quantity'],
                value: $testCase['value'],
                description: 'Test project product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($projectProductId)
                ->andReturn($projectProduct);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($projectProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($projectProductId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_zero_quantity_project_product()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_project_product()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High value project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_long_description_project_product()
    {
        $projectProductId = 1;
        $longDescription = str_repeat('Long description text. ', 20);
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_decimal_values()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($projectProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($projectProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_different_project_and_product_ids()
    {
        $testCases = [
            ['project_id' => 1, 'product_id' => 1],
            ['project_id' => 5, 'product_id' => 3],
            ['project_id' => 10, 'product_id' => null], // Custom
        ];

        foreach ($testCases as $index => $testCase) {
            $projectProductId = $index + 1;
            
            $projectProduct = new ProjectProduct(
                id: $projectProductId,
                project_id: $testCase['project_id'],
                product_id: $testCase['product_id'],
                quantity: 10,
                value: 150.50,
                description: 'Test project product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($projectProductId)
                ->andReturn($projectProduct);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($projectProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($projectProductId);

            $this->assertTrue($result);
        }
    }
}
