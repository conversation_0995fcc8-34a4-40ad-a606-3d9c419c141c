<?php

namespace Tests\Unit\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductHistoryRepository;
use App\UseCases\Inventory\ProductHistory\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private ProductHistoryRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductHistoryRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_product_histories()
    {
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            ),
            new ProductHistory(
                id: 2,
                user_id: $this->user->id,
                product_id: 2,
                field: 'name',
                alias: 'Nome',
                old: 'Old Product',
                new: 'New Product'
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $productHistory) {
            $this->assertInstanceOf(ProductHistory::class, $productHistory);
        }
    }

    public function test_perform_with_empty_result()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
        $this->assertEmpty($result['data']);
    }

    public function test_perform_handles_repository_exception()
    {
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform();
    }

    public function test_perform_with_pagination()
    {
        $productHistories = [];
        for ($i = 1; $i <= 5; $i++) {
            $productHistories[] = new ProductHistory(
                id: $i,
                user_id: $this->user->id,
                product_id: $i,
                field: 'price',
                alias: 'Preço',
                old: ($i * 10) . '.50',
                new: ($i * 15) . '.75'
            );
        }

        $expectedResult = [
            'data' => $productHistories,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($differentOrg->id) // Should use the different organization
            ->andReturn($expectedResult);

        $this->useCase->perform();
    }

    public function test_perform_with_various_product_history_types()
    {
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'name',
                alias: 'Nome',
                old: 'Old Name',
                new: 'New Name'
            ),
            new ProductHistory(
                id: 2,
                user_id: $this->user->id,
                product_id: 2,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            ),
            new ProductHistory(
                id: 3,
                user_id: $this->user->id,
                product_id: 3,
                field: 'description',
                alias: 'Descrição',
                old: 'Old Description',
                new: 'New Description'
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify different product history types
        $this->assertEquals('name', $result['data'][0]->field);
        $this->assertEquals('Nome', $result['data'][0]->alias);
        
        $this->assertEquals('price', $result['data'][1]->field);
        $this->assertEquals('Preço', $result['data'][1]->alias);
        
        $this->assertEquals('description', $result['data'][2]->field);
        $this->assertEquals('Descrição', $result['data'][2]->alias);
    }

    public function test_perform_with_price_changes()
    {
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: '0.00',
                new: '10.50'
            ),
            new ProductHistory(
                id: 2,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            ),
            new ProductHistory(
                id: 3,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: '15.75',
                new: '20.00'
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify price changes
        $this->assertEquals('0.00', $result['data'][0]->old);
        $this->assertEquals('10.50', $result['data'][0]->new);
        
        $this->assertEquals('10.50', $result['data'][1]->old);
        $this->assertEquals('15.75', $result['data'][1]->new);
        
        $this->assertEquals('15.75', $result['data'][2]->old);
        $this->assertEquals('20.00', $result['data'][2]->new);
    }

    public function test_perform_with_multiple_products()
    {
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            ),
            new ProductHistory(
                id: 2,
                user_id: $this->user->id,
                product_id: 2,
                field: 'price',
                alias: 'Preço',
                old: '20.00',
                new: '25.50'
            ),
            new ProductHistory(
                id: 3,
                user_id: $this->user->id,
                product_id: 3,
                field: 'name',
                alias: 'Nome',
                old: 'Old Product 3',
                new: 'New Product 3'
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify product histories from different products
        $this->assertEquals(1, $result['data'][0]->product_id);
        $this->assertEquals(2, $result['data'][1]->product_id);
        $this->assertEquals(3, $result['data'][2]->product_id);
    }

    public function test_perform_with_long_values()
    {
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'description',
                alias: 'Descrição',
                old: $longOldValue,
                new: $longNewValue
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($longOldValue, $result['data'][0]->old);
        $this->assertEquals($longNewValue, $result['data'][0]->new);
    }

    public function test_perform_with_null_values()
    {
        $productHistories = [
            new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'description',
                alias: 'Descrição',
                old: null,
                new: null
            )
        ];

        $expectedResult = [
            'data' => $productHistories,
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->old);
        $this->assertNull($result['data'][0]->new);
    }
}
