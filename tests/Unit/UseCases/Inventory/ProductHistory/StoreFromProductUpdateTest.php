<?php

namespace Tests\Unit\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\ProductHistory;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductHistoryRepository;
use App\UseCases\Inventory\ProductHistory\StoreFromProductUpdate;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreFromProductUpdateTest extends TestCase
{
    use RefreshDatabase;

    private StoreFromProductUpdate $useCase;
    private ProductHistoryRepository $mockRepository;
    private ProductHistoryFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductHistoryRepository::class);
        $this->mockFactory = Mockery::mock(ProductHistoryFactory::class);

        $this->useCase = new StoreFromProductUpdate($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_product_history_from_product_update()
    {
        $oldProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 10.50,
            is_active: true
        );

        $newProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 15.75,
            is_active: true
        );

        $domainFromProducts = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockFactory
            ->shouldReceive('buildFromProducts')
            ->once()
            ->with($newProduct, $oldProduct)
            ->andReturn($domainFromProducts);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromProducts)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($newProduct, $oldProduct);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('price', $result->field);
        $this->assertEquals('Preço', $result->alias);
        $this->assertEquals('10.50', $result->old);
        $this->assertEquals('15.75', $result->new);
    }

    public function test_perform_with_different_price_changes()
    {
        $priceChanges = [
            ['old_price' => 0.00, 'new_price' => 10.50],
            ['old_price' => 10.50, 'new_price' => 15.75],
            ['old_price' => 15.75, 'new_price' => 20.00],
            ['old_price' => 20.00, 'new_price' => 0.00],
            ['old_price' => 100.99, 'new_price' => 999.99],
        ];

        foreach ($priceChanges as $priceChange) {
            $oldProduct = new Product(
                id: 1,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: 'Test Product',
                description: 'Test product description',
                price: $priceChange['old_price'],
                is_active: true
            );

            $newProduct = new Product(
                id: 1,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: 'Test Product',
                description: 'Test product description',
                price: $priceChange['new_price'],
                is_active: true
            );

            $domainFromProducts = new ProductHistory(
                id: null,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: (string) $priceChange['old_price'],
                new: (string) $priceChange['new_price']
            );

            $storedProductHistory = new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: (string) $priceChange['old_price'],
                new: (string) $priceChange['new_price']
            );

            $this->mockFactory
                ->shouldReceive('buildFromProducts')
                ->once()
                ->with($newProduct, $oldProduct)
                ->andReturn($domainFromProducts);

            $this->mockRepository
                ->shouldReceive('store')
                ->once()
                ->with($domainFromProducts)
                ->andReturn($storedProductHistory);

            $result = $this->useCase->perform($newProduct, $oldProduct);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals((string) $priceChange['old_price'], $result->old);
            $this->assertEquals((string) $priceChange['new_price'], $result->new);
        }
    }

    public function test_perform_with_same_prices()
    {
        $oldProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 15.75,
            is_active: true
        );

        $newProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 15.75,
            is_active: true
        );

        $domainFromProducts = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '15.75',
            new: '15.75'
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '15.75',
            new: '15.75'
        );

        $this->mockFactory
            ->shouldReceive('buildFromProducts')
            ->once()
            ->with($newProduct, $oldProduct)
            ->andReturn($domainFromProducts);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromProducts)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($newProduct, $oldProduct);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals('15.75', $result->old);
        $this->assertEquals('15.75', $result->new);
    }

    public function test_perform_handles_factory_exception()
    {
        $oldProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 10.50,
            is_active: true
        );

        $newProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 15.75,
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromProducts')
            ->once()
            ->with($newProduct, $oldProduct)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($newProduct, $oldProduct);
    }

    public function test_perform_handles_repository_exception()
    {
        $oldProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 10.50,
            is_active: true
        );

        $newProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 15.75,
            is_active: true
        );

        $domainFromProducts = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockFactory
            ->shouldReceive('buildFromProducts')
            ->once()
            ->with($newProduct, $oldProduct)
            ->andReturn($domainFromProducts);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($newProduct, $oldProduct);
    }

    public function test_perform_with_different_product_ids()
    {
        $productIds = [1, 5, 10, 999];

        foreach ($productIds as $productId) {
            $oldProduct = new Product(
                id: $productId,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: 'Test Product',
                description: 'Test product description',
                price: 10.50,
                is_active: true
            );

            $newProduct = new Product(
                id: $productId,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: 'Test Product',
                description: 'Test product description',
                price: 15.75,
                is_active: true
            );

            $domainFromProducts = new ProductHistory(
                id: null,
                user_id: $this->user->id,
                product_id: $productId,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            );

            $storedProductHistory = new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: $productId,
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            );

            $this->mockFactory
                ->shouldReceive('buildFromProducts')
                ->once()
                ->with($newProduct, $oldProduct)
                ->andReturn($domainFromProducts);

            $this->mockRepository
                ->shouldReceive('store')
                ->once()
                ->with($domainFromProducts)
                ->andReturn($storedProductHistory);

            $result = $this->useCase->perform($newProduct, $oldProduct);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($productId, $result->product_id);
        }
    }

    public function test_perform_with_decimal_prices()
    {
        $oldProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 123.456789,
            is_active: true
        );

        $newProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 987.654321,
            is_active: true
        );

        $domainFromProducts = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '123.456789',
            new: '987.654321'
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '123.456789',
            new: '987.654321'
        );

        $this->mockFactory
            ->shouldReceive('buildFromProducts')
            ->once()
            ->with($newProduct, $oldProduct)
            ->andReturn($domainFromProducts);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromProducts)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($newProduct, $oldProduct);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals('123.456789', $result->old);
        $this->assertEquals('987.654321', $result->new);
    }
}
