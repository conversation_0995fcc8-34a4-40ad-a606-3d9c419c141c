<?php

namespace Tests\Unit\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductHistoryRepository;
use App\UseCases\Inventory\ProductHistory\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ProductHistoryRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductHistoryRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_product_history_successfully()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andReturn(true);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertTrue($result);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $productHistoryId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($productHistoryId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($productHistoryId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andReturn(false);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertFalse($result);
    }

    public function test_perform_with_different_product_history_types()
    {
        $testCases = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
        ];

        foreach ($testCases as $index => $testCase) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $this->user->id,
                product_id: 1,
                field: $testCase['field'],
                alias: $testCase['alias'],
                old: $testCase['old'],
                new: $testCase['new']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($productHistory)
                ->andReturn(true);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_price_change_histories()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $index => $priceChange) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: $priceChange['old'],
                new: $priceChange['new']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($productHistory)
                ->andReturn(true);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_long_value_histories()
    {
        $productHistoryId = 1;
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: $longOldValue,
            new: $longNewValue
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andReturn(true);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertTrue($result);
    }

    public function test_perform_with_null_value_histories()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: null,
            new: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andReturn(true);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertTrue($result);
    }

    public function test_perform_with_different_user_and_product_ids()
    {
        $testCases = [
            ['user_id' => 1, 'product_id' => 1],
            ['user_id' => 5, 'product_id' => 3],
            ['user_id' => 10, 'product_id' => 7],
            ['user_id' => 999, 'product_id' => 888],
        ];

        foreach ($testCases as $index => $testCase) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $testCase['user_id'],
                product_id: $testCase['product_id'],
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($productHistory)
                ->andReturn(true);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_decimal_price_histories()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '123.456789',
            new: '987.654321'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($productHistory)
            ->andReturn(true);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertTrue($result);
    }
}
