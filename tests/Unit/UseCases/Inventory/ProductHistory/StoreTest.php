<?php

namespace Tests\Unit\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductHistoryRepository;
use App\UseCases\Inventory\ProductHistory\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ProductHistoryRepository $mockRepository;
    private ProductHistoryFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductHistoryRepository::class);
        $this->mockFactory = Mockery::mock(ProductHistoryFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_product_history_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('price', $result->field);
        $this->assertEquals('Preço', $result->alias);
        $this->assertEquals('10.50', $result->old);
        $this->assertEquals('15.75', $result->new);
    }

    public function test_perform_with_different_fields()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
        ];

        foreach ($fields as $fieldData) {
            $request = $this->createMockStoreRequest();
            
            $domainFromRequest = new ProductHistory(
                id: null,
                user_id: $this->user->id,
                product_id: 1,
                field: $fieldData['field'],
                alias: $fieldData['alias'],
                old: $fieldData['old'],
                new: $fieldData['new']
            );

            $storedProductHistory = new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: $fieldData['field'],
                alias: $fieldData['alias'],
                old: $fieldData['old'],
                new: $fieldData['new']
            );

            $this->mockFactory
                ->shouldReceive('buildFromStoreRequest')
                ->once()
                ->with($request)
                ->andReturn($domainFromRequest);

            $this->mockRepository
                ->shouldReceive('store')
                ->once()
                ->with($domainFromRequest)
                ->andReturn($storedProductHistory);

            $result = $this->useCase->perform($request);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($fieldData['field'], $result->field);
            $this->assertEquals($fieldData['alias'], $result->alias);
            $this->assertEquals($fieldData['old'], $result->old);
            $this->assertEquals($fieldData['new'], $result->new);
        }
    }

    public function test_perform_with_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $priceChange) {
            $request = $this->createMockStoreRequest();
            
            $domainFromRequest = new ProductHistory(
                id: null,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: $priceChange['old'],
                new: $priceChange['new']
            );

            $storedProductHistory = new ProductHistory(
                id: 1,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: $priceChange['old'],
                new: $priceChange['new']
            );

            $this->mockFactory
                ->shouldReceive('buildFromStoreRequest')
                ->once()
                ->with($request)
                ->andReturn($domainFromRequest);

            $this->mockRepository
                ->shouldReceive('store')
                ->once()
                ->with($domainFromRequest)
                ->andReturn($storedProductHistory);

            $result = $this->useCase->perform($request);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($priceChange['old'], $result->old);
            $this->assertEquals($priceChange['new'], $result->new);
        }
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_long_values()
    {
        $request = $this->createMockStoreRequest();
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        $domainFromRequest = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: $longOldValue,
            new: $longNewValue
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: $longOldValue,
            new: $longNewValue
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals($longOldValue, $result->old);
        $this->assertEquals($longNewValue, $result->new);
    }

    public function test_perform_with_null_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new ProductHistory(
            id: null,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: null,
            new: null
        );

        $storedProductHistory = new ProductHistory(
            id: 1,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: null,
            new: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProductHistory);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertNull($result->old);
        $this->assertNull($result->new);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\ProductHistory\StoreRequest {
            public $user_id = 1;
            public $product_id = 1;
            public $field = 'price';
            public $alias = 'Preço';
            public $old = '10.50';
            public $new = '15.75';
        };
    }
}
