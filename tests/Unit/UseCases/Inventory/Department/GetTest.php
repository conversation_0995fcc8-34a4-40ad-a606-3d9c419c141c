<?php

namespace Tests\Unit\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\Department\Get;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private DepartmentRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentRepository::class);
        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_department_when_belongs_to_organization()
    {
        $departmentId = 1;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentId);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertEquals($departmentId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Test Department', $result->name);
        $this->assertTrue($result->is_active);
    }

    public function test_perform_throws_exception_when_department_belongs_to_different_organization()
    {
        $departmentId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $department = new Department(
            id: $departmentId,
            organization_id: $otherOrganization->id,
            name: 'Other Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentId);
    }

    public function test_perform_handles_repository_exception()
    {
        $departmentId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Department not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department not found');

        $this->useCase->perform($departmentId);
    }

    public function test_perform_with_inactive_department()
    {
        $departmentId = 2;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Inactive Department',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentId);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertEquals($departmentId, $result->id);
        $this->assertFalse($result->is_active);
    }

    public function test_perform_validates_organization_ownership()
    {
        $departmentId = 3;
        
        // Create a department that belongs to the user's organization
        $validDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Valid Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($validDepartment);

        $result = $this->useCase->perform($departmentId);

        // Should not throw exception and return the department
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_null_organization_id_throws_exception()
    {
        $departmentId = 4;
        $department = new Department(
            id: $departmentId,
            organization_id: null,
            name: 'No Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $departmentId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $department = new Department(
            id: $departmentId,
            organization_id: $newOrganization->id,
            name: 'New Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentId);

        $this->assertEquals($newOrganization->id, $result->organization_id);
    }

    public function test_perform_with_zero_id()
    {
        $departmentId = 0;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Invalid department ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department ID');

        $this->useCase->perform($departmentId);
    }

    public function test_perform_with_negative_id()
    {
        $departmentId = -1;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Invalid department ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department ID');

        $this->useCase->perform($departmentId);
    }
}
