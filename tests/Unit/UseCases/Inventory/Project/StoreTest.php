<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Http\Requests\Project\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ProjectRepository $mockRepository;
    private ProjectFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);
        $this->mockFactory = Mockery::mock(ProjectFactory::class);

        $this->useCase = new Store(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_project_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) {
                return $project instanceof Project && 
                       $project->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('Test Project', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) {
                return $project->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProject);

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_project_data()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: null,
            budget_id: null,
            name: 'Minimal Project',
            description: null,
            value: null,
            cost: null
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Minimal Project',
            description: null,
            value: null,
            cost: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals('Minimal Project', $result->name);
        $this->assertNull($result->client_id);
        $this->assertNull($result->budget_id);
        $this->assertNull($result->value);
        $this->assertNull($result->cost);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_zero_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Project with zero values',
            value: 0.0,
            cost: 0.0
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Project with zero values',
            value: 0.0,
            cost: 0.0
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(0.0, $result->value);
        $this->assertEquals(0.0, $result->cost);
    }

    public function test_perform_with_high_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'High Value Project',
            description: 'Project with high values',
            value: 1000000.99,
            cost: 750000.50
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'High Value Project',
            description: 'Project with high values',
            value: 1000000.99,
            cost: 750000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(1000000.99, $result->value);
        $this->assertEquals(750000.50, $result->cost);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $differentOrg->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) use ($differentOrg) {
                return $project->organization_id === $differentOrg->id;
            }))
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    private function createMockStoreRequest(): StoreRequest
    {
        return Mockery::mock(StoreRequest::class);
    }
}
