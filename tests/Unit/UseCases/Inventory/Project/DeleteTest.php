<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ProjectRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_project_successfully()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andReturn(true);

        $result = $this->useCase->perform($projectId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_project_belongs_to_different_organization()
    {
        $projectId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $project = new Project(
            id: $projectId,
            organization_id: $otherOrganization->id, // Different organization
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This project don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($projectId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $projectId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($projectId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($projectId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andReturn(false);

        $result = $this->useCase->perform($projectId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $projectId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id, // Project belongs to original org
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        // Should throw exception because project belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This project don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($projectId);
    }

    public function test_perform_with_project_without_client_and_budget()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Minimal Project',
            description: null,
            value: null,
            cost: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andReturn(true);

        $result = $this->useCase->perform($projectId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_value_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Project with zero values',
            value: 0.0,
            cost: 0.0
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andReturn(true);

        $result = $this->useCase->perform($projectId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'High Value Project',
            description: 'Project with high values',
            value: 1000000.99,
            cost: 750000.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($project)
            ->andReturn(true);

        $result = $this->useCase->perform($projectId);

        $this->assertTrue($result);
    }
}
