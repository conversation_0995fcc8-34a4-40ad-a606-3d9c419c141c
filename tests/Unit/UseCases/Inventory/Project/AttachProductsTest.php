<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Domains\Inventory\Project;
use App\Http\Requests\Project\AttachProductsRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\AttachProducts;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class AttachProductsTest extends TestCase
{
    use RefreshDatabase;

    private AttachProducts $useCase;
    private ProjectRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);

        $this->useCase = new AttachProducts($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_attaches_products_successfully()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('clearProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('clearCustomProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('attachProducts')
            ->once()
            ->with($project, Mockery::type(AttachProductsDomain::class));

        $this->mockRepository
            ->shouldReceive('attachCustomProducts')
            ->once()
            ->with($project, Mockery::type(AttachCustomDomain::class));

        $result = $this->useCase->perform($request, $projectId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_project_belongs_to_different_organization()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        $otherOrganization = Organization::factory()->create();
        
        $project = new Project(
            id: $projectId,
            organization_id: $otherOrganization->id, // Different organization
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldNotReceive('clearProducts');
        $this->mockRepository
            ->shouldNotReceive('clearCustomProducts');
        $this->mockRepository
            ->shouldNotReceive('attachProducts');
        $this->mockRepository
            ->shouldNotReceive('attachCustomProducts');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This project don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $projectId = 999;
        $request = $this->createMockAttachProductsRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_with_empty_products_and_custom_products()
    {
        $projectId = 1;
        $request = $this->createMockEmptyAttachProductsRequest();
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('clearProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('clearCustomProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('attachProducts')
            ->once()
            ->with($project, Mockery::on(function ($attachProducts) {
                return $attachProducts instanceof AttachProductsDomain && 
                       empty($attachProducts->products);
            }));

        $this->mockRepository
            ->shouldReceive('attachCustomProducts')
            ->once()
            ->with($project, Mockery::on(function ($attachCustomProducts) {
                return $attachCustomProducts instanceof AttachCustomDomain && 
                       empty($attachCustomProducts->customProducts);
            }));

        $result = $this->useCase->perform($request, $projectId);

        $this->assertTrue($result);
    }

    public function test_perform_handles_clear_products_exception()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('clearProducts')
            ->once()
            ->with($project)
            ->andThrow(new Exception('Clear products error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Clear products error');

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_handles_attach_products_exception()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('clearProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('clearCustomProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('attachProducts')
            ->once()
            ->with($project, Mockery::type(AttachProductsDomain::class))
            ->andThrow(new Exception('Attach products error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Attach products error');

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_handles_attach_custom_products_exception()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $this->mockRepository
            ->shouldReceive('clearProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('clearCustomProducts')
            ->once()
            ->with($project);

        $this->mockRepository
            ->shouldReceive('attachProducts')
            ->once()
            ->with($project, Mockery::type(AttachProductsDomain::class));

        $this->mockRepository
            ->shouldReceive('attachCustomProducts')
            ->once()
            ->with($project, Mockery::type(AttachCustomDomain::class))
            ->andThrow(new Exception('Attach custom products error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Attach custom products error');

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $projectId = 1;
        $request = $this->createMockAttachProductsRequest();
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id, // Project belongs to original org
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        // Should throw exception because project belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This project don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $projectId);
    }

    private function createMockAttachProductsRequest(): AttachProductsRequest
    {
        $request = Mockery::mock(AttachProductsRequest::class);
        
        $request->shouldReceive('input')
            ->with('products', [])
            ->andReturn([
                1 => ['quantity' => 5, 'value' => 100.00, 'description' => 'Product 1'],
                2 => ['quantity' => 3, 'value' => 200.00, 'description' => 'Product 2']
            ]);

        $request->shouldReceive('input')
            ->with('custom_products', [])
            ->andReturn([
                ['name' => 'Custom Product 1', 'description' => 'Description 1', 'quantity' => 2, 'value' => 150.00],
                ['name' => 'Custom Product 2', 'description' => 'Description 2', 'quantity' => 1, 'value' => 300.00]
            ]);

        return $request;
    }

    private function createMockEmptyAttachProductsRequest(): AttachProductsRequest
    {
        $request = Mockery::mock(AttachProductsRequest::class);
        
        $request->shouldReceive('input')
            ->with('products', [])
            ->andReturn([]);

        $request->shouldReceive('input')
            ->with('custom_products', [])
            ->andReturn([]);

        return $request;
    }
}
