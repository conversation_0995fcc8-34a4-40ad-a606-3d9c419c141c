<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetRepository;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\StoreFromBudget;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class StoreFromBudgetTest extends TestCase
{
    use RefreshDatabase;

    private StoreFromBudget $useCase;
    private ProjectRepository $mockProjectRepository;
    private BudgetRepository $mockBudgetRepository;
    private ProjectFactory $mockProjectFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockProjectRepository = Mockery::mock(ProjectRepository::class);
        $this->mockBudgetRepository = Mockery::mock(BudgetRepository::class);
        $this->mockProjectFactory = Mockery::mock(ProjectFactory::class);

        $this->useCase = new StoreFromBudget(
            $this->mockProjectRepository,
            $this->mockBudgetRepository,
            $this->mockProjectFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_project_from_budget_successfully()
    {
        $budgetId = 1;
        $request = new Request([
            'name' => 'Custom Project Name',
            'description' => 'Custom project description'
        ]);
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $domainFromBudget = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Custom Project Name',
            description: 'Custom project description',
            value: null,
            cost: null
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Custom Project Name',
            description: 'Custom project description',
            value: null,
            cost: null
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->with($budget, $request)
            ->andReturn($domainFromBudget);

        $this->mockProjectRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) {
                return $project instanceof Project && 
                       $project->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request, $budgetId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('Custom Project Name', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals($budgetId, $result->budget_id);
    }

    public function test_perform_throws_exception_when_budget_belongs_to_different_organization()
    {
        $budgetId = 1;
        $request = new Request();
        $otherOrganization = Organization::factory()->create();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $otherOrganization->id, // Different organization
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldNotReceive('buildFromBudget');
        $this->mockProjectRepository
            ->shouldNotReceive('store');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This budget don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $budgetId);
    }

    public function test_perform_handles_budget_repository_fetch_exception()
    {
        $budgetId = 999;
        $request = new Request();

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($request, $budgetId);
    }

    public function test_perform_sets_organization_id_from_authenticated_user()
    {
        $budgetId = 1;
        $request = new Request();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $domainFromBudget = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Project from budget #1',
            description: 'Test budget description',
            value: null,
            cost: null
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Project from budget #1',
            description: 'Test budget description',
            value: null,
            cost: null
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->andReturn($domainFromBudget);

        $this->mockProjectRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) {
                return $project->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProject);

        $this->useCase->perform($request, $budgetId);
    }

    public function test_perform_with_empty_request()
    {
        $budgetId = 5;
        $request = new Request(); // Empty request
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 2,
            name: 'Source Budget',
            description: 'Source budget description',
            value: 25000.00,
            cost: 18000.00,
            created_at: now(),
            updated_at: now()
        );

        $domainFromBudget = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: $budgetId,
            name: 'Project from budget #5',
            description: 'Source budget description',
            value: null,
            cost: null
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $this->organization->id,
            client_id: 2,
            budget_id: $budgetId,
            name: 'Project from budget #5',
            description: 'Source budget description',
            value: null,
            cost: null
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->with($budget, $request)
            ->andReturn($domainFromBudget);

        $this->mockProjectRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request, $budgetId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals('Project from budget #5', $result->name);
        $this->assertEquals('Source budget description', $result->description);
        $this->assertEquals(2, $result->client_id);
        $this->assertEquals($budgetId, $result->budget_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $budgetId = 1;
        $request = new Request();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $budgetId);
    }

    public function test_perform_handles_project_repository_store_exception()
    {
        $budgetId = 1;
        $request = new Request();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $domainFromBudget = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Project from budget #1',
            description: 'Test budget description',
            value: null,
            cost: null
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->andReturn($domainFromBudget);

        $this->mockProjectRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request, $budgetId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $budgetId = 1;
        $request = new Request();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $differentOrg->id,
            client_id: 1,
            name: 'Test Budget',
            description: 'Test budget description',
            value: 20000.00,
            cost: 15000.00,
            created_at: now(),
            updated_at: now()
        );

        $domainFromBudget = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Project from budget #1',
            description: 'Test budget description',
            value: null,
            cost: null
        );

        $storedProject = new Project(
            id: 1,
            organization_id: $differentOrg->id,
            client_id: 1,
            budget_id: $budgetId,
            name: 'Project from budget #1',
            description: 'Test budget description',
            value: null,
            cost: null
        );

        $this->mockBudgetRepository
            ->shouldReceive('fetchById')
            ->once()
            ->andReturn($budget);

        $this->mockProjectFactory
            ->shouldReceive('buildFromBudget')
            ->once()
            ->andReturn($domainFromBudget);

        $this->mockProjectRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($project) use ($differentOrg) {
                return $project->organization_id === $differentOrg->id;
            }))
            ->andReturn($storedProject);

        $result = $this->useCase->perform($request, $budgetId);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }
}
