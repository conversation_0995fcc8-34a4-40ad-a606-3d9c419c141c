<?php

namespace Tests\Unit\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\StockEntry\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private StockEntryRepository $mockRepository;
    private StockEntryFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockEntryRepository::class);
        $this->mockFactory = Mockery::mock(StockEntryFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_stock_entry_successfully()
    {
        $entryId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingEntry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Old description'
        );

        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: null,
            user_id: null,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated description'
        );

        $updatedEntry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($existingEntry);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($entry) use ($entryId) {
                return $entry instanceof StockEntry &&
                       $entry->id === $entryId &&
                       $entry->organization_id === $this->organization->id &&
                       $entry->quantity === 100;
            }), $this->organization->id)
            ->andReturn($updatedEntry);

        $result = $this->useCase->perform($entryId, $request);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($entryId, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Updated description', $result->description);
    }

    public function test_perform_throws_exception_when_entry_belongs_to_different_organization()
    {
        $entryId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingEntry = new StockEntry(
            id: $entryId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Other entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($existingEntry);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock entry don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($entryId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $entryId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($entryId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $entryId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingEntry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Existing entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($existingEntry);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($entryId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $entryId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingEntry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Existing entry'
        );

        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: null,
            user_id: null,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Updated entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($existingEntry);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($entryId, $request);
    }

    public function test_perform_preserves_original_id_organization_shop_and_user()
    {
        $entryId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingEntry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Preserve entry'
        );

        $domainFromRequest = new StockEntry(
            id: 999, // Different ID in request
            organization_id: 888, // Different organization in request
            shop_id: 777, // Different shop in request
            user_id: 666, // Different user in request
            brand_id: 2,
            product_id: 2,
            batch_id: 2,
            client_id: 2,
            project_id: 2,
            quantity: 200,
            value: 3000.75,
            description: 'Preserve updated entry'
        );

        $updatedEntry = new StockEntry(
            id: $entryId, // Should preserve original
            organization_id: $this->organization->id, // Should preserve original
            shop_id: 1, // Should preserve original
            user_id: $this->user->id, // Should preserve original
            brand_id: 2,
            product_id: 2,
            batch_id: 2,
            client_id: 2,
            project_id: 2,
            quantity: 200,
            value: 3000.75,
            description: 'Preserve updated entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($existingEntry);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($entry) use ($entryId) {
                return $entry->id === $entryId && 
                       $entry->organization_id === $this->organization->id &&
                       $entry->shop_id === 1 &&
                       $entry->user_id === $this->user->id;
            }), $this->organization->id)
            ->andReturn($updatedEntry);

        $result = $this->useCase->perform($entryId, $request);

        $this->assertEquals($entryId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\StockEntry\UpdateRequest {
            public $brand_id = 1;
            public $product_id = 1;
            public $batch_id = 1;
            public $client_id = 1;
            public $project_id = 1;
            public $quantity = 100;
            public $value = 1500.50;
            public $description = 'Updated description';
        };
    }
}
