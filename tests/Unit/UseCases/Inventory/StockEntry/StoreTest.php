<?php

namespace Tests\Unit\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\Stock\IncreaseStock;
use App\UseCases\Inventory\StockEntry\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private StockEntryRepository $mockRepository;
    private StockEntryFactory $mockFactory;
    private IncreaseStock $mockIncreaseStock;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockEntryRepository::class);
        $this->mockFactory = Mockery::mock(StockEntryFactory::class);
        $this->mockIncreaseStock = Mockery::mock(IncreaseStock::class);

        // Mock the app container to return our mock
        $this->app->instance(IncreaseStock::class, $this->mockIncreaseStock);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_stock_entry_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Store entry'
        );

        $storedEntry = new StockEntry(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Store entry'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry instanceof StockEntry &&
                       $entry->organization_id === $this->organization->id &&
                       $entry->quantity === 100;
            }))
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: null,
            description: 'Organization test'
        );

        $storedEntry = new StockEntry(
            id: 2,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Organization test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->organization_id === $this->organization->id;
            }))
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_calls_calculate_value()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = Mockery::mock(StockEntry::class);
        $domainFromRequest->shouldReceive('calculateValue')->once();
        $domainFromRequest->organization_id = null;

        $storedEntry = new StockEntry(
            id: 3,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 75,
            value: 1125.75,
            description: 'Calculate value test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockEntry::class, $result);
    }

    public function test_perform_calls_increase_stock()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 25,
            value: null,
            description: 'Increase stock test'
        );

        $storedEntry = new StockEntry(
            id: 4,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 25,
            value: 375.25,
            description: 'Increase stock test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(25, $result->quantity);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_increase_stock_exception()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: null,
            description: 'Increase stock error test'
        );

        $storedEntry = new StockEntry(
            id: 5,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Increase stock error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry)
            ->andThrow(new Exception('Increase stock error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Increase stock error');

        $this->useCase->perform($request);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\StockEntry\StoreRequest {
            public $shop_id = 1;
            public $brand_id = 1;
            public $product_id = 1;
            public $batch_id = 1;
            public $client_id = 1;
            public $project_id = 1;
            public $quantity = 100;
            public $value = 1500.50;
            public $description = 'Store entry';

            public function user()
            {
                return (object) ['id' => 1];
            }
        };
    }
}
