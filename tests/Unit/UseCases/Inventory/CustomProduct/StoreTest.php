<?php

namespace Tests\Unit\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\CustomProductRepository;
use App\UseCases\Inventory\CustomProduct\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private CustomProductRepository $mockRepository;
    private CustomProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(CustomProductRepository::class);
        $this->mockFactory = Mockery::mock(CustomProductFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_custom_product_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $storedCustomProduct = new CustomProduct(
            id: 1,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test custom product', $result->description);
    }

    public function test_perform_with_different_quantities()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'High quantity custom product'
        );

        $storedCustomProduct = new CustomProduct(
            id: 2,
            project_id: 1,
            budget_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'High quantity custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(50, $result->quantity);
        $this->assertEquals(750.25, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom product'
        );

        $storedCustomProduct = new CustomProduct(
            id: 3,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'Very high quantity custom product'
        );

        $storedCustomProduct = new CustomProduct(
            id: 4,
            project_id: 1,
            budget_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'Very high quantity custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_decimal_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom product'
        );

        $storedCustomProduct = new CustomProduct(
            id: 5,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $request = $this->createMockStoreRequest();
        $longDescription = str_repeat('Long description text. ', 20);
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $storedCustomProduct = new CustomProduct(
            id: 6,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedCustomProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\CustomProduct\StoreRequest {
            public $project_id = 1;
            public $budget_id = 1;
            public $quantity = 10;
            public $value = 150.50;
            public $description = 'Test custom product';
        };
    }
}
