<?php

namespace Tests\Unit\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\CustomProductRepository;
use App\UseCases\Inventory\CustomProduct\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private CustomProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(CustomProductRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_custom_product_successfully()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($customProductId);

        $this->assertTrue($result);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $customProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($customProductId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($customProductId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(false);

        $result = $this->useCase->perform($customProductId);

        $this->assertFalse($result);
    }

    public function test_perform_with_different_custom_product_types()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $customProductId = $index + 1;
            
            $customProduct = new CustomProduct(
                id: $customProductId,
                project_id: 1,
                budget_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value'],
                description: 'Test custom product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($customProductId)
                ->andReturn($customProduct);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($customProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($customProductId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_zero_quantity_custom_product()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($customProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_custom_product()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High value custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($customProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_long_description_custom_product()
    {
        $customProductId = 1;
        $longDescription = str_repeat('Long description text. ', 20);
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($customProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_decimal_values()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($customProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($customProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_different_project_and_budget_ids()
    {
        $testCases = [
            ['project_id' => 1, 'budget_id' => 1],
            ['project_id' => 5, 'budget_id' => 3],
            ['project_id' => 10, 'budget_id' => 7],
        ];

        foreach ($testCases as $index => $testCase) {
            $customProductId = $index + 1;
            
            $customProduct = new CustomProduct(
                id: $customProductId,
                project_id: $testCase['project_id'],
                budget_id: $testCase['budget_id'],
                quantity: 10,
                value: 150.50,
                description: 'Test custom product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($customProductId)
                ->andReturn($customProduct);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($customProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($customProductId);

            $this->assertTrue($result);
        }
    }
}
