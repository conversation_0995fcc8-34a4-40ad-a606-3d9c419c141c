<?php

namespace Tests\Unit\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\CustomProductRepository;
use App\UseCases\Inventory\CustomProduct\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private CustomProductRepository $mockRepository;
    private CustomProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(CustomProductRepository::class);
        $this->mockFactory = Mockery::mock(CustomProductFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_custom_product_successfully()
    {
        $customProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 2,
            budget_id: 2,
            quantity: 20,
            value: 300.75,
            description: 'Updated custom product'
        );

        $updatedCustomProduct = new CustomProduct(
            id: $customProductId,
            project_id: 2,
            budget_id: 2,
            quantity: 20,
            value: 300.75,
            description: 'Updated custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($domain) use ($customProductId) {
                return $domain->id === $customProductId;
            }))
            ->andReturn($updatedCustomProduct);

        $result = $this->useCase->perform($request, $customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($customProductId, $result->id);
        $this->assertEquals(20, $result->quantity);
        $this->assertEquals(300.75, $result->value);
        $this->assertEquals('Updated custom product', $result->description);
    }

    public function test_perform_with_different_quantities()
    {
        $customProductId = 2;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 3,
            budget_id: 3,
            quantity: 100,
            value: 1500.00,
            description: 'High quantity update'
        );

        $updatedCustomProduct = new CustomProduct(
            id: $customProductId,
            project_id: 3,
            budget_id: 3,
            quantity: 100,
            value: 1500.00,
            description: 'High quantity update'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedCustomProduct);

        $result = $this->useCase->perform($request, $customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.00, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $customProductId = 3;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity update'
        );

        $updatedCustomProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity update'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedCustomProduct);

        $result = $this->useCase->perform($request, $customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $customProductId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $customProductId);
    }

    public function test_perform_handles_repository_exception()
    {
        $customProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 2,
            budget_id: 2,
            quantity: 20,
            value: 300.75,
            description: 'Updated custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request, $customProductId);
    }

    public function test_perform_with_decimal_values()
    {
        $customProductId = 4;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value update'
        );

        $updatedCustomProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value update'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedCustomProduct);

        $result = $this->useCase->perform($request, $customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $customProductId = 5;
        $request = $this->createMockUpdateRequest();
        $longDescription = str_repeat('Updated long description text. ', 20);
        
        $domainFromRequest = new CustomProduct(
            id: null,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $updatedCustomProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedCustomProduct);

        $result = $this->useCase->perform($request, $customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_sets_id_correctly()
    {
        $customProductId = 99;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new CustomProduct(
            id: null, // Should be null from factory
            project_id: 2,
            budget_id: 2,
            quantity: 20,
            value: 300.75,
            description: 'Updated custom product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($domain) use ($customProductId) {
                // Verify that the ID was set correctly
                return $domain->id === $customProductId;
            }))
            ->andReturn($domainFromRequest);

        $this->useCase->perform($request, $customProductId);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\CustomProduct\UpdateRequest {
            public $project_id = 2;
            public $budget_id = 2;
            public $quantity = 20;
            public $value = 300.75;
            public $description = 'Updated custom product';
        };
    }
}
