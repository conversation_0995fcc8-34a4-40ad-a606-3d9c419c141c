<?php

namespace Tests\Unit\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupRepository;
use App\UseCases\Inventory\Group\Get;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private GroupRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupRepository::class);
        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_group_when_belongs_to_organization()
    {
        $groupId = 1;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Test Group',
            description: 'Test group description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Test Group', $result->name);
        $this->assertEquals('Test group description', $result->description);
    }

    public function test_perform_throws_exception_when_group_belongs_to_different_organization()
    {
        $groupId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $group = new Group(
            id: $groupId,
            organization_id: $otherOrganization->id,
            name: 'Other Org Group',
            description: 'Other org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupId);
    }

    public function test_perform_handles_repository_exception()
    {
        $groupId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Group not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Group not found');

        $this->useCase->perform($groupId);
    }

    public function test_perform_with_group_without_description()
    {
        $groupId = 2;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group Without Description',
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals('Group Without Description', $result->name);
        $this->assertNull($result->description);
    }

    public function test_perform_validates_organization_ownership()
    {
        $groupId = 3;
        
        // Create a group that belongs to the user's organization
        $validGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Valid Group',
            description: 'Valid group description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($validGroup);

        $result = $this->useCase->perform($groupId);

        // Should not throw exception and return the group
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_null_organization_id_throws_exception()
    {
        $groupId = 4;
        $group = new Group(
            id: $groupId,
            organization_id: null,
            name: 'No Org Group',
            description: 'No org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $groupId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $group = new Group(
            id: $groupId,
            organization_id: $newOrganization->id,
            name: 'New Org Group',
            description: 'New org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertEquals($newOrganization->id, $result->organization_id);
    }

    public function test_perform_with_zero_id()
    {
        $groupId = 0;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Invalid group ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group ID');

        $this->useCase->perform($groupId);
    }

    public function test_perform_with_negative_id()
    {
        $groupId = -1;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Invalid group ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group ID');

        $this->useCase->perform($groupId);
    }

    public function test_perform_with_group_with_empty_description()
    {
        $groupId = 6;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group With Empty Description',
            description: ''
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals('Group With Empty Description', $result->name);
        $this->assertEquals('', $result->description);
    }

    public function test_perform_with_group_with_long_description()
    {
        $groupId = 7;
        $longDescription = str_repeat('This is a very long description. ', 20);
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group With Long Description',
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals('Group With Long Description', $result->name);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_with_special_characters_in_name_and_description()
    {
        $groupId = 8;
        $specialName = 'Group with Special Characters: @#$%^&*()';
        $specialDescription = 'Description with special chars: <>&"\'';
        
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: $specialName,
            description: $specialDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals($specialName, $result->name);
        $this->assertEquals($specialDescription, $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $groupId = 9;
        $unicodeName = 'Grupo de Prueba 测试组 グループ';
        $unicodeDescription = 'Descripción de prueba 测试描述 説明';
        
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: $unicodeName,
            description: $unicodeDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $result = $this->useCase->perform($groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals($unicodeName, $result->name);
        $this->assertEquals($unicodeDescription, $result->description);
    }
}
