<?php

namespace Tests\Unit\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Factories\Inventory\GroupFactory;
use App\Http\Requests\Group\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupRepository;
use App\UseCases\Inventory\Group\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private GroupRepository $mockRepository;
    private GroupFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupRepository::class);
        $this->mockFactory = Mockery::mock(GroupFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_group_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Store Group',
            description: 'Store group description'
        );

        $storedGroup = new Group(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Store Group',
            description: 'Store group description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($group) {
                return $group instanceof Group &&
                       $group->organization_id === $this->organization->id &&
                       $group->name === 'Store Group';
            }))
            ->andReturn($storedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Store Group', $result->name);
        $this->assertEquals('Store group description', $result->description);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Organization Group',
            description: 'Organization group description'
        );

        $storedGroup = new Group(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Organization Group',
            description: 'Organization group description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($group) {
                return $group->organization_id === $this->organization->id;
            }))
            ->andReturn($storedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Exception Group',
            description: 'Exception group description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_null_description()
    {
        $request = $this->createMockStoreRequest(['description' => null]);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Group Without Description',
            description: null
        );

        $storedGroup = new Group(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Group Without Description',
            description: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($group) {
                return $group->description === null;
            }))
            ->andReturn($storedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertNull($result->description);
    }

    public function test_perform_with_empty_description()
    {
        $request = $this->createMockStoreRequest(['description' => '']);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Group With Empty Description',
            description: ''
        );

        $storedGroup = new Group(
            id: 4,
            organization_id: $this->organization->id,
            name: 'Group With Empty Description',
            description: ''
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($group) {
                return $group->description === '';
            }))
            ->andReturn($storedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals('', $result->description);
    }

    public function test_perform_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description. ', 20);
        $request = $this->createMockStoreRequest(['description' => $longDescription]);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Group With Long Description',
            description: $longDescription
        );

        $storedGroup = new Group(
            id: 5,
            organization_id: $this->organization->id,
            name: 'Group With Long Description',
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($group) use ($longDescription) {
                return $group->description === $longDescription;
            }))
            ->andReturn($storedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals($longDescription, $result->description);
    }

    private function createMockStoreRequest(array $data = []): StoreRequest
    {
        $defaultData = [
            'name' => 'Test Group',
            'description' => 'Test group description',
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(StoreRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
