<?php

namespace Tests\Unit\UseCases\Inventory\Group;

use App\Domains\Filters\GroupFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Group;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupRepository;
use App\UseCases\Inventory\Group\GetAll;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private GroupRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupRepository::class);
        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_groups_from_organization()
    {
        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Group 1', 'Description 1'),
                new Group(2, $this->organization->id, 'Group 2', 'Description 2'),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $group) {
            $this->assertInstanceOf(Group::class, $group);
            $this->assertEquals($this->organization->id, $group->organization_id);
        }
    }

    public function test_perform_with_filters()
    {
        $filters = new GroupFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'name', 'by' => 'asc', 'limit' => 5]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Alpha Group', 'Alpha description'),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Group', $result['data'][0]->name);
    }

    public function test_perform_with_description_filter()
    {
        $filters = new GroupFilters(['description' => 'Special']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 20]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Special Group 1', 'Special description'),
                new Group(3, $this->organization->id, 'Special Group 2', 'Special description'),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        foreach ($result['data'] as $group) {
            $this->assertStringContainsString('Special', $group->description);
        }
    }

    public function test_perform_with_pagination()
    {
        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'id', 'by' => 'asc', 'limit' => 2]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Group 1', 'Description 1'),
                new Group(2, $this->organization->id, 'Group 2', 'Description 2'),
            ],
            'count' => 2,
            'total' => 5,
            'currentPage' => 1,
            'lastPage' => 3,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(5, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_with_empty_result()
    {
        $filters = new GroupFilters(['name' => 'NonExistent']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(0, $result['total']);
    }

    public function test_perform_handles_repository_exception()
    {
        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($filters, $orderBy);
    }

    public function test_perform_uses_authenticated_user_organization_id()
    {
        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        // Verify that the correct organization ID is passed
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, Mockery::any(), Mockery::any())
            ->andReturn($expectedResult);

        $this->useCase->perform($filters, $orderBy);
    }

    public function test_perform_with_different_order_by_configurations()
    {
        $filters = new GroupFilters([]);
        
        // Test different ordering configurations
        $orderConfigs = [
            ['order' => 'name', 'by' => 'asc', 'limit' => 15],
            ['order' => 'description', 'by' => 'desc', 'limit' => 25],
            ['order' => 'updated_at', 'by' => 'asc', 'limit' => 50],
        ];

        foreach ($orderConfigs as $config) {
            $orderBy = new OrderBy($config);
            
            $expectedResult = [
                'data' => [],
                'count' => 0,
                'total' => 0,
                'currentPage' => 1,
                'lastPage' => 1,
            ];

            $this->mockRepository
                ->shouldReceive('fetchFromOrganization')
                ->once()
                ->with($this->organization->id, $filters, $orderBy)
                ->andReturn($expectedResult);

            $result = $this->useCase->perform($filters, $orderBy);
            $this->assertIsArray($result);
        }
    }

    public function test_perform_with_multiple_filters()
    {
        $filters = new GroupFilters(['name' => 'Test', 'description' => 'Special']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Test Group', 'Special description'),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Test Group', $result['data'][0]->name);
        $this->assertEquals('Special description', $result['data'][0]->description);
    }

    public function test_perform_with_groups_without_description()
    {
        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [
                new Group(1, $this->organization->id, 'Group With Description', 'Has description'),
                new Group(2, $this->organization->id, 'Group Without Description', null),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals('Has description', $result['data'][0]->description);
        $this->assertNull($result['data'][1]->description);
    }
}
