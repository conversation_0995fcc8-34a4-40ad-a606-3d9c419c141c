<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\Stock;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\RefreshProductStockFromPriceChange;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class RefreshProductStockFromPriceChangeTest extends TestCase
{
    use RefreshDatabase;

    private RefreshProductStockFromPriceChange $useCase;
    private StockRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);

        $this->useCase = new RefreshProductStockFromPriceChange($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_refreshes_stock_value_successfully()
    {
        $product = $this->createMockProduct();
        $product->price = 15.0; // New price

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 100,
            value: 1000.0, // Old value (100 * 10.0)
            description: 'Test stock'
        );

        $refreshedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 100,
            value: 1500.0, // New value (100 * 15.0)
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 100 &&
                       $stockToSave->value === 1500.0;
            }))
            ->andReturn($refreshedStock);

        $result = $this->useCase->perform($product);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.0, $result->value);
    }

    public function test_perform_returns_null_when_stock_not_found()
    {
        $product = $this->createMockProduct();
        $product->price = 15.0;

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn(null);

        $this->mockRepository
            ->shouldNotReceive('save');

        $result = $this->useCase->perform($product);

        $this->assertNull($result);
    }

    public function test_perform_with_zero_price_product()
    {
        $product = $this->createMockProduct();
        $product->price = 0.0; // Free product

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 50,
            value: 500.0, // Old value
            description: 'Free product stock'
        );

        $refreshedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 50,
            value: 0.0, // New value (50 * 0.0)
            description: 'Free product stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 50 &&
                       $stockToSave->value === 0.0;
            }))
            ->andReturn($refreshedStock);

        $result = $this->useCase->perform($product);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(50, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_price_product()
    {
        $product = $this->createMockProduct();
        $product->price = 999.99; // Expensive product

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 1000,
            value: 99999.0, // Old value
            description: 'Expensive product stock'
        );

        $refreshedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 1000,
            value: 999990.0, // New value (1000 * 999.99)
            description: 'Expensive product stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 1000 &&
                       $stockToSave->value === 999990.0;
            }))
            ->andReturn($refreshedStock);

        $result = $this->useCase->perform($product);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(1000, $result->quantity);
        $this->assertEquals(999990.0, $result->value);
    }

    public function test_perform_with_zero_quantity_stock()
    {
        $product = $this->createMockProduct();
        $product->price = 20.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 0,
            value: 100.0, // Old value
            description: 'Empty stock'
        );

        $refreshedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 0,
            value: 0.0, // New value (0 * 20.0)
            description: 'Empty stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 0 &&
                       $stockToSave->value === 0.0;
            }))
            ->andReturn($refreshedStock);

        $result = $this->useCase->perform($product);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $product = $this->createMockProduct();
        $product->price = 15.0;

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $differentOrg->id) // Should use different organization
            ->andReturn(null);

        $result = $this->useCase->perform($product);

        $this->assertNull($result);
    }

    public function test_perform_handles_repository_find_exception()
    {
        $product = $this->createMockProduct();
        $product->price = 15.0;

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($product);
    }

    public function test_perform_handles_repository_save_exception()
    {
        $product = $this->createMockProduct();
        $product->price = 15.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->andThrow(new Exception('Save error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Save error');

        $this->useCase->perform($product);
    }

    public function test_perform_with_decimal_price()
    {
        $product = $this->createMockProduct();
        $product->price = 12.34; // Decimal price

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 25,
            value: 250.0, // Old value
            description: 'Decimal price stock'
        );

        $refreshedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $product->id,
            quantity: 25,
            value: 308.5, // New value (25 * 12.34)
            description: 'Decimal price stock'
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($product->id, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 25 &&
                       $stockToSave->value === 308.5;
            }))
            ->andReturn($refreshedStock);

        $result = $this->useCase->perform($product);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(25, $result->quantity);
        $this->assertEquals(308.5, $result->value);
    }

    private function createMockProduct(): Product
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now(),
            created_at: now(),
            updated_at: now()
        );
    }
}
