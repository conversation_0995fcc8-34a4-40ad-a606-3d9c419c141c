<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Http\Requests\Stock\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\Update;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private StockRepository $mockRepository;
    private StockFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);
        $this->mockFactory = Mockery::mock(StockFactory::class);

        $this->useCase = new Update(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_stock_successfully()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 200,
            value: 2000.0,
            description: 'Updated stock'
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 200,
            value: 2000.0,
            description: 'Updated stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($stock) use ($stockId) {
                    return $stock instanceof Stock && 
                           $stock->id === $stockId &&
                           $stock->quantity === 200;
                }),
                $this->organization->id
            )
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request, $stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($stockId, $result->id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_correct_stock_id()
    {
        $stockId = 123;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 150,
            value: 1500.0,
            description: 'Updated stock'
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 150,
            value: 1500.0,
            description: 'Updated stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($stock) use ($stockId) {
                    return $stock->id === $stockId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request, $stockId);

        $this->assertEquals($stockId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 100,
            value: 1000.0,
            description: 'Updated stock'
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Updated stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id  // Should use authenticated user's organization
            )
            ->andReturn($updatedStock);

        $this->useCase->perform($request, $stockId);
    }

    public function test_perform_with_null_values()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: null,
            value: null,
            description: null
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: null,
            value: null,
            description: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request, $stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertNull($result->quantity);
        $this->assertNull($result->value);
        $this->assertNull($result->description);
    }

    public function test_perform_with_zero_quantity()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 0,
            value: 0.0,
            description: 'Empty stock'
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Empty stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request, $stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_handles_repository_exception()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 100,
            value: 1000.0,
            description: 'Updated stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request, $stockId);
    }

    public function test_perform_handles_factory_exception()
    {
        $stockId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $stockId);
    }

    public function test_perform_with_different_organization_user()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $stockId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: null,
            brand_id: null,
            product_id: null,
            quantity: 75,
            value: 750.0,
            description: 'Updated stock'
        );

        $updatedStock = new Stock(
            id: $stockId,
            organization_id: $differentOrg->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 75,
            value: 750.0,
            description: 'Updated stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $differentOrg->id  // Should use the different organization
            )
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request, $stockId);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    private function createMockUpdateRequest(): UpdateRequest
    {
        return Mockery::mock(UpdateRequest::class);
    }
}
