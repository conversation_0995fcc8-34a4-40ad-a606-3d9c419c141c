<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\Stock;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\DecreaseStock;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DecreaseStockTest extends TestCase
{
    use RefreshDatabase;

    private DecreaseStock $useCase;
    private StockRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);

        $this->useCase = new DecreaseStock($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_decreases_stock_successfully()
    {
        $productId = 1;
        $quantity = 25;

        $product = $this->createMockProduct();
        $product->price = 10.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock',
            product: $product
        );

        $updatedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 75, // 100 - 25
            value: 750.0, // 75 * 10.0
            description: 'Test stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 75 &&
                       $stockToSave->value === 750.0;
            }))
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($productId, $quantity);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(75, $result->quantity);
        $this->assertEquals(750.0, $result->value);
    }

    public function test_perform_throws_exception_when_stock_not_found()
    {
        $productId = 999;
        $quantity = 10;

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn(null);

        $this->mockRepository
            ->shouldNotReceive('save');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Stock not found for this product in this organization.");

        $this->useCase->perform($productId, $quantity);
    }

    public function test_perform_throws_exception_when_insufficient_stock()
    {
        $productId = 1;
        $quantity = 150; // More than available

        $product = $this->createMockProduct();
        $product->price = 10.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 100, // Less than requested
            value: 1000.0,
            description: 'Test stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldNotReceive('save');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("There is not enough stock available of this product!");

        $this->useCase->perform($productId, $quantity);
    }

    public function test_perform_decreases_stock_to_zero()
    {
        $productId = 1;
        $quantity = 50; // Exact amount available

        $product = $this->createMockProduct();
        $product->price = 10.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 50,
            value: 500.0,
            description: 'Test stock',
            product: $product
        );

        $updatedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 0, // 50 - 50
            value: 0.0, // 0 * 10.0
            description: 'Test stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 0 &&
                       $stockToSave->value === 0.0;
            }))
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($productId, $quantity);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_zero_price_product()
    {
        $productId = 1;
        $quantity = 30;

        $product = $this->createMockProduct();
        $product->price = 0.0; // Free product

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 100,
            value: 0.0,
            description: 'Free product stock',
            product: $product
        );

        $updatedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 70, // 100 - 30
            value: 0.0, // 70 * 0.0
            description: 'Free product stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($productId, $quantity);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(70, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $productId = 1;
        $quantity = 25;

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $differentOrg->id) // Should use different organization
            ->andReturn(null);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Stock not found for this product in this organization.");

        $this->useCase->perform($productId, $quantity);
    }

    public function test_perform_handles_repository_save_exception()
    {
        $productId = 1;
        $quantity = 25;

        $product = $this->createMockProduct();
        $product->price = 10.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($productId, $quantity);
    }

    public function test_perform_with_high_quantity_decrease()
    {
        $productId = 1;
        $quantity = 999;

        $product = $this->createMockProduct();
        $product->price = 5.0;

        $stock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 1000,
            value: 5000.0,
            description: 'Large stock',
            product: $product
        );

        $updatedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: $productId,
            quantity: 1, // 1000 - 999
            value: 5.0, // 1 * 5.0
            description: 'Large stock',
            product: $product
        );

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with($productId, $this->organization->id)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($productId, $quantity);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(1, $result->quantity);
        $this->assertEquals(5.0, $result->value);
    }

    private function createMockProduct(): Product
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now(),
            created_at: now(),
            updated_at: now()
        );
    }
}
