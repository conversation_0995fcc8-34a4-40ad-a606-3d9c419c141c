<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private StockRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_stock_successfully()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($stockId, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $stockId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($stockId);
    }

    public function test_perform_with_minimal_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: null,
            brand_id: null,
            product_id: 1,
            quantity: 10,
            value: null,
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(10, $result->quantity);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->brand_id);
        $this->assertNull($result->value);
        $this->assertNull($result->description);
    }

    public function test_perform_with_complete_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 5,
            brand_id: 3,
            product_id: 2,
            quantity: 250,
            value: 2500.0,
            description: 'Complete stock with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($stockId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(5, $result->shop_id);
        $this->assertEquals(3, $result->brand_id);
        $this->assertEquals(2, $result->product_id);
        $this->assertEquals(250, $result->quantity);
        $this->assertEquals(2500.0, $result->value);
        $this->assertEquals('Complete stock with all fields', $result->description);
    }

    public function test_perform_with_zero_quantity_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Empty stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 9999999.99,
            description: 'Large stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(9999999.99, $result->value);
    }

    public function test_perform_returns_stock_with_all_properties()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 3,
            brand_id: 2,
            product_id: 4,
            quantity: 150,
            value: 1500.0,
            description: 'Stock with all properties set',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $result = $this->useCase->perform($stockId);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($stockId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(3, $result->shop_id);
        $this->assertEquals(2, $result->brand_id);
        $this->assertEquals(4, $result->product_id);
        $this->assertEquals(150, $result->quantity);
        $this->assertEquals(1500.0, $result->value);
        $this->assertEquals('Stock with all properties set', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_stock_ids()
    {
        // Test with different stock IDs to ensure correct ID handling
        $stockIds = [1, 42, 999, 12345];

        foreach ($stockIds as $stockId) {
            $stock = new Stock(
                id: $stockId,
                organization_id: $this->organization->id,
                shop_id: 1,
                brand_id: 1,
                product_id: 1,
                quantity: $stockId * 10,
                value: $stockId * 100.0,
                description: "Stock $stockId"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($stockId)
                ->andReturn($stock);

            $result = $this->useCase->perform($stockId);

            $this->assertInstanceOf(Stock::class, $result);
            $this->assertEquals($stockId, $result->id);
            $this->assertEquals("Stock $stockId", $result->description);
            $this->assertEquals($stockId * 10, $result->quantity);
            $this->assertEquals($stockId * 100.0, $result->value);
        }
    }

    public function test_perform_with_different_quantity_ranges()
    {
        $quantities = [0, 1, 50, 1000, 999999];

        foreach ($quantities as $index => $quantity) {
            $stockId = $index + 1;
            
            $stock = new Stock(
                id: $stockId,
                organization_id: $this->organization->id,
                shop_id: 1,
                brand_id: 1,
                product_id: 1,
                quantity: $quantity,
                value: $quantity * 10.0,
                description: "Stock with quantity $quantity"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($stockId)
                ->andReturn($stock);

            $result = $this->useCase->perform($stockId);

            $this->assertInstanceOf(Stock::class, $result);
            $this->assertEquals($quantity, $result->quantity);
            $this->assertEquals($quantity * 10.0, $result->value);
        }
    }
}
