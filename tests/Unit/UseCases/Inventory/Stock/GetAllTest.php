<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockFilters;
use App\Domains\Inventory\Stock;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private StockRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_stocks()
    {
        $request = new Request([
            'product_id' => 1,
            'order' => 'quantity',
            'by' => 'desc',
            'limit' => 10
        ]);

        $stocks = [
            new Stock(
                id: 1,
                organization_id: $this->organization->id,
                shop_id: 1,
                brand_id: 1,
                product_id: 1,
                quantity: 100,
                value: 1000.0,
                description: 'Test stock 1'
            ),
            new Stock(
                id: 2,
                organization_id: $this->organization->id,
                shop_id: 2,
                brand_id: 2,
                product_id: 1,
                quantity: 50,
                value: 500.0,
                description: 'Test stock 2'
            )
        ];

        $expectedResult = [
            'data' => $stocks,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof StockFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'quantity' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 10;
                }),
                false // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $stock) {
            $this->assertInstanceOf(Stock::class, $stock);
            $this->assertEquals($this->organization->id, $stock->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof StockFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'product_id' => 5,
            'shop_id' => 3,
            'brand_id' => 2,
            'quantity_min' => 10,
            'quantity_max' => 500,
            'value_min' => 100.0,
            'value_max' => 5000.0
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof StockFilters &&
                           isset($filters->filters['product_id']) &&
                           isset($filters->filters['shop_id']) &&
                           isset($filters->filters['brand_id']) &&
                           isset($filters->filters['quantity_min']) &&
                           isset($filters->filters['quantity_max']) &&
                           isset($filters->filters['value_min']) &&
                           isset($filters->filters['value_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'value',
            'by' => 'asc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'value' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 50;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_shop_relationship()
    {
        $request = new Request([
            'with_shop' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true  // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $stocks = [];
        for ($i = 1; $i <= 5; $i++) {
            $stocks[] = new Stock(
                id: $i,
                organization_id: $this->organization->id,
                shop_id: $i,
                brand_id: $i,
                product_id: $i,
                quantity: $i * 10,
                value: $i * 100.0,
                description: "Stock $i"
            );
        }

        $expectedResult = [
            'data' => $stocks,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }

    public function test_perform_with_quantity_range_filter()
    {
        $request = new Request([
            'quantity_min' => 50,
            'quantity_max' => 200
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof StockFilters &&
                           isset($filters->filters['quantity_min']) &&
                           isset($filters->filters['quantity_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_value_range_filter()
    {
        $request = new Request([
            'value_min' => 100.0,
            'value_max' => 1000.0
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof StockFilters &&
                           isset($filters->filters['value_min']) &&
                           isset($filters->filters['value_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }
}
