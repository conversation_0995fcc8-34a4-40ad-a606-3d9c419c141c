<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Http\Requests\Entry\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\CreateStockFromEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateStockFromEntryTest extends TestCase
{
    use RefreshDatabase;

    private CreateStockFromEntry $useCase;
    private StockRepository $mockRepository;
    private StockFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);
        $this->mockFactory = Mockery::mock(StockFactory::class);

        $this->useCase = new CreateStockFromEntry(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_new_stock_when_none_exists()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Entry stock'
        );

        $createdStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with(1, $this->organization->id)
            ->andReturn(null); // No existing stock

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($createdStock);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1000.0, $result->value);
    }

    public function test_perform_increases_existing_stock()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 50,
            value: 500.0,
            description: 'Entry stock'
        );

        $product = $this->createMockProduct();
        $product->price = 10.0;

        $existingStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Existing stock',
            product: $product
        );

        $updatedStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 150, // 100 + 50
            value: 1500.0, // 150 * 10.0
            description: 'Existing stock',
            product: $product
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with(1, $this->organization->id)
            ->andReturn($existingStock);

        $this->mockRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($stockToSave) {
                return $stockToSave instanceof Stock &&
                       $stockToSave->quantity === 150 &&
                       $stockToSave->value === 1500.0;
            }))
            ->andReturn($updatedStock);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(150, $result->quantity);
        $this->assertEquals(1500.0, $result->value);
    }

    public function test_perform_sets_organization_id_from_authenticated_user()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null, // Will be set by use case
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 75,
            value: 750.0,
            description: 'Entry stock'
        );

        $createdStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 75,
            value: 750.0,
            description: 'Entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($stock) {
                return $stock->organization_id === $this->organization->id;
            }))
            ->andReturn($createdStock);

        $this->useCase->perform($request);
    }

    public function test_perform_with_zero_quantity_entry()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero entry stock'
        );

        $createdStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($createdStock);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockEntryRequest();

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_different_organization_user()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: null,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Entry stock'
        );

        $createdStock = new Stock(
            id: 1,
            organization_id: $differentOrg->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->with(1, $differentOrg->id) // Should use different organization
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($stock) use ($differentOrg) {
                return $stock->organization_id === $differentOrg->id;
            }))
            ->andReturn($createdStock);

        $result = $this->useCase->perform($request);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    public function test_perform_with_high_quantity_entry()
    {
        $request = $this->createMockEntryRequest();
        
        $domainFromRequest = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 9999999.99,
            description: 'Large entry stock'
        );

        $createdStock = new Stock(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 9999999.99,
            description: 'Large entry stock'
        );

        $this->mockFactory
            ->shouldReceive('buildFromEntryRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('findByProductAndOrganization')
            ->once()
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($createdStock);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(9999999.99, $result->value);
    }

    private function createMockEntryRequest(): StoreRequest
    {
        return Mockery::mock(StoreRequest::class);
    }

    private function createMockProduct(): Product
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now(),
            created_at: now(),
            updated_at: now()
        );
    }
}
