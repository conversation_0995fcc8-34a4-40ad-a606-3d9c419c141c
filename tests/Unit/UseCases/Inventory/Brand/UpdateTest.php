<?php

namespace Tests\Unit\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Factories\Inventory\BrandFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BrandRepository;
use App\UseCases\Inventory\Brand\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private BrandRepository $mockRepository;
    private BrandFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BrandRepository::class);
        $this->mockFactory = Mockery::mock(BrandFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_brand_successfully()
    {
        $brandId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Old Brand',
            description: 'Old description'
        );

        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Updated Brand',
            description: 'Updated description'
        );

        $updatedBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Updated Brand',
            description: 'Updated description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($brand) use ($brandId) {
                return $brand instanceof Brand &&
                       $brand->id === $brandId &&
                       $brand->organization_id === $this->organization->id &&
                       $brand->name === 'Updated Brand';
            }), $this->organization->id)
            ->andReturn($updatedBrand);

        $result = $this->useCase->perform($brandId, $request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($brandId, $result->id);
        $this->assertEquals('Updated Brand', $result->name);
        $this->assertEquals('Updated description', $result->description);
    }

    public function test_perform_throws_exception_when_brand_belongs_to_different_organization()
    {
        $brandId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $otherOrganization->id, // Different organization
            name: 'Other Brand',
            description: 'Other description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This brand don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($brandId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $brandId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($brandId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $brandId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Existing Brand',
            description: 'Existing description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($brandId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $brandId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Existing Brand',
            description: 'Existing description'
        );

        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Updated Brand',
            description: 'Updated description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($brandId, $request);
    }

    public function test_perform_preserves_original_id_and_organization()
    {
        $brandId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Preserve Brand',
            description: 'Preserve description'
        );

        $domainFromRequest = new Brand(
            id: 999, // Different ID in request
            organization_id: 888, // Different organization in request
            name: 'Preserve Updated Brand',
            description: 'Preserve updated description'
        );

        $updatedBrand = new Brand(
            id: $brandId, // Should preserve original
            organization_id: $this->organization->id, // Should preserve original
            name: 'Preserve Updated Brand',
            description: 'Preserve updated description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($brand) use ($brandId) {
                return $brand->id === $brandId && 
                       $brand->organization_id === $this->organization->id;
            }), $this->organization->id)
            ->andReturn($updatedBrand);

        $result = $this->useCase->perform($brandId, $request);

        $this->assertEquals($brandId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_special_characters()
    {
        $brandId = 1;
        $request = $this->createMockSpecialCharactersUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Old Brand',
            description: 'Old description'
        );

        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $updatedBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedBrand);

        $result = $this->useCase->perform($brandId, $request);

        $this->assertEquals('Brand & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $brandId = 1;
        $request = $this->createMockUnicodeUpdateRequest();
        
        $existingBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Old Brand',
            description: 'Old description'
        );

        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $updatedBrand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($existingBrand);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedBrand);

        $result = $this->useCase->perform($brandId, $request);

        $this->assertEquals('Bränd Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Brand\UpdateRequest {
            public $name = 'Updated Brand';
            public $description = 'Updated description';
        };
    }

    private function createMockSpecialCharactersUpdateRequest()
    {
        return new class extends \App\Http\Requests\Brand\UpdateRequest {
            public $name = 'Brand & Co. (™)';
            public $description = 'Description with special chars: @#$%^&*()';
        };
    }

    private function createMockUnicodeUpdateRequest()
    {
        return new class extends \App\Http\Requests\Brand\UpdateRequest {
            public $name = 'Bränd Ñamé 中文';
            public $description = 'Descripción con caracteres especiales: ñáéíóú';
        };
    }
}
