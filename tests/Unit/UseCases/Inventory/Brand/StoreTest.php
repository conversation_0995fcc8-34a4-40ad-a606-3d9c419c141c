<?php

namespace Tests\Unit\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Factories\Inventory\BrandFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BrandRepository;
use App\UseCases\Inventory\Brand\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private BrandRepository $mockRepository;
    private BrandFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BrandRepository::class);
        $this->mockFactory = Mockery::mock(BrandFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_brand_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Store Brand',
            description: 'Store brand description'
        );

        $storedBrand = new Brand(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Store Brand',
            description: 'Store brand description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($brand) {
                return $brand instanceof Brand &&
                       $brand->organization_id === $this->organization->id &&
                       $brand->name === 'Store Brand';
            }))
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Store Brand', $result->name);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Organization Brand',
            description: 'Brand for organization test'
        );

        $storedBrand = new Brand(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Organization Brand',
            description: 'Brand for organization test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($brand) {
                return $brand->organization_id === $this->organization->id;
            }))
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Error Brand',
            description: 'Brand that will cause error'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_brand_data()
    {
        $request = $this->createMockMinimalStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Minimal Brand',
            description: ''
        );

        $storedBrand = new Brand(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Minimal Brand',
            description: ''
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Minimal Brand', $result->name);
        $this->assertEquals('', $result->description);
    }

    public function test_perform_with_special_characters()
    {
        $request = $this->createMockSpecialCharactersStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $storedBrand = new Brand(
            id: 4,
            organization_id: $this->organization->id,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Brand & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $request = $this->createMockUnicodeStoreRequest();
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $storedBrand = new Brand(
            id: 5,
            organization_id: $this->organization->id,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Bränd Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    public function test_perform_with_long_name_and_description()
    {
        $request = $this->createMockLongDataStoreRequest();
        
        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);
        
        $domainFromRequest = new Brand(
            id: null,
            organization_id: null,
            name: $longName,
            description: $longDescription
        );

        $storedBrand = new Brand(
            id: 6,
            organization_id: $this->organization->id,
            name: $longName,
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBrand);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($longName, $result->name);
        $this->assertEquals($longDescription, $result->description);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $name = 'Store Brand';
            public $description = 'Store brand description';
        };
    }

    private function createMockMinimalStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $name = 'Minimal Brand';
            public $description = '';
        };
    }

    private function createMockSpecialCharactersStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $name = 'Brand & Co. (™)';
            public $description = 'Description with special chars: @#$%^&*()';
        };
    }

    private function createMockUnicodeStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $name = 'Bränd Ñamé 中文';
            public $description = 'Descripción con caracteres especiales: ñáéíóú';
        };
    }

    private function createMockLongDataStoreRequest()
    {
        return new class extends \App\Http\Requests\Brand\StoreRequest {
            public $name;
            public $description;

            public function __construct()
            {
                $this->name = str_repeat('A', 255);
                $this->description = str_repeat('B', 1000);
            }
        };
    }
}
