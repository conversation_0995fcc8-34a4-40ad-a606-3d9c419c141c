<?php

namespace Tests\Unit\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BrandRepository;
use App\UseCases\Inventory\Brand\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private BrandRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BrandRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_brand_successfully()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Get Brand',
            description: 'Brand for get test'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($brandId, $result->id);
        $this->assertEquals('Get Brand', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $brandId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($brandId);
    }

    public function test_perform_with_minimal_brand()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Minimal Brand',
            description: ''
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Minimal Brand', $result->name);
        $this->assertEquals('', $result->description);
    }

    public function test_perform_with_complete_brand()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Complete Brand',
            description: 'Complete brand with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($brandId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Complete Brand', $result->name);
        $this->assertEquals('Complete brand with all fields', $result->description);
    }

    public function test_perform_with_special_characters()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Brand & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('Bränd Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    public function test_perform_with_long_name_and_description()
    {
        $brandId = 1;
        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: $longName,
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($longName, $result->name);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_returns_brand_with_all_properties()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Properties Brand',
            description: 'Brand with all properties set',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals($brandId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Properties Brand', $result->name);
        $this->assertEquals('Brand with all properties set', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_brand_ids()
    {
        // Test with different brand IDs to ensure correct ID handling
        $brandIds = [1, 42, 999, 12345];

        foreach ($brandIds as $brandId) {
            $brand = new Brand(
                id: $brandId,
                organization_id: $this->organization->id,
                name: "Brand $brandId",
                description: "Test brand $brandId"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($brandId)
                ->andReturn($brand);

            $result = $this->useCase->perform($brandId);

            $this->assertInstanceOf(Brand::class, $result);
            $this->assertEquals($brandId, $result->id);
            $this->assertEquals("Brand $brandId", $result->name);
            $this->assertEquals("Test brand $brandId", $result->description);
        }
    }

    public function test_perform_with_different_organization_ids()
    {
        $brandId = 1;
        $organizationIds = [1, 100, 999, 12345];

        foreach ($organizationIds as $orgId) {
            $brand = new Brand(
                id: $brandId,
                organization_id: $orgId,
                name: "Brand for Org $orgId",
                description: "Brand for organization $orgId"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($brandId)
                ->andReturn($brand);

            $result = $this->useCase->perform($brandId);

            $this->assertInstanceOf(Brand::class, $result);
            $this->assertEquals($orgId, $result->organization_id);
            $this->assertEquals("Brand for Org $orgId", $result->name);
        }
    }

    public function test_perform_with_empty_description()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'No Description Brand',
            description: ''
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $result = $this->useCase->perform($brandId);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('No Description Brand', $result->name);
        $this->assertEquals('', $result->description);
    }
}
