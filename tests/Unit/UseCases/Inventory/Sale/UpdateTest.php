<?php

namespace Tests\Unit\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\SaleRepository;
use App\UseCases\Inventory\Sale\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private SaleRepository $mockRepository;
    private SaleFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(SaleRepository::class);
        $this->mockFactory = Mockery::mock(SaleFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_sale_successfully()
    {
        $saleId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 2,
            shop_id: 2,
            client_id: 2,
            total_value: 200.75
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 2,
            shop_id: 2,
            client_id: 2,
            total_value: 200.75
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($sale) use ($saleId) {
                return $sale instanceof Sale &&
                       $sale->id === $saleId &&
                       $sale->user_id === 2 &&
                       $sale->total_value === 200.75;
            }), $this->organization->id)
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($saleId, $result->id);
        $this->assertEquals(2, $result->user_id);
        $this->assertEquals(200.75, $result->total_value);
    }

    public function test_perform_assigns_correct_id()
    {
        $saleId = 42;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 3,
            shop_id: 3,
            client_id: 3,
            total_value: 300.00
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 3,
            shop_id: 3,
            client_id: 3,
            total_value: 300.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($sale) use ($saleId) {
                return $sale->id === $saleId;
            }), $this->organization->id)
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertEquals($saleId, $result->id);
    }

    public function test_perform_handles_factory_exception()
    {
        $saleId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $saleId);
    }

    public function test_perform_handles_repository_exception()
    {
        $saleId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 2,
            shop_id: 2,
            client_id: 2,
            total_value: 200.75
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request, $saleId);
    }

    public function test_perform_with_minimal_update_data()
    {
        $saleId = 1;
        $request = $this->createMockMinimalUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 75.00
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 75.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(75.00, $result->total_value);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->client_id);
    }

    public function test_perform_with_zero_total_value()
    {
        $saleId = 1;
        $request = $this->createMockZeroValueUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(0.00, $result->total_value);
    }

    public function test_perform_with_high_total_value()
    {
        $saleId = 1;
        $request = $this->createMockHighValueUpdateRequest();
        
        $highValue = 999999.99;
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($highValue, $result->total_value);
    }

    public function test_perform_preserves_organization_context()
    {
        $saleId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 2,
            shop_id: 2,
            client_id: 2,
            total_value: 200.75
        );

        $updatedSale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: 2,
            shop_id: 2,
            client_id: 2,
            total_value: 200.75
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::any(), $this->organization->id)
            ->andReturn($updatedSale);

        $result = $this->useCase->perform($request, $saleId);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Sale\UpdateRequest {
            public $user_id = 2;
            public $shop_id = 2;
            public $client_id = 2;
            public $total_value = 200.75;
        };
    }

    private function createMockMinimalUpdateRequest()
    {
        return new class extends \App\Http\Requests\Sale\UpdateRequest {
            public $user_id = 1;
            public $shop_id = null;
            public $client_id = null;
            public $total_value = 75.00;
        };
    }

    private function createMockZeroValueUpdateRequest()
    {
        return new class extends \App\Http\Requests\Sale\UpdateRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 0.00;
        };
    }

    private function createMockHighValueUpdateRequest()
    {
        return new class extends \App\Http\Requests\Sale\UpdateRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 999999.99;
        };
    }
}
