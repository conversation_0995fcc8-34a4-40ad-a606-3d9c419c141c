<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Filters\BatchFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Batch;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private BatchRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BatchRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_batches()
    {
        $request = new Request([
            'product_id' => 1,
            'order' => 'quantity',
            'by' => 'desc',
            'limit' => 10
        ]);

        $batches = [
            new Batch(
                id: 1,
                organization_id: $this->organization->id,
                shop_id: 1,
                product_id: 1,
                batch_number: 'BATCH-001',
                name: 'Test Batch 1',
                description: 'Test batch 1 description',
                quantity: 100,
                produced_at: now()->subDays(10),
                expired_at: now()->addDays(20),
                processed_at: null,
                is_processed_at_stock: false
            ),
            new Batch(
                id: 2,
                organization_id: $this->organization->id,
                shop_id: 1,
                product_id: 1,
                batch_number: 'BATCH-002',
                name: 'Test Batch 2',
                description: 'Test batch 2 description',
                quantity: 200,
                produced_at: now()->subDays(15),
                expired_at: now()->addDays(15),
                processed_at: null,
                is_processed_at_stock: false
            )
        ];

        $expectedResult = [
            'data' => $batches,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BatchFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'quantity' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 10;
                }),
                false, // with_product
                false  // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $batch) {
            $this->assertInstanceOf(Batch::class, $batch);
            $this->assertEquals($this->organization->id, $batch->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BatchFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                }),
                false,
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'product_id' => 5,
            'shop_id' => 3,
            'batch_number' => 'ALPHA',
            'name' => 'Test',
            'quantity_min' => 50,
            'quantity_max' => 500,
            'is_processed_at_stock' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof BatchFilters &&
                           isset($filters->filters['product_id']) &&
                           isset($filters->filters['shop_id']) &&
                           isset($filters->filters['batch_number']) &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['quantity_min']) &&
                           isset($filters->filters['quantity_max']) &&
                           isset($filters->filters['is_processed_at_stock']);
                }),
                Mockery::any(),
                false,
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'batch_number',
            'by' => 'asc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'batch_number' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 50;
                }),
                false,
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_product_relationship()
    {
        $request = new Request([
            'with_product' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true,  // with_product
                false  // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_shop_relationship()
    {
        $request = new Request([
            'with_shop' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                false, // with_product
                true   // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_both_relationships()
    {
        $request = new Request([
            'with_product' => true,
            'with_shop' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true, // with_product
                true  // with_shop
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $batches = [];
        for ($i = 1; $i <= 5; $i++) {
            $batches[] = new Batch(
                id: $i,
                organization_id: $this->organization->id,
                shop_id: 1,
                product_id: $i,
                batch_number: "BATCH-$i",
                name: "Batch $i",
                description: "Batch $i description",
                quantity: $i * 50,
                produced_at: now()->subDays($i * 2),
                expired_at: now()->addDays($i * 5),
                processed_at: null,
                is_processed_at_stock: false
            );
        }

        $expectedResult = [
            'data' => $batches,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                }),
                false,
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any(),
                false,
                false
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }
}
