<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private BatchRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BatchRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_batch_successfully()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'DELETE-001',
            name: 'Delete Batch',
            description: 'Batch to be deleted',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andReturn(true);

        $result = $this->useCase->perform($batchId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_batch_belongs_to_different_organization()
    {
        $batchId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            product_id: 1,
            batch_number: 'OTHER-001',
            name: 'Other Batch',
            description: 'Batch from other organization',
            quantity: 50,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This batch don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($batchId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $batchId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($batchId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ERROR-001',
            name: 'Error Batch',
            description: 'Batch that will cause delete error',
            quantity: 75,
            produced_at: now()->subDays(7),
            expired_at: now()->addDays(23),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($batchId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'FAIL-001',
            name: 'Fail Batch',
            description: 'Batch that will fail to delete',
            quantity: 25,
            produced_at: now()->subDays(3),
            expired_at: now()->addDays(27),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andReturn(false);

        $result = $this->useCase->perform($batchId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $batchId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id, // Batch belongs to original org
            shop_id: 1,
            product_id: 1,
            batch_number: 'AUTH-001',
            name: 'Auth Test Batch',
            description: 'Batch for auth test',
            quantity: 60,
            produced_at: now()->subDays(8),
            expired_at: now()->addDays(22),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        // Should throw exception because batch belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This batch don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($batchId);
    }

    public function test_perform_with_processed_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'PROCESSED-001',
            name: 'Processed Batch',
            description: 'Batch that is already processed',
            quantity: 200,
            produced_at: now()->subDays(20),
            expired_at: now()->addDays(10),
            processed_at: now()->subDays(5),
            is_processed_at_stock: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andReturn(true);

        $result = $this->useCase->perform($batchId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_quantity_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andReturn(true);

        $result = $this->useCase->perform($batchId);

        $this->assertTrue($result);
    }

    public function test_perform_with_expired_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'EXPIRED-001',
            name: 'Expired Batch',
            description: 'Batch that is expired',
            quantity: 150,
            produced_at: now()->subDays(60),
            expired_at: now()->subDays(10), // Expired
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($batch)
            ->andReturn(true);

        $result = $this->useCase->perform($batchId);

        $this->assertTrue($result);
    }
}
