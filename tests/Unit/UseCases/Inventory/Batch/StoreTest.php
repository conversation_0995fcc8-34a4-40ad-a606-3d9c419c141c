<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Factories\Inventory\BatchFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private BatchRepository $mockRepository;
    private BatchFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BatchRepository::class);
        $this->mockFactory = Mockery::mock(BatchFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_batch_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 1,
            product_id: 1,
            batch_number: 'STORE-001',
            name: 'Store Batch',
            description: 'Store batch description',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $storedBatch = new Batch(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'STORE-001',
            name: 'Store Batch',
            description: 'Store batch description',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($batch) {
                return $batch instanceof Batch &&
                       $batch->organization_id === $this->organization->id &&
                       $batch->batch_number === 'STORE-001';
            }))
            ->andReturn($storedBatch);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('STORE-001', $result->batch_number);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ORG-001',
            name: 'Organization Batch',
            description: 'Batch for organization test',
            quantity: 50,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $storedBatch = new Batch(
            id: 2,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ORG-001',
            name: 'Organization Batch',
            description: 'Batch for organization test',
            quantity: 50,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($batch) {
                return $batch->organization_id === $this->organization->id;
            }))
            ->andReturn($storedBatch);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ERROR-001',
            name: 'Error Batch',
            description: 'Batch that will cause error',
            quantity: 75,
            produced_at: now()->subDays(7),
            expired_at: now()->addDays(23),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_batch_data()
    {
        $request = $this->createMockMinimalStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: null,
            product_id: 1,
            batch_number: 'MIN-001',
            name: 'Minimal Batch',
            description: null,
            quantity: 25,
            produced_at: null,
            expired_at: null,
            processed_at: null,
            is_processed_at_stock: false
        );

        $storedBatch = new Batch(
            id: 3,
            organization_id: $this->organization->id,
            shop_id: null,
            product_id: 1,
            batch_number: 'MIN-001',
            name: 'Minimal Batch',
            description: null,
            quantity: 25,
            produced_at: null,
            expired_at: null,
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBatch);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals('MIN-001', $result->batch_number);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->description);
    }

    public function test_perform_with_zero_quantity_batch()
    {
        $request = $this->createMockZeroQuantityStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now(),
            expired_at: now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $storedBatch = new Batch(
            id: 4,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now(),
            expired_at: now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBatch);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals(0, $result->quantity);
    }

    public function test_perform_with_high_quantity_batch()
    {
        $request = $this->createMockHighQuantityStoreRequest();
        
        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: now(),
            expired_at: now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $storedBatch = new Batch(
            id: 5,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: now(),
            expired_at: now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedBatch);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals(999999, $result->quantity);
    }

    private function createMockStoreRequest()
    {
        return new class {
            public $shop_id = 1;
            public $product_id = 1;
            public $batch_number = 'STORE-001';
            public $name = 'Store Batch';
            public $description = 'Store batch description';
            public $quantity = 100;
            public $produced_at = '2024-01-01';
            public $expired_at = '2024-12-31';
            public $processed_at = null;
            public $is_processed_at_stock = false;
        };
    }

    private function createMockMinimalStoreRequest()
    {
        return new class {
            public $shop_id = null;
            public $product_id = 1;
            public $batch_number = 'MIN-001';
            public $name = 'Minimal Batch';
            public $description = null;
            public $quantity = 25;
            public $produced_at = null;
            public $expired_at = null;
            public $processed_at = null;
            public $is_processed_at_stock = false;
        };
    }

    private function createMockZeroQuantityStoreRequest()
    {
        return new class {
            public $shop_id = 1;
            public $product_id = 1;
            public $batch_number = 'ZERO-001';
            public $name = 'Zero Quantity Batch';
            public $description = 'Batch with zero quantity';
            public $quantity = 0;
            public $produced_at = '2024-08-01';
            public $expired_at = '2024-09-01';
            public $processed_at = null;
            public $is_processed_at_stock = false;
        };
    }

    private function createMockHighQuantityStoreRequest()
    {
        return new class {
            public $shop_id = 1;
            public $product_id = 1;
            public $batch_number = 'HIGH-001';
            public $name = 'High Quantity Batch';
            public $description = 'Batch with high quantity';
            public $quantity = 999999;
            public $produced_at = '2024-08-01';
            public $expired_at = '2024-09-01';
            public $processed_at = null;
            public $is_processed_at_stock = false;
        };
    }
}
