<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\AddBatchToStock;
use App\UseCases\Inventory\Stock\CreateStockFromEntry;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class AddBatchToStockTest extends TestCase
{
    use RefreshDatabase;

    private AddBatchToStock $useCase;
    private BatchRepository $mockBatchRepository;
    private CreateStockFromEntry $mockCreateStockFromEntry;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockBatchRepository = Mockery::mock(BatchRepository::class);
        $this->mockCreateStockFromEntry = Mockery::mock(CreateStockFromEntry::class);

        $this->useCase = new AddBatchToStock(
            $this->mockBatchRepository,
            $this->mockCreateStockFromEntry
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_adds_batch_to_stock_successfully()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'STOCK-001',
            name: 'Stock Batch',
            description: 'Batch to add to stock',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $processedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'STOCK-001',
            name: 'Stock Batch',
            description: 'Batch to add to stock',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->product_id === 1 &&
                       $entry->shop_id === 1 &&
                       $entry->quantity === 100;
            }))
            ->andReturn(true);

        $this->mockBatchRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($batchToUpdate) {
                return $batchToUpdate instanceof Batch &&
                       $batchToUpdate->is_processed_at_stock === true &&
                       $batchToUpdate->processed_at !== null;
            }), $this->organization->id)
            ->andReturn($processedBatch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertNotNull($result->processed_at);
    }

    public function test_perform_throws_exception_when_batch_belongs_to_different_organization()
    {
        $batchId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            product_id: 1,
            batch_number: 'OTHER-001',
            name: 'Other Batch',
            description: 'Batch from other organization',
            quantity: 50,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldNotReceive('perform');

        $this->mockBatchRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This batch don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($batchId);
    }

    public function test_perform_throws_exception_when_batch_already_processed()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'PROCESSED-001',
            name: 'Already Processed Batch',
            description: 'Batch already processed',
            quantity: 75,
            produced_at: now()->subDays(15),
            expired_at: now()->addDays(15),
            processed_at: now()->subDays(5),
            is_processed_at_stock: true // Already processed
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldNotReceive('perform');

        $this->mockBatchRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('This batch is already processed at stock.');
        $this->expectExceptionCode(400);

        $this->useCase->perform($batchId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $batchId = 999;

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($batchId);
    }

    public function test_perform_handles_create_stock_exception()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ERROR-001',
            name: 'Error Batch',
            description: 'Batch that will cause stock error',
            quantity: 125,
            produced_at: now()->subDays(8),
            expired_at: now()->addDays(22),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->andThrow(new Exception('Stock creation error'));

        $this->mockBatchRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Stock creation error');

        $this->useCase->perform($batchId);
    }

    public function test_perform_handles_batch_update_exception()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'UPDATE-ERROR-001',
            name: 'Update Error Batch',
            description: 'Batch that will cause update error',
            quantity: 150,
            produced_at: now()->subDays(12),
            expired_at: now()->addDays(18),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn(true);

        $this->mockBatchRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Batch update error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Batch update error');

        $this->useCase->perform($batchId);
    }

    public function test_perform_with_zero_quantity_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $processedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->quantity === 0;
            }))
            ->andReturn(true);

        $this->mockBatchRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($processedBatch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertEquals(0, $result->quantity);
    }

    public function test_perform_with_high_quantity_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: now()->subDays(20),
            expired_at: now()->addDays(10),
            processed_at: null,
            is_processed_at_stock: false
        );

        $processedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: now()->subDays(20),
            expired_at: now()->addDays(10),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->quantity === 999999;
            }))
            ->andReturn(true);

        $this->mockBatchRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($processedBatch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertEquals(999999, $result->quantity);
    }

    public function test_perform_with_batch_without_shop()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: null, // No shop
            product_id: 1,
            batch_number: 'NO-SHOP-001',
            name: 'No Shop Batch',
            description: 'Batch without shop',
            quantity: 80,
            produced_at: now()->subDays(6),
            expired_at: now()->addDays(24),
            processed_at: null,
            is_processed_at_stock: false
        );

        $processedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: null,
            product_id: 1,
            batch_number: 'NO-SHOP-001',
            name: 'No Shop Batch',
            description: 'Batch without shop',
            quantity: 80,
            produced_at: now()->subDays(6),
            expired_at: now()->addDays(24),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockBatchRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $this->mockCreateStockFromEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->shop_id === null;
            }))
            ->andReturn(true);

        $this->mockBatchRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($processedBatch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertNull($result->shop_id);
    }
}
