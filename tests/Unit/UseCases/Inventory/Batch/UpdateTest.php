<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Factories\Inventory\BatchFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private BatchRepository $mockRepository;
    private BatchFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BatchRepository::class);
        $this->mockFactory = Mockery::mock(BatchFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_batch_successfully()
    {
        $batchId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'OLD-001',
            name: 'Old Batch',
            description: 'Old description',
            quantity: 50,
            produced_at: now()->subDays(20),
            expired_at: now()->addDays(10),
            processed_at: null,
            is_processed_at_stock: false
        );

        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 2,
            product_id: 2,
            batch_number: 'NEW-001',
            name: 'Updated Batch',
            description: 'Updated description',
            quantity: 150,
            produced_at: now()->subDays(15),
            expired_at: now()->addDays(15),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $updatedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1, // Should keep original shop_id
            product_id: 1, // Should keep original product_id
            batch_number: 'NEW-001',
            name: 'Updated Batch',
            description: 'Updated description',
            quantity: 150,
            produced_at: now()->subDays(15),
            expired_at: now()->addDays(15),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($existingBatch);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($batch) use ($batchId) {
                return $batch instanceof Batch &&
                       $batch->id === $batchId &&
                       $batch->organization_id === $this->organization->id &&
                       $batch->batch_number === 'NEW-001' &&
                       $batch->shop_id === 1 && // Should preserve original
                       $batch->product_id === 1; // Should preserve original
            }), $this->organization->id)
            ->andReturn($updatedBatch);

        $result = $this->useCase->perform($batchId, $request);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals($batchId, $result->id);
        $this->assertEquals('NEW-001', $result->batch_number);
        $this->assertEquals('Updated Batch', $result->name);
        $this->assertEquals(150, $result->quantity);
        $this->assertTrue($result->is_processed_at_stock);
    }

    public function test_perform_throws_exception_when_batch_belongs_to_different_organization()
    {
        $batchId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingBatch = new Batch(
            id: $batchId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            product_id: 1,
            batch_number: 'OTHER-001',
            name: 'Other Batch',
            description: 'Other description',
            quantity: 75,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($existingBatch);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This batch don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($batchId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $batchId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($batchId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $batchId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'EXIST-001',
            name: 'Existing Batch',
            description: 'Existing description',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($existingBatch);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($batchId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $batchId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'EXIST-001',
            name: 'Existing Batch',
            description: 'Existing description',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 2,
            product_id: 2,
            batch_number: 'UPDATE-001',
            name: 'Updated Batch',
            description: 'Updated description',
            quantity: 200,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($existingBatch);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($batchId, $request);
    }

    public function test_perform_preserves_original_shop_and_product_ids()
    {
        $batchId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 5, // Original shop
            product_id: 10, // Original product
            batch_number: 'PRESERVE-001',
            name: 'Preserve Batch',
            description: 'Preserve description',
            quantity: 75,
            produced_at: now()->subDays(15),
            expired_at: now()->addDays(15),
            processed_at: null,
            is_processed_at_stock: false
        );

        $domainFromRequest = new Batch(
            id: null,
            organization_id: null,
            shop_id: 99, // Different shop in request
            product_id: 99, // Different product in request
            batch_number: 'PRESERVE-UPDATED',
            name: 'Preserve Updated Batch',
            description: 'Preserve updated description',
            quantity: 125,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $updatedBatch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 5, // Should preserve original
            product_id: 10, // Should preserve original
            batch_number: 'PRESERVE-UPDATED',
            name: 'Preserve Updated Batch',
            description: 'Preserve updated description',
            quantity: 125,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: now(),
            is_processed_at_stock: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($existingBatch);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($batch) {
                return $batch->shop_id === 5 && $batch->product_id === 10;
            }), $this->organization->id)
            ->andReturn($updatedBatch);

        $result = $this->useCase->perform($batchId, $request);

        $this->assertEquals(5, $result->shop_id);
        $this->assertEquals(10, $result->product_id);
    }

    private function createMockUpdateRequest()
    {
        return new class {
            public $shop_id = 2;
            public $product_id = 2;
            public $batch_number = 'NEW-001';
            public $name = 'Updated Batch';
            public $description = 'Updated description';
            public $quantity = 150;
            public $produced_at = '2024-02-01';
            public $expired_at = '2024-11-30';
            public $processed_at = '2024-08-01';
            public $is_processed_at_stock = true;
        };
    }
}
