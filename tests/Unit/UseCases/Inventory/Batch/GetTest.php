<?php

namespace Tests\Unit\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\Batch\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private BatchRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BatchRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_batch_successfully()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'GET-001',
            name: 'Get Batch',
            description: 'Batch for get test',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals($batchId, $result->id);
        $this->assertEquals('GET-001', $result->batch_number);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $batchId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($batchId);
    }

    public function test_perform_with_minimal_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: null,
            product_id: 1,
            batch_number: 'MIN-001',
            name: 'Minimal Batch',
            description: null,
            quantity: 50,
            produced_at: null,
            expired_at: null,
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals('MIN-001', $result->batch_number);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->description);
        $this->assertNull($result->produced_at);
        $this->assertNull($result->expired_at);
        $this->assertNull($result->processed_at);
        $this->assertFalse($result->is_processed_at_stock);
    }

    public function test_perform_with_complete_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 5,
            product_id: 10,
            batch_number: 'COMPLETE-001',
            name: 'Complete Batch',
            description: 'Complete batch with all fields',
            quantity: 250,
            produced_at: now()->subDays(30),
            expired_at: now()->addDays(30),
            processed_at: now()->subDays(5),
            is_processed_at_stock: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals($batchId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(5, $result->shop_id);
        $this->assertEquals(10, $result->product_id);
        $this->assertEquals('COMPLETE-001', $result->batch_number);
        $this->assertEquals('Complete Batch', $result->name);
        $this->assertEquals('Complete batch with all fields', $result->description);
        $this->assertEquals(250, $result->quantity);
        $this->assertNotNull($result->produced_at);
        $this->assertNotNull($result->expired_at);
        $this->assertNotNull($result->processed_at);
        $this->assertTrue($result->is_processed_at_stock);
    }

    public function test_perform_with_zero_quantity_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Quantity Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: now()->subDays(5),
            expired_at: now()->addDays(25),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals(0, $result->quantity);
    }

    public function test_perform_with_high_quantity_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: now()->subDays(15),
            expired_at: now()->addDays(15),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals(999999, $result->quantity);
    }

    public function test_perform_with_processed_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'PROCESSED-001',
            name: 'Processed Batch',
            description: 'Batch that is processed',
            quantity: 150,
            produced_at: now()->subDays(20),
            expired_at: now()->addDays(10),
            processed_at: now()->subDays(5),
            is_processed_at_stock: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertNotNull($result->processed_at);
    }

    public function test_perform_with_expired_batch()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 1,
            product_id: 1,
            batch_number: 'EXPIRED-001',
            name: 'Expired Batch',
            description: 'Batch that is expired',
            quantity: 75,
            produced_at: now()->subDays(60),
            expired_at: now()->subDays(10), // Expired
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertLessThan(now(), $result->expired_at);
    }

    public function test_perform_returns_batch_with_all_properties()
    {
        $batchId = 1;
        
        $batch = new Batch(
            id: $batchId,
            organization_id: $this->organization->id,
            shop_id: 3,
            product_id: 7,
            batch_number: 'PROPS-001',
            name: 'Properties Batch',
            description: 'Batch with all properties set',
            quantity: 300,
            produced_at: now()->subDays(25),
            expired_at: now()->addDays(35),
            processed_at: now()->subDays(3),
            is_processed_at_stock: true,
            created_at: now()->subDays(30),
            updated_at: now()->subDays(1)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($batchId)
            ->andReturn($batch);

        $result = $this->useCase->perform($batchId);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals($batchId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(3, $result->shop_id);
        $this->assertEquals(7, $result->product_id);
        $this->assertEquals('PROPS-001', $result->batch_number);
        $this->assertEquals('Properties Batch', $result->name);
        $this->assertEquals('Batch with all properties set', $result->description);
        $this->assertEquals(300, $result->quantity);
        $this->assertNotNull($result->produced_at);
        $this->assertNotNull($result->expired_at);
        $this->assertNotNull($result->processed_at);
        $this->assertTrue($result->is_processed_at_stock);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_batch_ids()
    {
        // Test with different batch IDs to ensure correct ID handling
        $batchIds = [1, 42, 999, 12345];

        foreach ($batchIds as $batchId) {
            $batch = new Batch(
                id: $batchId,
                organization_id: $this->organization->id,
                shop_id: 1,
                product_id: 1,
                batch_number: "BATCH-$batchId",
                name: "Batch $batchId",
                description: "Test batch $batchId",
                quantity: $batchId * 10,
                produced_at: now()->subDays($batchId),
                expired_at: now()->addDays($batchId),
                processed_at: null,
                is_processed_at_stock: false
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($batchId)
                ->andReturn($batch);

            $result = $this->useCase->perform($batchId);

            $this->assertInstanceOf(Batch::class, $result);
            $this->assertEquals($batchId, $result->id);
            $this->assertEquals("BATCH-$batchId", $result->batch_number);
            $this->assertEquals("Batch $batchId", $result->name);
            $this->assertEquals($batchId * 10, $result->quantity);
        }
    }
}
