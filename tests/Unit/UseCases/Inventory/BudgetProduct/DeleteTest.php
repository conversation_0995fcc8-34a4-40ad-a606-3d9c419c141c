<?php

namespace Tests\Unit\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetProductRepository;
use App\UseCases\Inventory\BudgetProduct\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private BudgetProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetProductRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_budget_product_successfully()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budgetProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_budget_product_belongs_to_different_organization()
    {
        $budgetProductId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $otherOrganization->id, // Different organization
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This budget product don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($budgetProductId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $budgetProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($budgetProductId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budgetProduct)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($budgetProductId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budgetProduct)
            ->andReturn(false);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $budgetProductId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id, // Budget product belongs to original org
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        // Should throw exception because budget product belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This budget product don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($budgetProductId);
    }

    public function test_perform_with_different_budget_product_types()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $budgetProductId = $index + 1;
            
            $budgetProduct = new BudgetProduct(
                id: $budgetProductId,
                organization_id: $this->organization->id,
                budget_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($budgetProductId)
                ->andReturn($budgetProduct);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($budgetProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($budgetProductId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_zero_quantity_budget_product()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budgetProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_budget_product()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budgetProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertTrue($result);
    }
}
