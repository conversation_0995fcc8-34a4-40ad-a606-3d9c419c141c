<?php

namespace Tests\Unit\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Factories\Inventory\BudgetProductFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetProductRepository;
use App\UseCases\Inventory\BudgetProduct\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private BudgetProductRepository $mockRepository;
    private BudgetProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetProductRepository::class);
        $this->mockFactory = Mockery::mock(BudgetProductFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_budget_product_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $storedBudgetProduct = new BudgetProduct(
            id: 1,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedBudgetProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
    }

    public function test_perform_with_different_quantities()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 50,
            value: 750.25
        );

        $storedBudgetProduct = new BudgetProduct(
            id: 2,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 50,
            value: 750.25
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedBudgetProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(50, $result->quantity);
        $this->assertEquals(750.25, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $storedBudgetProduct = new BudgetProduct(
            id: 3,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedBudgetProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $storedBudgetProduct = new BudgetProduct(
            id: 4,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedBudgetProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_decimal_values()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new BudgetProduct(
            id: null,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789
        );

        $storedBudgetProduct = new BudgetProduct(
            id: 5,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedBudgetProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\BudgetProduct\StoreRequest {
            public $organization_id = 1;
            public $budget_id = 1;
            public $product_id = 1;
            public $quantity = 10;
            public $value = 150.50;
        };
    }
}
