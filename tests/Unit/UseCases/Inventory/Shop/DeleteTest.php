<?php

namespace Tests\Unit\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ShopRepository;
use App\UseCases\Inventory\Shop\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ShopRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ShopRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_shop_successfully()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Delete Shop',
            description: 'Shop to be deleted',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_shop_belongs_to_different_organization()
    {
        $shopId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $otherOrganization->id, // Different organization
            name: 'Other Shop',
            description: 'Shop from other organization',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This shop don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($shopId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $shopId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($shopId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Error Shop',
            description: 'Shop that will cause delete error',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($shopId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Fail Shop',
            description: 'Shop that will fail to delete',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(false);

        $result = $this->useCase->perform($shopId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $shopId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id, // Shop belongs to original org
            name: 'Auth Test Shop',
            description: 'Shop for auth test',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        // Should throw exception because shop belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This shop don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($shopId);
    }

    public function test_perform_with_shop_with_special_characters()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }

    public function test_perform_with_shop_with_unicode_characters()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }

    public function test_perform_with_inactive_shop()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }

    public function test_perform_with_shop_with_empty_description()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'No Description Shop',
            description: '',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }

    public function test_perform_with_shop_with_long_name()
    {
        $shopId = 1;
        $longName = str_repeat('A', 255);
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: $longName,
            description: 'Shop with long name',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($shop)
            ->andReturn(true);

        $result = $this->useCase->perform($shopId);

        $this->assertTrue($result);
    }
}
