<?php

namespace Tests\Unit\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ShopRepository;
use App\UseCases\Inventory\Shop\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ShopRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ShopRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_shop_successfully()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Get Shop',
            description: 'Shop for get test',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($shopId, $result->id);
        $this->assertEquals('Get Shop', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $shopId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($shopId);
    }

    public function test_perform_with_minimal_shop()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Minimal Shop',
            description: null,
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Minimal Shop', $result->name);
        $this->assertNull($result->description);
    }

    public function test_perform_with_complete_shop()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Complete Shop',
            description: 'Complete shop with all fields',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($shopId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Complete Shop', $result->name);
        $this->assertEquals('Complete shop with all fields', $result->description);
        $this->assertTrue($result->is_active);
    }

    public function test_perform_with_inactive_shop()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Inactive Shop', $result->name);
        $this->assertFalse($result->is_active);
    }

    public function test_perform_with_special_characters()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Shop & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Shöp Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    public function test_perform_with_long_name_and_description()
    {
        $shopId = 1;
        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: $longName,
            description: $longDescription,
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($longName, $result->name);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_returns_shop_with_all_properties()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Properties Shop',
            description: 'Shop with all properties set',
            is_active: true,
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($shopId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Properties Shop', $result->name);
        $this->assertEquals('Shop with all properties set', $result->description);
        $this->assertTrue($result->is_active);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_shop_ids()
    {
        // Test with different shop IDs to ensure correct ID handling
        $shopIds = [1, 42, 999, 12345];

        foreach ($shopIds as $shopId) {
            $shop = new Shop(
                id: $shopId,
                organization_id: $this->organization->id,
                name: "Shop $shopId",
                description: "Test shop $shopId",
                is_active: true
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($shopId)
                ->andReturn($shop);

            $result = $this->useCase->perform($shopId);

            $this->assertInstanceOf(Shop::class, $result);
            $this->assertEquals($shopId, $result->id);
            $this->assertEquals("Shop $shopId", $result->name);
            $this->assertEquals("Test shop $shopId", $result->description);
        }
    }

    public function test_perform_with_different_organization_ids()
    {
        $shopId = 1;
        $organizationIds = [1, 100, 999, 12345];

        foreach ($organizationIds as $orgId) {
            $shop = new Shop(
                id: $shopId,
                organization_id: $orgId,
                name: "Shop for Org $orgId",
                description: "Shop for organization $orgId",
                is_active: true
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($shopId)
                ->andReturn($shop);

            $result = $this->useCase->perform($shopId);

            $this->assertInstanceOf(Shop::class, $result);
            $this->assertEquals($orgId, $result->organization_id);
            $this->assertEquals("Shop for Org $orgId", $result->name);
        }
    }

    public function test_perform_with_empty_description()
    {
        $shopId = 1;
        
        $shop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'No Description Shop',
            description: '',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($shop);

        $result = $this->useCase->perform($shopId);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('No Description Shop', $result->name);
        $this->assertEquals('', $result->description);
    }
}
