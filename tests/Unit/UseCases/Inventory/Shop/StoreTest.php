<?php

namespace Tests\Unit\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ShopFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ShopRepository;
use App\UseCases\Inventory\Shop\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ShopRepository $mockRepository;
    private ShopFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ShopRepository::class);
        $this->mockFactory = Mockery::mock(ShopFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_shop_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Store Shop',
            description: 'Store shop description',
            is_active: true
        );

        $storedShop = new Shop(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Store Shop',
            description: 'Store shop description',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($shop) {
                return $shop instanceof Shop &&
                       $shop->organization_id === $this->organization->id &&
                       $shop->name === 'Store Shop';
            }))
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Store Shop', $result->name);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Organization Shop',
            description: 'Shop for organization test',
            is_active: true
        );

        $storedShop = new Shop(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Organization Shop',
            description: 'Shop for organization test',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($shop) {
                return $shop->organization_id === $this->organization->id;
            }))
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Error Shop',
            description: 'Shop that will cause error',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_shop_data()
    {
        $request = $this->createMockMinimalStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Minimal Shop',
            description: null,
            is_active: true
        );

        $storedShop = new Shop(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Minimal Shop',
            description: null,
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Minimal Shop', $result->name);
        $this->assertNull($result->description);
    }

    public function test_perform_with_inactive_shop()
    {
        $request = $this->createMockInactiveStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $storedShop = new Shop(
            id: 4,
            organization_id: $this->organization->id,
            name: 'Inactive Shop',
            description: 'This shop is inactive',
            is_active: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Inactive Shop', $result->name);
        $this->assertFalse($result->is_active);
    }

    public function test_perform_with_special_characters()
    {
        $request = $this->createMockSpecialCharactersStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $storedShop = new Shop(
            id: 5,
            organization_id: $this->organization->id,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Shop & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $request = $this->createMockUnicodeStoreRequest();
        
        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $storedShop = new Shop(
            id: 6,
            organization_id: $this->organization->id,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals('Shöp Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    public function test_perform_with_long_name_and_description()
    {
        $request = $this->createMockLongDataStoreRequest();

        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);

        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: $longName,
            description: $longDescription,
            is_active: true
        );

        $storedShop = new Shop(
            id: 7,
            organization_id: $this->organization->id,
            name: $longName,
            description: $longDescription,
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedShop);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($longName, $result->name);
        $this->assertEquals($longDescription, $result->description);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name = 'Store Shop';
            public $description = 'Store shop description';
            public $is_active = true;
        };
    }

    private function createMockMinimalStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name = 'Minimal Shop';
            public $description = null;
            public $is_active = true;
        };
    }

    private function createMockInactiveStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name = 'Inactive Shop';
            public $description = 'This shop is inactive';
            public $is_active = false;
        };
    }

    private function createMockSpecialCharactersStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name = 'Shop & Co. (™)';
            public $description = 'Description with special chars: @#$%^&*()';
            public $is_active = true;
        };
    }

    private function createMockUnicodeStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name = 'Shöp Ñamé 中文';
            public $description = 'Descripción con caracteres especiales: ñáéíóú';
            public $is_active = true;
        };
    }

    private function createMockLongDataStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $name;
            public $description;
            public $is_active = true;

            public function __construct()
            {
                $this->name = str_repeat('A', 255);
                $this->description = str_repeat('B', 1000);
            }
        };
    }
}
