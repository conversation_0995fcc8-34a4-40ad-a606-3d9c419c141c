<?php

namespace Tests\Unit\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Factories\Inventory\ProductFactory;
use App\Http\Requests\Product\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\Product\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ProductRepository $mockRepository;
    private ProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductRepository::class);
        $this->mockFactory = Mockery::mock(ProductFactory::class);

        $this->useCase = new Store(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_product_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($product) {
                return $product instanceof Product && 
                       $product->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('Test Product', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($product) {
                return $product->organization_id === $this->organization->id;
            }))
            ->andReturn($storedProduct);

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_product_data()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: null,
            name: 'Minimal Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Minimal Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals('Minimal Product', $result->name);
        $this->assertNull($result->brand_id);
        $this->assertNull($result->barcode);
        $this->assertNull($result->price);
        $this->assertNull($result->unity);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_zero_price()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Product with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Product with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(0.0, $result->price);
    }

    public function test_perform_with_high_price()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Expensive Product',
            barcode: '1234567890123',
            description: 'Product with high price',
            price: 999999.99,
            unity: 1,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Expensive Product',
            barcode: '1234567890123',
            description: 'Product with high price',
            price: 999999.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(999999.99, $result->price);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $storedProduct = new Product(
            id: 1,
            organization_id: $differentOrg->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($product) use ($differentOrg) {
                return $product->organization_id === $differentOrg->id;
            }))
            ->andReturn($storedProduct);

        $result = $this->useCase->perform($request);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    private function createMockStoreRequest(): StoreRequest
    {
        return Mockery::mock(StoreRequest::class);
    }
}
