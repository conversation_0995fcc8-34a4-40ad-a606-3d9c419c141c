<?php

namespace Tests\Unit\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Factories\Inventory\ProductFactory;
use App\Http\Requests\Product\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\Product\Update;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private ProductRepository $mockRepository;
    private ProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductRepository::class);
        $this->mockFactory = Mockery::mock(ProductFactory::class);

        $this->useCase = new Update(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_product_successfully()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($product) use ($productId) {
                    return $product instanceof Product && 
                           $product->id === $productId &&
                           $product->name === 'Updated Product';
                }),
                $this->organization->id
            )
            ->andReturn($updatedProduct);

        $result = $this->useCase->perform($request, $productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($productId, $result->id);
        $this->assertEquals('Updated Product', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_correct_product_id()
    {
        $productId = 123;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($product) use ($productId) {
                    return $product->id === $productId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedProduct);

        $result = $this->useCase->perform($request, $productId);

        $this->assertEquals($productId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id  // Should use authenticated user's organization
            )
            ->andReturn($updatedProduct);

        $this->useCase->perform($request, $productId);
    }

    public function test_perform_with_null_values()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: null,
            name: 'Updated Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Updated Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedProduct);

        $result = $this->useCase->perform($request, $productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals('Updated Product', $result->name);
        $this->assertNull($result->brand_id);
        $this->assertNull($result->barcode);
        $this->assertNull($result->price);
        $this->assertNull($result->unity);
    }

    public function test_perform_with_zero_price()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Updated with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Updated with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedProduct);

        $result = $this->useCase->perform($request, $productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(0.0, $result->price);
    }

    public function test_perform_handles_repository_exception()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request, $productId);
    }

    public function test_perform_handles_factory_exception()
    {
        $productId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $productId);
    }

    public function test_perform_with_different_organization_user()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $productId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Product(
            id: null,
            organization_id: null,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: null
        );

        $updatedProduct = new Product(
            id: $productId,
            organization_id: $differentOrg->id,
            brand_id: 2,
            name: 'Updated Product',
            barcode: '9876543210987',
            description: 'Updated product description',
            price: 149.99,
            unity: 2,
            last_priced_at: now()
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $differentOrg->id  // Should use the different organization
            )
            ->andReturn($updatedProduct);

        $result = $this->useCase->perform($request, $productId);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    private function createMockUpdateRequest(): UpdateRequest
    {
        return Mockery::mock(UpdateRequest::class);
    }
}
