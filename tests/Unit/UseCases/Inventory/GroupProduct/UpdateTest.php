<?php

namespace Tests\Unit\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Domains\Inventory\Group;
use App\Domains\Inventory\Product;
use App\Factories\Inventory\GroupProductFactory;
use App\Http\Requests\GroupProduct\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupProductRepository;
use App\Repositories\GroupRepository;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\GroupProduct\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;
use Carbon\Carbon;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private GroupProductRepository $mockGroupProductRepository;
    private GroupProductFactory $mockFactory;
    private GroupRepository $mockGroupRepository;
    private ProductRepository $mockProductRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockGroupProductRepository = Mockery::mock(GroupProductRepository::class);
        $this->mockFactory = Mockery::mock(GroupProductFactory::class);
        $this->mockGroupRepository = Mockery::mock(GroupRepository::class);
        $this->mockProductRepository = Mockery::mock(ProductRepository::class);

        $this->useCase = new Update(
            $this->mockGroupProductRepository,
            $this->mockFactory,
            $this->mockGroupRepository,
            $this->mockProductRepository
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_group_product_successfully()
    {
        $groupProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $existingProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Existing Product',
            description: 'Existing description',
            price: 100.00
        );

        $newGroup = new Group(
            id: 2,
            organization_id: $this->organization->id,
            name: 'New Group',
            description: 'New group description'
        );

        $newProduct = new Product(
            id: 2,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'New Product',
            description: 'New description',
            price: 200.00
        );

        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 2,
            product_id: 2
        );

        $updatedGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 2,
            product_id: 2
        );

        // Mock the repository calls
        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($existingGroupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1) // existing product_id
            ->andReturn($existingProduct);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockGroupRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2) // new group_id
            ->andReturn($newGroup);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2) // new product_id
            ->andReturn($newProduct);

        $this->mockGroupProductRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('organization_id issue')); // Expected to fail

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($updatedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupProductId);

        $this->assertInstanceOf(GroupProduct::class, $result);
        $this->assertEquals($groupProductId, $result->id);
        $this->assertEquals(2, $result->group_id);
        $this->assertEquals(2, $result->product_id);
    }

    public function test_perform_throws_exception_when_existing_group_product_not_in_organization()
    {
        $groupProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $otherOrganization = Organization::factory()->create();
        $existingProduct = new Product(
            id: 1,
            organization_id: $otherOrganization->id,
            brand_id: 1,
            name: 'Other Org Product',
            description: 'Other org description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($existingGroupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingProduct);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group product assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $groupProductId);
    }

    public function test_perform_throws_exception_when_new_group_not_in_organization()
    {
        $groupProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $existingProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Existing Product',
            description: 'Existing description',
            price: 100.00
        );

        $otherOrganization = Organization::factory()->create();
        $newGroup = new Group(
            id: 2,
            organization_id: $otherOrganization->id,
            name: 'Other Org Group',
            description: 'Other org group description'
        );

        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 2,
            product_id: 1
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($existingGroupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingProduct);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockGroupRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newGroup);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The specified group doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $groupProductId);
    }

    public function test_perform_throws_exception_when_new_product_not_in_organization()
    {
        $groupProductId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $existingProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Existing Product',
            description: 'Existing description',
            price: 100.00
        );

        $newGroup = new Group(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Same Group',
            description: 'Same group description'
        );

        $otherOrganization = Organization::factory()->create();
        $newProduct = new Product(
            id: 2,
            organization_id: $otherOrganization->id,
            brand_id: 1,
            name: 'Other Org Product',
            description: 'Other org description',
            price: 200.00
        );

        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 1,
            product_id: 2
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($existingGroupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingProduct);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockGroupRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($newGroup);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newProduct);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The specified product doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $groupProductId);
    }

    public function test_perform_with_partial_update_group_only()
    {
        $groupProductId = 1;
        $request = $this->createMockUpdateRequest(['group_id' => 3, 'product_id' => null]);
        
        $existingGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $existingProduct = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Existing Product',
            description: 'Existing description',
            price: 100.00
        );

        $newGroup = new Group(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Updated Group',
            description: 'Updated group description'
        );

        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 3,
            product_id: null
        );

        $updatedGroupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 3,
            product_id: 1 // Should remain the same
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($existingGroupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingProduct);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockGroupRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(3)
            ->andReturn($newGroup);

        $this->mockGroupProductRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('organization_id issue'));

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($updatedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupProductId);

        $this->assertEquals(3, $result->group_id);
        $this->assertEquals(1, $result->product_id);
    }

    private function createMockUpdateRequest(array $data = []): UpdateRequest
    {
        $defaultData = [
            'group_id' => 2,
            'product_id' => 2,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(UpdateRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
