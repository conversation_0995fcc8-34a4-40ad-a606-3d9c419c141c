<?php

namespace Tests\Unit\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Factories\Inventory\GroupProductFactory;
use App\Http\Requests\GroupProduct\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupProductRepository;
use App\UseCases\Inventory\GroupProduct\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private GroupProductRepository $mockRepository;
    private GroupProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupProductRepository::class);
        $this->mockFactory = Mockery::mock(GroupProductFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_group_product_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 1,
            product_id: 1
        );

        $storedGroupProduct = new GroupProduct(
            id: 1,
            group_id: 1,
            product_id: 1
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                return $groupProduct instanceof GroupProduct &&
                       $groupProduct->group_id === 1 &&
                       $groupProduct->product_id === 1;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(GroupProduct::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(1, $result->group_id);
        $this->assertEquals(1, $result->product_id);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 2,
            product_id: 3
        );

        $storedGroupProduct = new GroupProduct(
            id: 2,
            group_id: 2,
            product_id: 3
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                // Note: GroupProduct doesn't have organization_id, but the UseCase sets it
                return $groupProduct instanceof GroupProduct;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals(2, $result->group_id);
        $this->assertEquals(3, $result->product_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 5,
            product_id: 10
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_null_group_id()
    {
        $request = $this->createMockStoreRequest(['group_id' => null]);
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: null,
            product_id: 1
        );

        $storedGroupProduct = new GroupProduct(
            id: 3,
            group_id: null,
            product_id: 1
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                return $groupProduct->group_id === null;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertNull($result->group_id);
        $this->assertEquals(1, $result->product_id);
    }

    public function test_perform_with_null_product_id()
    {
        $request = $this->createMockStoreRequest(['product_id' => null]);
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 1,
            product_id: null
        );

        $storedGroupProduct = new GroupProduct(
            id: 4,
            group_id: 1,
            product_id: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                return $groupProduct->product_id === null;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals(1, $result->group_id);
        $this->assertNull($result->product_id);
    }

    public function test_perform_with_zero_ids()
    {
        $request = $this->createMockStoreRequest(['group_id' => 0, 'product_id' => 0]);
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 0,
            product_id: 0
        );

        $storedGroupProduct = new GroupProduct(
            id: 5,
            group_id: 0,
            product_id: 0
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                return $groupProduct->group_id === 0 && $groupProduct->product_id === 0;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals(0, $result->group_id);
        $this->assertEquals(0, $result->product_id);
    }

    public function test_perform_with_large_ids()
    {
        $request = $this->createMockStoreRequest(['group_id' => 999999, 'product_id' => 888888]);
        
        $domainFromRequest = new GroupProduct(
            id: null,
            group_id: 999999,
            product_id: 888888
        );

        $storedGroupProduct = new GroupProduct(
            id: 6,
            group_id: 999999,
            product_id: 888888
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($groupProduct) {
                return $groupProduct->group_id === 999999 && $groupProduct->product_id === 888888;
            }))
            ->andReturn($storedGroupProduct);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals(999999, $result->group_id);
        $this->assertEquals(888888, $result->product_id);
    }

    private function createMockStoreRequest(array $data = []): StoreRequest
    {
        $defaultData = [
            'group_id' => 1,
            'product_id' => 1,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(StoreRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
