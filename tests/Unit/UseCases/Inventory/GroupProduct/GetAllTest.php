<?php

namespace Tests\Unit\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupProductRepository;
use App\UseCases\Inventory\GroupProduct\GetAll;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private GroupProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupProductRepository::class);
        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_group_products_from_organization()
    {
        $expectedResult = [
            'data' => [
                new GroupProduct(1, 1, 1),
                new GroupProduct(2, 1, 2),
                new GroupProduct(3, 2, 1),
            ],
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(3, $result['data']);
        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $groupProduct) {
            $this->assertInstanceOf(GroupProduct::class, $groupProduct);
        }
    }

    public function test_perform_with_empty_result()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(0, $result['total']);
    }

    public function test_perform_with_pagination()
    {
        $expectedResult = [
            'data' => [
                new GroupProduct(1, 1, 1),
                new GroupProduct(2, 1, 2),
            ],
            'count' => 2,
            'total' => 5,
            'currentPage' => 1,
            'lastPage' => 3,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(5, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_handles_repository_exception()
    {
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform();
    }

    public function test_perform_uses_authenticated_user_organization_id()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        // Verify that the correct organization ID is passed
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $this->useCase->perform();
    }

    public function test_perform_with_different_organization()
    {
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $expectedResult = [
            'data' => [
                new GroupProduct(1, 5, 10),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($newOrganization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(5, $result['data'][0]->group_id);
        $this->assertEquals(10, $result['data'][0]->product_id);
    }

    public function test_perform_with_large_dataset()
    {
        $groupProducts = [];
        for ($i = 1; $i <= 100; $i++) {
            $groupProducts[] = new GroupProduct($i, ($i % 10) + 1, ($i % 20) + 1);
        }

        $expectedResult = [
            'data' => array_slice($groupProducts, 0, 30), // First 30 items
            'count' => 30,
            'total' => 100,
            'currentPage' => 1,
            'lastPage' => 4,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(30, $result['count']);
        $this->assertEquals(100, $result['total']);
        $this->assertEquals(4, $result['lastPage']);
        $this->assertCount(30, $result['data']);
    }

    public function test_perform_with_various_group_product_combinations()
    {
        $expectedResult = [
            'data' => [
                new GroupProduct(1, 1, 1),    // Group 1, Product 1
                new GroupProduct(2, 1, 2),    // Group 1, Product 2
                new GroupProduct(3, 2, 1),    // Group 2, Product 1
                new GroupProduct(4, 2, 3),    // Group 2, Product 3
                new GroupProduct(5, 3, 2),    // Group 3, Product 2
            ],
            'count' => 5,
            'total' => 5,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(5, $result['count']);
        
        // Verify different combinations
        $this->assertEquals(1, $result['data'][0]->group_id);
        $this->assertEquals(1, $result['data'][0]->product_id);
        
        $this->assertEquals(2, $result['data'][2]->group_id);
        $this->assertEquals(1, $result['data'][2]->product_id);
        
        $this->assertEquals(3, $result['data'][4]->group_id);
        $this->assertEquals(2, $result['data'][4]->product_id);
    }

    public function test_perform_with_null_group_or_product_ids()
    {
        $expectedResult = [
            'data' => [
                new GroupProduct(1, null, 1),    // Null group_id
                new GroupProduct(2, 1, null),    // Null product_id
                new GroupProduct(3, null, null), // Both null
            ],
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify null handling
        $this->assertNull($result['data'][0]->group_id);
        $this->assertEquals(1, $result['data'][0]->product_id);
        
        $this->assertEquals(1, $result['data'][1]->group_id);
        $this->assertNull($result['data'][1]->product_id);
        
        $this->assertNull($result['data'][2]->group_id);
        $this->assertNull($result['data'][2]->product_id);
    }

    public function test_perform_repository_returns_consistent_structure()
    {
        $expectedResult = [
            'data' => [new GroupProduct(1, 1, 1)],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        // Verify the structure matches exactly what repository returns
        $this->assertEquals($expectedResult, $result);
    }
}
