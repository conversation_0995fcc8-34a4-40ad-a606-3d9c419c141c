<?php

namespace Tests\Unit\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Domains\Inventory\Product;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupProductRepository;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\GroupProduct\Get;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private GroupProductRepository $mockGroupProductRepository;
    private ProductRepository $mockProductRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockGroupProductRepository = Mockery::mock(GroupProductRepository::class);
        $this->mockProductRepository = Mockery::mock(ProductRepository::class);
        
        $this->useCase = new Get($this->mockGroupProductRepository, $this->mockProductRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_group_product_when_belongs_to_organization()
    {
        $groupProductId = 1;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            description: 'Test product description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $result = $this->useCase->perform($groupProductId);

        $this->assertInstanceOf(GroupProduct::class, $result);
        $this->assertEquals($groupProductId, $result->id);
        $this->assertEquals(1, $result->group_id);
        $this->assertEquals(1, $result->product_id);
    }

    public function test_perform_throws_exception_when_product_belongs_to_different_organization()
    {
        $groupProductId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: $otherOrganization->id,
            brand_id: 1,
            name: 'Other Org Product',
            description: 'Other org description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group product assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_handles_group_product_not_found()
    {
        $groupProductId = 999;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('GroupProduct not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('GroupProduct not found');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_handles_product_not_found()
    {
        $groupProductId = 1;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 999
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(999)
            ->andThrow(new Exception('Product not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Product not found');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_validates_organization_ownership()
    {
        $groupProductId = 2;
        
        // Create a group product that belongs to the user's organization (through product)
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 5,
            product_id: 10
        );

        $product = new Product(
            id: 10,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Valid Product',
            description: 'Valid product description',
            price: 200.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(10)
            ->andReturn($product);

        $result = $this->useCase->perform($groupProductId);

        // Should not throw exception and return the group product
        $this->assertEquals($this->organization->id, $product->organization_id);
        $this->assertEquals(5, $result->group_id);
        $this->assertEquals(10, $result->product_id);
    }

    public function test_perform_with_null_product_id_throws_exception()
    {
        $groupProductId = 3;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: null
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(null)
            ->andThrow(new Exception('Product ID cannot be null'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Product ID cannot be null');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $groupProductId = 4;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 2,
            product_id: 3
        );

        $product = new Product(
            id: 3,
            organization_id: $newOrganization->id,
            brand_id: 1,
            name: 'New Org Product',
            description: 'New org description',
            price: 300.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(3)
            ->andReturn($product);

        $result = $this->useCase->perform($groupProductId);

        $this->assertEquals($newOrganization->id, $product->organization_id);
        $this->assertEquals(2, $result->group_id);
        $this->assertEquals(3, $result->product_id);
    }

    public function test_perform_with_zero_id()
    {
        $groupProductId = 0;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('Invalid group product ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group product ID');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_with_negative_id()
    {
        $groupProductId = -1;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('Invalid group product ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group product ID');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_with_different_group_and_product_combinations()
    {
        $testCases = [
            ['id' => 1, 'group_id' => 1, 'product_id' => 1],
            ['id' => 2, 'group_id' => 5, 'product_id' => 10],
            ['id' => 3, 'group_id' => 100, 'product_id' => 200],
        ];

        foreach ($testCases as $case) {
            $groupProduct = new GroupProduct(
                id: $case['id'],
                group_id: $case['group_id'],
                product_id: $case['product_id']
            );

            $product = new Product(
                id: $case['product_id'],
                organization_id: $this->organization->id,
                brand_id: 1,
                name: "Product {$case['product_id']}",
                description: "Product {$case['product_id']} description",
                price: $case['product_id'] * 10.0
            );

            $this->mockGroupProductRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($case['id'])
                ->andReturn($groupProduct);

            $this->mockProductRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($case['product_id'])
                ->andReturn($product);

            $result = $this->useCase->perform($case['id']);

            $this->assertEquals($case['id'], $result->id);
            $this->assertEquals($case['group_id'], $result->group_id);
            $this->assertEquals($case['product_id'], $result->product_id);
        }
    }

    public function test_perform_with_product_null_organization_id_throws_exception()
    {
        $groupProductId = 5;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: null,
            brand_id: 1,
            name: 'No Org Product',
            description: 'No org description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group product assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupProductId);
    }
}
