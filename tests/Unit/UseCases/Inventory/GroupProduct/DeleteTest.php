<?php

namespace Tests\Unit\UseCases\Inventory\GroupProduct;

use App\Domains\Inventory\GroupProduct;
use App\Domains\Inventory\Product;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupProductRepository;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\GroupProduct\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private GroupProductRepository $mockGroupProductRepository;
    private ProductRepository $mockProductRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockGroupProductRepository = Mockery::mock(GroupProductRepository::class);
        $this->mockProductRepository = Mockery::mock(ProductRepository::class);
        
        $this->useCase = new Delete($this->mockGroupProductRepository, $this->mockProductRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_group_product_successfully()
    {
        $groupProductId = 1;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Product to Delete',
            description: 'Product to delete description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->mockGroupProductRepository
            ->shouldReceive('delete')
            ->once()
            ->with($groupProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($groupProductId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_product_belongs_to_different_organization()
    {
        $groupProductId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: $otherOrganization->id,
            brand_id: 1,
            name: 'Other Org Product',
            description: 'Other org description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group product assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_handles_fetch_group_product_exception()
    {
        $groupProductId = 999;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('GroupProduct not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('GroupProduct not found');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_handles_fetch_product_exception()
    {
        $groupProductId = 1;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 999
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(999)
            ->andThrow(new Exception('Product not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Product not found');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_handles_delete_exception()
    {
        $groupProductId = 1;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Product with Delete Error',
            description: 'Product with delete error description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->mockGroupProductRepository
            ->shouldReceive('delete')
            ->once()
            ->with($groupProduct)
            ->andThrow(new Exception('Delete operation failed'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Delete operation failed');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $groupProductId = 2;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 2,
            product_id: 2
        );

        $product = new Product(
            id: 2,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Product Delete Fail',
            description: 'Product delete fail description',
            price: 200.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($product);

        $this->mockGroupProductRepository
            ->shouldReceive('delete')
            ->once()
            ->with($groupProduct)
            ->andReturn(false);

        $result = $this->useCase->perform($groupProductId);

        $this->assertFalse($result);
    }

    public function test_perform_validates_organization_ownership_before_delete()
    {
        $groupProductId = 3;
        
        // Create a group product that belongs to the user's organization (through product)
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 5,
            product_id: 10
        );

        $product = new Product(
            id: 10,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Valid Product for Delete',
            description: 'Valid product for delete description',
            price: 300.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(10)
            ->andReturn($product);

        $this->mockGroupProductRepository
            ->shouldReceive('delete')
            ->once()
            ->with($groupProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($groupProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_null_product_id_throws_exception()
    {
        $groupProductId = 4;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: null
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(null)
            ->andThrow(new Exception('Product ID cannot be null'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Product ID cannot be null');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $groupProductId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 3,
            product_id: 6
        );

        $product = new Product(
            id: 6,
            organization_id: $newOrganization->id,
            brand_id: 1,
            name: 'New Org Product',
            description: 'New org description',
            price: 400.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(6)
            ->andReturn($product);

        $this->mockGroupProductRepository
            ->shouldReceive('delete')
            ->once()
            ->with($groupProduct)
            ->andReturn(true);

        $result = $this->useCase->perform($groupProductId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_id()
    {
        $groupProductId = 0;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('Invalid group product ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group product ID');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_with_negative_id()
    {
        $groupProductId = -1;

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andThrow(new Exception('Invalid group product ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group product ID');

        $this->useCase->perform($groupProductId);
    }

    public function test_perform_with_different_group_and_product_combinations()
    {
        $testCases = [
            ['id' => 1, 'group_id' => 1, 'product_id' => 1],
            ['id' => 2, 'group_id' => 5, 'product_id' => 10],
            ['id' => 3, 'group_id' => 100, 'product_id' => 200],
        ];

        foreach ($testCases as $case) {
            $groupProduct = new GroupProduct(
                id: $case['id'],
                group_id: $case['group_id'],
                product_id: $case['product_id']
            );

            $product = new Product(
                id: $case['product_id'],
                organization_id: $this->organization->id,
                brand_id: 1,
                name: "Product {$case['product_id']}",
                description: "Product {$case['product_id']} description",
                price: $case['product_id'] * 10.0
            );

            $this->mockGroupProductRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($case['id'])
                ->andReturn($groupProduct);

            $this->mockProductRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($case['product_id'])
                ->andReturn($product);

            $this->mockGroupProductRepository
                ->shouldReceive('delete')
                ->once()
                ->with($groupProduct)
                ->andReturn(true);

            $result = $this->useCase->perform($case['id']);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_product_null_organization_id_throws_exception()
    {
        $groupProductId = 6;
        $groupProduct = new GroupProduct(
            id: $groupProductId,
            group_id: 1,
            product_id: 1
        );

        $product = new Product(
            id: 1,
            organization_id: null,
            brand_id: 1,
            name: 'No Org Product',
            description: 'No org description',
            price: 100.00
        );

        $this->mockGroupProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupProductId)
            ->andReturn($groupProduct);

        $this->mockProductRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($product);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group product assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupProductId);
    }
}
