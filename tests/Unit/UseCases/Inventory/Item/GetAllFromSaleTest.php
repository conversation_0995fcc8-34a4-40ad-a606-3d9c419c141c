<?php

namespace Tests\Unit\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ItemRepository;
use App\UseCases\Inventory\Item\GetAllFromSale;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllFromSaleTest extends TestCase
{
    use RefreshDatabase;

    private GetAllFromSale $useCase;
    private ItemRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ItemRepository::class);

        $this->useCase = new GetAllFromSale($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_items_from_sale()
    {
        $saleId = 1;
        
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 2,
                quantity: 5,
                value: 75.25
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);

        foreach ($result as $item) {
            $this->assertInstanceOf(Item::class, $item);
            $this->assertEquals($saleId, $item->sale_id);
            $this->assertEquals($this->organization->id, $item->organization_id);
        }
    }

    public function test_perform_with_empty_sale()
    {
        $saleId = 1;

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn([]);

        $result = $this->useCase->perform($saleId);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $saleId = 1;

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($saleId);
    }

    public function test_perform_with_single_item()
    {
        $saleId = 1;
        
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 1,
                quantity: 10,
                value: 150.50
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals($saleId, $result[0]->sale_id);
        $this->assertEquals(10, $result[0]->quantity);
        $this->assertEquals(150.50, $result[0]->value);
    }

    public function test_perform_with_multiple_items()
    {
        $saleId = 1;
        
        $items = [];
        for ($i = 1; $i <= 5; $i++) {
            $items[] = new Item(
                id: $i,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: $i,
                quantity: $i * 10,
                value: $i * 150.50
            );
        }

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertIsArray($result);
        $this->assertCount(5, $result);

        foreach ($result as $index => $item) {
            $this->assertEquals($saleId, $item->sale_id);
            $this->assertEquals(($index + 1) * 10, $item->quantity);
            $this->assertEquals(($index + 1) * 150.50, $item->value);
        }
    }

    public function test_perform_with_different_sale_ids()
    {
        $testSaleIds = [1, 5, 10, 100];

        foreach ($testSaleIds as $saleId) {
            $items = [
                new Item(
                    id: 1,
                    organization_id: $this->organization->id,
                    sale_id: $saleId,
                    product_id: 1,
                    quantity: 10,
                    value: 150.50
                )
            ];

            $this->mockRepository
                ->shouldReceive('fetchFromSale')
                ->once()
                ->with($saleId)
                ->andReturn($items);

            $result = $this->useCase->perform($saleId);

            $this->assertIsArray($result);
            $this->assertCount(1, $result);
            $this->assertEquals($saleId, $result[0]->sale_id);
        }
    }

    public function test_perform_with_various_item_types()
    {
        $saleId = 1;
        
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 1,
                quantity: 0,
                value: 0.0
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 2,
                quantity: 999999,
                value: 99999999.99
            ),
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 3,
                quantity: 33,
                value: 123.456789
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertCount(3, $result);
        
        // Verify different item types
        $this->assertEquals(0, $result[0]->quantity);
        $this->assertEquals(0.0, $result[0]->value);
        
        $this->assertEquals(999999, $result[1]->quantity);
        $this->assertEquals(99999999.99, $result[1]->value);
        
        $this->assertEquals(33, $result[2]->quantity);
        $this->assertEquals(123.456789, $result[2]->value);
    }

    public function test_perform_with_different_products()
    {
        $saleId = 1;
        
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 5,
                quantity: 15,
                value: 225.75
            ),
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 10,
                quantity: 20,
                value: 300.00
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertCount(3, $result);
        
        // Verify items with different products
        $this->assertEquals(1, $result[0]->product_id);
        $this->assertEquals(5, $result[1]->product_id);
        $this->assertEquals(10, $result[2]->product_id);
    }

    public function test_perform_maintains_item_order()
    {
        $saleId = 1;
        
        $items = [
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 3,
                quantity: 30,
                value: 450.00
            ),
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: $saleId,
                product_id: 2,
                quantity: 20,
                value: 300.75
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromSale')
            ->once()
            ->with($saleId)
            ->andReturn($items);

        $result = $this->useCase->perform($saleId);

        $this->assertCount(3, $result);
        
        // Verify order is maintained
        $this->assertEquals(3, $result[0]->id);
        $this->assertEquals(1, $result[1]->id);
        $this->assertEquals(2, $result[2]->id);
    }
}
