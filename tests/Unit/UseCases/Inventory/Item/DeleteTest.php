<?php

namespace Tests\Unit\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ItemRepository;
use App\UseCases\Inventory\Item\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ItemRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ItemRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_item_successfully()
    {
        $itemId = 1;
        
        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($item)
            ->andReturn(true);

        $result = $this->useCase->perform($itemId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_item_belongs_to_different_organization()
    {
        $itemId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $item = new Item(
            id: $itemId,
            organization_id: $otherOrganization->id, // Different organization
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This item don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($itemId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $itemId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($itemId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $itemId = 1;
        
        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($item)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($itemId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $itemId = 1;
        
        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($item)
            ->andReturn(false);

        $result = $this->useCase->perform($itemId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $itemId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id, // Item belongs to original org
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        // Should throw exception because item belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This item don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($itemId);
    }

    public function test_perform_with_different_item_types()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $itemId = $index + 1;
            
            $item = new Item(
                id: $itemId,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($itemId)
                ->andReturn($item);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($item)
                ->andReturn(true);

            $result = $this->useCase->perform($itemId);

            $this->assertTrue($result);
        }
    }

    public function test_perform_with_zero_quantity_item()
    {
        $itemId = 1;
        
        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($item)
            ->andReturn(true);

        $result = $this->useCase->perform($itemId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_item()
    {
        $itemId = 1;
        
        $item = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($item);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($item)
            ->andReturn(true);

        $result = $this->useCase->perform($itemId);

        $this->assertTrue($result);
    }
}
