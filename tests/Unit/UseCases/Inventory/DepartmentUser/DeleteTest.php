<?php

namespace Tests\Unit\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Domains\Inventory\Department;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentUserRepository;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\DepartmentUser\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private DepartmentUserRepository $mockDepartmentUserRepository;
    private DepartmentRepository $mockDepartmentRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockDepartmentUserRepository = Mockery::mock(DepartmentUserRepository::class);
        $this->mockDepartmentRepository = Mockery::mock(DepartmentRepository::class);
        
        $this->useCase = new Delete($this->mockDepartmentUserRepository, $this->mockDepartmentRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_department_user_successfully()
    {
        $departmentUserId = 1;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Department to Delete',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $this->mockDepartmentUserRepository
            ->shouldReceive('delete')
            ->once()
            ->with($departmentUser)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_department_belongs_to_different_organization()
    {
        $departmentUserId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $otherOrganization->id,
            name: 'Other Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department user assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_handles_fetch_department_user_exception()
    {
        $departmentUserId = 999;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Department user not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department user not found');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_handles_fetch_department_exception()
    {
        $departmentUserId = 1;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 999
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(999)
            ->andThrow(new Exception('Department not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department not found');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_handles_delete_exception()
    {
        $departmentUserId = 1;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Department with Delete Error',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $this->mockDepartmentUserRepository
            ->shouldReceive('delete')
            ->once()
            ->with($departmentUser)
            ->andThrow(new Exception('Delete operation failed'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Delete operation failed');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $departmentUserId = 2;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 2,
            department_id: 2
        );

        $department = new Department(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Department Delete Fail',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($department);

        $this->mockDepartmentUserRepository
            ->shouldReceive('delete')
            ->once()
            ->with($departmentUser)
            ->andReturn(false);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertFalse($result);
    }

    public function test_perform_validates_organization_ownership_before_delete()
    {
        $departmentUserId = 3;
        
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 3,
            department_id: 3
        );

        $department = new Department(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Valid Department for Delete',
            is_active: false
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(3)
            ->andReturn($department);

        $this->mockDepartmentUserRepository
            ->shouldReceive('delete')
            ->once()
            ->with($departmentUser)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertTrue($result);
    }

    public function test_perform_with_null_department_organization_id_throws_exception()
    {
        $departmentUserId = 4;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 4,
            department_id: 4
        );

        $department = new Department(
            id: 4,
            organization_id: null,
            name: 'No Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(4)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department user assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $departmentUserId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 5,
            department_id: 5
        );

        $department = new Department(
            id: 5,
            organization_id: $newOrganization->id,
            name: 'New Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(5)
            ->andReturn($department);

        $this->mockDepartmentUserRepository
            ->shouldReceive('delete')
            ->once()
            ->with($departmentUser)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_id()
    {
        $departmentUserId = 0;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Invalid department user ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department user ID');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_with_negative_id()
    {
        $departmentUserId = -1;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Invalid department user ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department user ID');

        $this->useCase->perform($departmentUserId);
    }
}
