<?php

namespace Tests\Unit\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Domains\Inventory\Department;
use App\Domains\User;
use App\Factories\Inventory\DepartmentUserFactory;
use App\Http\Requests\DepartmentUser\UpdateRequest;
use App\Models\Organization;
use App\Models\User as UserModel;
use App\Repositories\DepartmentUserRepository;
use App\Repositories\DepartmentRepository;
use App\Repositories\UserRepository;
use App\UseCases\Inventory\DepartmentUser\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private DepartmentUserRepository $mockDepartmentUserRepository;
    private DepartmentRepository $mockDepartmentRepository;
    private UserRepository $mockUserRepository;
    private DepartmentUserFactory $mockFactory;
    private Organization $organization;
    private UserModel $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockDepartmentUserRepository = Mockery::mock(DepartmentUserRepository::class);
        $this->mockDepartmentRepository = Mockery::mock(DepartmentRepository::class);
        $this->mockUserRepository = Mockery::mock(UserRepository::class);
        $this->mockFactory = Mockery::mock(DepartmentUserFactory::class);

        $this->useCase = new Update(
            $this->mockDepartmentUserRepository,
            $this->mockFactory,
            $this->mockDepartmentRepository,
            $this->mockUserRepository
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_department_user_successfully()
    {
        $departmentUserId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingDepartmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $existingDepartment = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $newUser = new User(
            id: 2,
            profile_id: 1,
            organization_id: $this->organization->id,
            first_name: 'New User',
            last_name: 'Test',
            username: 'newuser',
            email: '<EMAIL>',
            password: 'password',
            cpf: null,
            phone: null
        );

        $newDepartment = new Department(
            id: 2,
            organization_id: $this->organization->id,
            name: 'New Department',
            is_active: true
        );

        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: 2,
            department_id: 2
        );

        $updatedDepartmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 2,
            department_id: 2
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($existingDepartmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingDepartment);

        $this->mockUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newDepartment);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockDepartmentUserRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($departmentUser) use ($departmentUserId) {
                    return $departmentUser instanceof DepartmentUser &&
                           $departmentUser->id === $departmentUserId &&
                           $departmentUser->user_id === 2 &&
                           $departmentUser->department_id === 2;
                }),
                $this->organization->id
            )
            ->andReturn($updatedDepartmentUser);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $departmentUserId);

        $this->assertInstanceOf(DepartmentUser::class, $result);
        $this->assertEquals($departmentUserId, $result->id);
        $this->assertEquals(2, $result->user_id);
        $this->assertEquals(2, $result->department_id);
    }

    public function test_perform_throws_exception_when_existing_department_not_in_organization()
    {
        $departmentUserId = 1;
        $request = $this->createMockUpdateRequest();
        
        $otherOrganization = Organization::factory()->create();
        
        $existingDepartmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $existingDepartment = new Department(
            id: 1,
            organization_id: $otherOrganization->id,
            name: 'Other Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($existingDepartmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingDepartment);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department user assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $departmentUserId);
    }

    public function test_perform_throws_exception_when_new_user_not_in_organization()
    {
        $departmentUserId = 1;
        $request = $this->createMockUpdateRequest(['user_id' => 2]);
        
        $otherOrganization = Organization::factory()->create();
        
        $existingDepartmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $existingDepartment = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $newUser = new User(
            id: 2,
            profile_id: 1,
            organization_id: $otherOrganization->id,
            first_name: 'Other User',
            last_name: 'Test',
            username: 'otheruser',
            email: '<EMAIL>',
            password: 'password',
            cpf: null,
            phone: null
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($existingDepartmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingDepartment);

        $this->mockUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newUser);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The specified user doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $departmentUserId);
    }

    public function test_perform_throws_exception_when_new_department_not_in_organization()
    {
        $departmentUserId = 1;
        $request = $this->createMockUpdateRequest(['department_id' => 2]);
        
        $otherOrganization = Organization::factory()->create();
        
        $existingDepartmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $existingDepartment = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $newDepartment = new Department(
            id: 2,
            organization_id: $otherOrganization->id,
            name: 'Other Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($existingDepartmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($existingDepartment);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($newDepartment);

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The specified department doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($request, $departmentUserId);
    }

    private function createMockUpdateRequest(array $data = []): UpdateRequest
    {
        $defaultData = [
            'user_id' => 2,
            'department_id' => 2,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(UpdateRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
