<?php

namespace Tests\Unit\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Factories\Inventory\DepartmentUserFactory;
use App\Http\Requests\DepartmentUser\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentUserRepository;
use App\UseCases\Inventory\DepartmentUser\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private DepartmentUserRepository $mockRepository;
    private DepartmentUserFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentUserRepository::class);
        $this->mockFactory = Mockery::mock(DepartmentUserFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_department_user_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: 1,
            department_id: 2
        );

        $storedDepartmentUser = new DepartmentUser(
            id: 1,
            user_id: 1,
            department_id: 2
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($departmentUser) {
                return $departmentUser instanceof DepartmentUser &&
                       $departmentUser->user_id === 1 &&
                       $departmentUser->department_id === 2;
            }))
            ->andReturn($storedDepartmentUser);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(DepartmentUser::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(1, $result->user_id);
        $this->assertEquals(2, $result->department_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: 1,
            department_id: 2
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_different_user_and_department_ids()
    {
        $request = $this->createMockStoreRequest(['user_id' => 5, 'department_id' => 10]);
        
        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: 5,
            department_id: 10
        );

        $storedDepartmentUser = new DepartmentUser(
            id: 3,
            user_id: 5,
            department_id: 10
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($departmentUser) {
                return $departmentUser->user_id === 5 &&
                       $departmentUser->department_id === 10;
            }))
            ->andReturn($storedDepartmentUser);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result->user_id);
        $this->assertEquals(10, $result->department_id);
    }

    public function test_perform_transaction_rollback_on_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: 1,
            department_id: 2
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Database error'));

        DB::shouldReceive('beginTransaction')->once();
        // Note: DB::rollback() would be called automatically by Laravel on exception

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_null_values_in_request()
    {
        $request = $this->createMockStoreRequest(['user_id' => null, 'department_id' => null]);
        
        $domainFromRequest = new DepartmentUser(
            id: null,
            user_id: null,
            department_id: null
        );

        $storedDepartmentUser = new DepartmentUser(
            id: 4,
            user_id: null,
            department_id: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($departmentUser) {
                return $departmentUser->user_id === null &&
                       $departmentUser->department_id === null;
            }))
            ->andReturn($storedDepartmentUser);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertNull($result->user_id);
        $this->assertNull($result->department_id);
    }

    private function createMockStoreRequest(array $data = []): StoreRequest
    {
        $defaultData = [
            'user_id' => 1,
            'department_id' => 2,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(StoreRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
