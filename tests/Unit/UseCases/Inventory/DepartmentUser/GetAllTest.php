<?php

namespace Tests\Unit\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentUserRepository;
use App\UseCases\Inventory\DepartmentUser\GetAll;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private DepartmentUserRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentUserRepository::class);
        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_department_users_from_organization()
    {
        $expectedResult = [
            'data' => [
                new DepartmentUser(1, 1, 1),
                new DepartmentUser(2, 2, 1),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $departmentUser) {
            $this->assertInstanceOf(DepartmentUser::class, $departmentUser);
        }
    }

    public function test_perform_with_empty_result()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(0, $result['total']);
    }

    public function test_perform_with_pagination()
    {
        $expectedResult = [
            'data' => [
                new DepartmentUser(1, 1, 1),
                new DepartmentUser(2, 2, 1),
            ],
            'count' => 2,
            'total' => 5,
            'currentPage' => 1,
            'lastPage' => 3,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(5, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_handles_repository_exception()
    {
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform();
    }

    public function test_perform_uses_authenticated_user_organization_id()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        // Verify that the correct organization ID is passed
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $this->useCase->perform();
    }

    public function test_perform_with_large_dataset()
    {
        $departmentUsers = [];
        for ($i = 1; $i <= 50; $i++) {
            $departmentUsers[] = new DepartmentUser($i, $i, 1);
        }

        $expectedResult = [
            'data' => array_slice($departmentUsers, 0, 30), // First 30 items
            'count' => 30,
            'total' => 50,
            'currentPage' => 1,
            'lastPage' => 2,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(30, $result['count']);
        $this->assertEquals(50, $result['total']);
        $this->assertEquals(2, $result['lastPage']);
    }

    public function test_perform_with_different_organization()
    {
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $expectedResult = [
            'data' => [
                new DepartmentUser(1, 1, 1),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($newOrganization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(1, $result['count']);
    }

    public function test_perform_returns_proper_structure()
    {
        $expectedResult = [
            'data' => [
                new DepartmentUser(1, 1, 1),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        // Verify the structure matches exactly what the repository returns
        $this->assertEquals($expectedResult, $result);
    }

    public function test_perform_with_multiple_department_users()
    {
        $departmentUsers = [
            new DepartmentUser(1, 1, 1),
            new DepartmentUser(2, 2, 1),
            new DepartmentUser(3, 3, 2),
            new DepartmentUser(4, 1, 2), // Same user in different department
        ];

        $expectedResult = [
            'data' => $departmentUsers,
            'count' => 4,
            'total' => 4,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertCount(4, $result['data']);
        
        // Verify we have the expected department user assignments
        $this->assertEquals(1, $result['data'][0]->user_id);
        $this->assertEquals(1, $result['data'][0]->department_id);
        $this->assertEquals(1, $result['data'][3]->user_id);
        $this->assertEquals(2, $result['data'][3]->department_id);
    }
}
