<?php

namespace Tests\Unit\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Client\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\UseCases\Inventory\Client\Update;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private ClientRepository $mockRepository;
    private ClientFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ClientRepository::class);
        $this->mockFactory = Mockery::mock(ClientFactory::class);

        $this->useCase = new Update(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_client_successfully()
    {
        $clientId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $updatedClient = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($client) use ($clientId) {
                    return $client instanceof Client && 
                           $client->id === $clientId &&
                           $client->name === 'Updated Name';
                }),
                $this->organization->id
            )
            ->andReturn($updatedClient);

        $result = $this->useCase->perform($request, $clientId);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals($clientId, $result->id);
        $this->assertEquals('Updated Name', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_correct_client_id()
    {
        $clientId = 123;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $updatedClient = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($client) use ($clientId) {
                    return $client->id === $clientId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedClient);

        $result = $this->useCase->perform($request, $clientId);

        $this->assertEquals($clientId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $clientId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $updatedClient = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id  // Should use authenticated user's organization
            )
            ->andReturn($updatedClient);

        $this->useCase->perform($request, $clientId);
    }

    public function test_perform_handles_repository_exception()
    {
        $clientId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request, $clientId);
    }

    public function test_perform_handles_factory_exception()
    {
        $clientId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $clientId);
    }

    private function createMockUpdateRequest(): UpdateRequest
    {
        return Mockery::mock(UpdateRequest::class);
    }
}
