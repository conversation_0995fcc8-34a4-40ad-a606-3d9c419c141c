<?php

namespace Tests\Unit\UseCases\Inventory\Client;

use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Client;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\UseCases\Inventory\Client\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private ClientRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ClientRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_clients()
    {
        $request = new Request([
            'name' => 'John',
            'order' => 'name',
            'by' => 'asc',
            'limit' => 10
        ]);

        $clients = [
            new Client(
                id: 1,
                organization_id: $this->organization->id,
                name: 'John Doe',
                phone: '+1234567890',
                email: '<EMAIL>',
                profession: 'Engineer',
                birthdate: '1990-01-01',
                cpf: '12345678901',
                cnpj: null,
                service: 'Software Development',
                address: '123 Main St',
                number: '123',
                neighborhood: 'Downtown',
                cep: '12345-678',
                complement: 'Apt 1',
                civil_state: 'single',
                description: 'Test client 1'
            ),
            new Client(
                id: 2,
                organization_id: $this->organization->id,
                name: 'John Smith',
                phone: '+0987654321',
                email: '<EMAIL>',
                profession: 'Designer',
                birthdate: '1985-05-15',
                cpf: '98765432109',
                cnpj: null,
                service: 'Graphic Design',
                address: '456 Oak Ave',
                number: '456',
                neighborhood: 'Uptown',
                cep: '98765-432',
                complement: 'Suite 2',
                civil_state: 'married',
                description: 'Test client 2'
            )
        ];

        $expectedResult = [
            'data' => $clients,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->with(
                Mockery::on(function ($filters) {
                    return $filters instanceof ClientFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'name' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 10;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $client) {
            $this->assertInstanceOf(Client::class, $client);
            $this->assertEquals($this->organization->id, $client->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->with(
                Mockery::on(function ($filters) {
                    return $filters instanceof ClientFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'cpf' => '12345678901'
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->with(
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof ClientFilters &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['email']) &&
                           isset($filters->filters['phone']) &&
                           isset($filters->filters['cpf']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'email',
            'by' => 'desc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->with(
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'email' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 50;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $clients = [];
        for ($i = 1; $i <= 5; $i++) {
            $clients[] = new Client(
                id: $i,
                organization_id: $this->organization->id,
                name: "Client $i",
                phone: "+123456789$i",
                email: "client$<EMAIL>",
                profession: 'Engineer',
                birthdate: '1990-01-01',
                cpf: "1234567890$i",
                cnpj: null,
                service: 'Software Development',
                address: "123 Main St $i",
                number: "$i",
                neighborhood: 'Downtown',
                cep: '12345-678',
                complement: "Apt $i",
                civil_state: 'single',
                description: "Test client $i"
            );
        }

        $expectedResult = [
            'data' => $clients,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchAll')
            ->once()
            ->with(
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }
}
