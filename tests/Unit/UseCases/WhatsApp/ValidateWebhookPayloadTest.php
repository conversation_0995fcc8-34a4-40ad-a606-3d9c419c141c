<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Services\Meta\WhatsApp\UseCases\Webhook\ValidateWebhookPayload;
use Tests\TestCase;

class ValidateWebhookPayloadTest extends TestCase
{
    private ValidateWebhookPayload $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->useCase = new ValidateWebhookPayload();
    }

    public function test_perform_returns_true_for_valid_message_webhook()
    {
        $validWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => '*********'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        'from' => '*************',
                                        'timestamp' => '*********0',
                                        'type' => 'text',
                                        'text' => ['body' => 'Hello']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($validWebhook);

        $this->assertTrue($result);
    }

    public function test_perform_returns_true_for_valid_status_webhook()
    {
        $validWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => '*********'
                                ],
                                'statuses' => [
                                    [
                                        'id' => 'msg_123',
                                        'status' => 'delivered',
                                        'timestamp' => '*********0',
                                        'recipient_id' => '*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($validWebhook);

        $this->assertTrue($result);
    }

    public function test_perform_returns_false_for_missing_object()
    {
        $invalidWebhook = [
            'entry' => []
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_wrong_object_type()
    {
        $invalidWebhook = [
            'object' => 'instagram_business_account',
            'entry' => []
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_missing_entry()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account'
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_invalid_entry_structure()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id'
                    // Missing changes
                ]
            ]
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_missing_phone_number_id()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    // Missing phone_number_id
                                ],
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        'from' => '*************',
                                        'timestamp' => '*********0',
                                        'type' => 'text'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_invalid_message_structure()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => '*********'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        // Missing required fields: from, timestamp, type
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_invalid_status_structure()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => '*********'
                                ],
                                'statuses' => [
                                    [
                                        'id' => 'msg_123',
                                        // Missing required fields: status, timestamp, recipient_id
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_no_messages_or_statuses()
    {
        $invalidWebhook = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_id',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => '*********'
                                ]
                                // Missing both messages and statuses
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($invalidWebhook);

        $this->assertFalse($result);
    }
}
