<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessageStatusUpdate;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry\ProcessWebhookEntry;
use Mockery;
use Tests\TestCase;

class ProcessWebhookStatusTest extends TestCase
{
    private ProcessWebhookEntry $processWebhookEntry;
    private ProcessWebhookMessageStatusUpdate $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->processWebhookEntry = Mockery::mock(ProcessWebhookEntry::class);
        $this->useCase = new ProcessWebhookMessageStatusUpdate($this->processWebhookEntry);
    }

    public function test_perform_processes_status_updates_successfully()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'msg_123',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+*************'
                ],
                [
                    'id' => 'msg_124',
                    'status' => 'read',
                    'timestamp' => '**********',
                    'recipient_id' => '+*************'
                ]
            ]
        ];

        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($webhookData) {
                return $webhookData['object'] === 'whatsapp_business_account' &&
                       isset($webhookData['entry'][0]['changes'][0]['value']['statuses']) &&
                       count($webhookData['entry'][0]['changes'][0]['value']['statuses']) === 2;
            }))
            ->andReturn([
                'success' => true,
                'processed_entries' => 1,
                'entries' => ['entry1']
            ]);

        $result = $this->useCase->perform($changeValueData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(2, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
        $this->assertEquals(1, $result['processed_entries']);
        $this->assertEquals(['entry1'], $result['entries']);

        // Verify status summary
        $statusSummary = $result['statuses_processed'];
        $this->assertEquals(2, $statusSummary['total']);
        $this->assertEquals(1, $statusSummary['by_status']['delivered']);
        $this->assertEquals(1, $statusSummary['by_status']['read']);
        $this->assertEquals(['msg_123', 'msg_124'], $statusSummary['message_ids']);
    }

    public function test_perform_returns_error_when_no_statuses()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ]
            // No statuses array
        ];

        $result = $this->useCase->perform($changeValueData, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('No statuses found in change value', $result['error']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_perform_handles_process_webhook_entry_exception()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'msg_123',
                    'status' => 'failed',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+*************'
                ]
            ]
        ];

        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andThrow(new \Exception('ProcessWebhookEntry error'));

        $result = $this->useCase->perform($changeValueData, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ProcessWebhookEntry error', $result['error']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
    }

    public function test_perform_handles_missing_business_id()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
            // No whatsapp_business_id
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'msg_123',
                    'status' => 'sent',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+*************'
                ]
            ]
        ];

        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->with(Mockery::on(function ($webhookData) {
                return $webhookData['entry'][0]['id'] === 'unknown';
            }))
            ->andReturn(['success' => true, 'processed_entries' => 1]);

        $result = $this->useCase->perform($changeValueData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
    }

    public function test_perform_processes_multiple_status_types()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'msg_1',
                    'status' => 'sent',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+*************'
                ],
                [
                    'id' => 'msg_2',
                    'status' => 'delivered',
                    'timestamp' => '**********',
                    'recipient_id' => '+*************'
                ],
                [
                    'id' => 'msg_3',
                    'status' => 'delivered',
                    'timestamp' => '1234567892',
                    'recipient_id' => '+*************'
                ],
                [
                    'id' => 'msg_4',
                    'status' => 'read',
                    'timestamp' => '1234567893',
                    'recipient_id' => '+*************'
                ],
                [
                    'id' => 'msg_5',
                    'status' => 'failed',
                    'timestamp' => '1234567894',
                    'recipient_id' => '+*************'
                ]
            ]
        ];

        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn(['success' => true, 'processed_entries' => 1]);

        $result = $this->useCase->perform($changeValueData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals(5, $result['processed']);

        $statusSummary = $result['statuses_processed'];
        $this->assertEquals(5, $statusSummary['total']);
        $this->assertEquals(1, $statusSummary['by_status']['sent']);
        $this->assertEquals(2, $statusSummary['by_status']['delivered']);
        $this->assertEquals(1, $statusSummary['by_status']['read']);
        $this->assertEquals(1, $statusSummary['by_status']['failed']);
        $this->assertEquals(['msg_1', 'msg_2', 'msg_3', 'msg_4', 'msg_5'], $statusSummary['message_ids']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
