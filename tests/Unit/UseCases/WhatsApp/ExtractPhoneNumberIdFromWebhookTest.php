<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Services\Meta\WhatsApp\UseCases\Webhook\ExtractPhoneNumberIdFromWebhook;
use Tests\TestCase;

class ExtractPhoneNumberIdFromWebhookTest extends TestCase
{
    private ExtractPhoneNumberIdFromWebhook $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->useCase = new ExtractPhoneNumberIdFromWebhook();
    }

    /** @test */
    public function it_can_extract_phone_number_id_from_valid_webhook_payload()
    {
        $webhookData = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '****************',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'metadata' => [
                                    'display_phone_number' => '***********',
                                    'phone_number_id' => '***************'
                                ],
                                'messages' => [
                                    [
                                        'from' => '************',
                                        'id' => 'wamid.test',
                                        'timestamp' => '**********',
                                        'text' => ['body' => 'Hello'],
                                        'type' => 'text'
                                    ]
                                ]
                            ],
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($webhookData);

        $this->assertEquals('***************', $result);
    }

    /** @test */
    public function it_returns_null_when_entry_is_missing()
    {
        $webhookData = [
            'object' => 'whatsapp_business_account'
        ];

        $result = $this->useCase->perform($webhookData);

        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_null_when_phone_number_id_is_missing()
    {
        $webhookData = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '****************',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'metadata' => [
                                    'display_phone_number' => '***********'
                                ]
                            ],
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];

        $result = $this->useCase->perform($webhookData);

        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_malformed_payload_gracefully()
    {
        $webhookData = [
            'entry' => 'invalid_structure'
        ];

        $result = $this->useCase->perform($webhookData);

        $this->assertNull($result);
    }
}
