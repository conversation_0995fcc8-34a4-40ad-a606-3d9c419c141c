<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessage;
use Mockery;
use Tests\TestCase;

class ProcessWebhookMessageTest extends TestCase
{
    private ChatBotService $chatBotService;
    private ProcessWebhookMessage $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->chatBotService = Mockery::mock(ChatBotService::class);
        $this->useCase = new ProcessWebhookMessage($this->chatBotService);
    }

    public function test_perform_processes_incoming_messages_successfully()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888', // Different from business number
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ],
            'contacts' => [
                [
                    'profile' => ['name' => 'John Doe'],
                    'wa_id' => '+5511888888888'
                ]
            ]
        ];

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->once()
            ->andReturn(['success' => true, 'message_id' => 'msg_123']);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
        $this->assertCount(1, $result['results']);
    }

    public function test_perform_skips_outgoing_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '5511999999999', // Same as business number (outgoing)
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ];

        // Should not call processWebhook for outgoing messages
        $this->chatBotService
            ->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(0, $result['processed']); // No messages processed
        $this->assertEmpty($result['results']);
    }

    public function test_perform_returns_error_when_no_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ]
            // No messages array
        ];

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('No messages found in change value', $result['error']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_perform_handles_chatbot_service_exception()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ];

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->once()
            ->andThrow(new \Exception('ChatBot service error'));

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ChatBot service error', $result['error']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
    }

    public function test_perform_processes_multiple_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ],
                [
                    'id' => 'msg_124',
                    'from' => '+5511777777777',
                    'timestamp' => '1234567891',
                    'type' => 'text',
                    'text' => ['body' => 'Hi']
                ]
            ]
        ];

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->twice()
            ->andReturn(['success' => true]);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(2, $result['processed']);
        $this->assertCount(2, $result['results']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
