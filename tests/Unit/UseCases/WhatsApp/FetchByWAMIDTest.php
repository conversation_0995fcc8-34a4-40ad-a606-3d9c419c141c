<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Domains\WhatsApp\ChangeValue;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage\FetchByWAMID;
use Mockery;
use Tests\TestCase;

class FetchByWAMIDTest extends TestCase
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private FetchByWAMID $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->whatsAppMessageRepository = Mockery::mock(WhatsAppMessageRepository::class);
        $this->useCase = new FetchByWAMID($this->whatsAppMessageRepository);
    }

    public function test_perform_returns_whatsapp_message_when_wam_id_found()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.123456789',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);
        $expectedWhatsAppMessage = Mockery::mock(WhatsAppMessage::class);

        $this->whatsAppMessageRepository
            ->shouldReceive('fetchByExternalWamId')
            ->once()
            ->with('wamid.123456789')
            ->andReturn($expectedWhatsAppMessage);

        // Act
        $result = $this->useCase->perform($changeValue);

        // Assert
        $this->assertSame($expectedWhatsAppMessage, $result);
    }

    public function test_perform_returns_null_when_no_wam_id_in_change_value()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [],
            'messages' => []
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->whatsAppMessageRepository
            ->shouldNotReceive('fetchByExternalWamId');

        // Act
        $result = $this->useCase->perform($changeValue);

        // Assert
        $this->assertNull($result);
    }

    public function test_perform_returns_null_when_whatsapp_message_not_found()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.nonexistent',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->whatsAppMessageRepository
            ->shouldReceive('fetchByExternalWamId')
            ->once()
            ->with('wamid.nonexistent')
            ->andReturn(null);

        // Act
        $result = $this->useCase->perform($changeValue);

        // Assert
        $this->assertNull($result);
    }

    public function test_perform_uses_message_id_when_no_statuses()
    {
        // Arrange
        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [],
            'messages' => [
                [
                    'id' => 'wamid.message123',
                    'from' => '+5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);
        $expectedWhatsAppMessage = Mockery::mock(WhatsAppMessage::class);

        $this->whatsAppMessageRepository
            ->shouldReceive('fetchByExternalWamId')
            ->once()
            ->with('wamid.message123')
            ->andReturn($expectedWhatsAppMessage);

        // Act
        $result = $this->useCase->perform($changeValue);

        // Assert
        $this->assertSame($expectedWhatsAppMessage, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
