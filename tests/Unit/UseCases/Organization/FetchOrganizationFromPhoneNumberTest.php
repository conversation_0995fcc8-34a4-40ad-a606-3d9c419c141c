<?php

namespace Tests\Unit\UseCases\Organization;

use Tests\TestCase;
use App\UseCases\Organization\FetchOrganizationFromPhoneNumber;
use App\Repositories\PhoneNumberRepository;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use Mockery;

class FetchOrganizationFromPhoneNumberTest extends TestCase
{
    private PhoneNumberRepository $phoneNumberRepository;
    private FetchOrganizationFromPhoneNumber $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->phoneNumberRepository = Mockery::mock(PhoneNumberRepository::class);
        $this->useCase = new FetchOrganizationFromPhoneNumber($this->phoneNumberRepository);
    }

    public function test_perform_returns_organization_and_phone_number_when_found()
    {
        $phoneNumberId = 'test_phone_number_id';
        
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: $phoneNumberId,
            organization: $organization
        );

        $this->phoneNumberRepository
            ->shouldReceive('fetchByWhatsAppPhoneNumberId')
            ->with($phoneNumberId)
            ->once()
            ->andReturn($phoneNumber);

        $result = $this->useCase->perform($phoneNumberId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('organization', $result);
        $this->assertArrayHasKey('phone_number', $result);
        $this->assertInstanceOf(Organization::class, $result['organization']);
        $this->assertInstanceOf(PhoneNumber::class, $result['phone_number']);
        $this->assertEquals($organization, $result['organization']);
        $this->assertEquals($phoneNumber, $result['phone_number']);
    }

    public function test_perform_returns_null_when_phone_number_not_found()
    {
        $phoneNumberId = 'non_existent_phone_number_id';

        $this->phoneNumberRepository
            ->shouldReceive('fetchByWhatsAppPhoneNumberId')
            ->with($phoneNumberId)
            ->once()
            ->andReturn(null);

        $result = $this->useCase->perform($phoneNumberId);

        $this->assertNull($result);
    }

    public function test_perform_returns_null_when_phone_number_has_no_organization()
    {
        $phoneNumberId = 'test_phone_number_id';

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: $phoneNumberId,
            organization: null
        );

        $this->phoneNumberRepository
            ->shouldReceive('fetchByWhatsAppPhoneNumberId')
            ->with($phoneNumberId)
            ->once()
            ->andReturn($phoneNumber);

        $result = $this->useCase->perform($phoneNumberId);

        $this->assertNull($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
