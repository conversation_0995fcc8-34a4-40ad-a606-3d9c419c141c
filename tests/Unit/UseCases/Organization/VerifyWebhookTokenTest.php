<?php

namespace Tests\Unit\UseCases\Organization;

use Tests\TestCase;
use App\UseCases\Organization\VerifyWebhookToken;
use App\Repositories\OrganizationRepository;
use App\Domains\Organization;
use Mockery;

class VerifyWebhookTokenTest extends TestCase
{
    private OrganizationRepository $organizationRepository;
    private VerifyWebhookToken $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organizationRepository = Mockery::mock(OrganizationRepository::class);
        $this->useCase = new VerifyWebhookToken($this->organizationRepository);
    }

    public function test_perform_returns_success_with_organization_token()
    {
        $mode = 'subscribe';
        $token = 'org_token_123';
        
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false,
            whatsapp_webhook_verify_token: $token
        );

        $this->organizationRepository
            ->shouldReceive('fetchByWebhookToken')
            ->with($token)
            ->once()
            ->andReturn($organization);

        $result = $this->useCase->perform($mode, $token);

        $this->assertTrue($result['success']);
        $this->assertEquals('organization', $result['type']);
        $this->assertInstanceOf(Organization::class, $result['organization']);
        $this->assertEquals($organization, $result['organization']);
    }

    public function test_perform_returns_success_with_global_token()
    {
        $mode = 'subscribe';
        $token = 'global_token_123';
        
        config(['whatsapp.webhook_verify_token' => $token]);

        $this->organizationRepository
            ->shouldReceive('fetchByWebhookToken')
            ->with($token)
            ->once()
            ->andReturn(null);

        $result = $this->useCase->perform($mode, $token);

        $this->assertTrue($result['success']);
        $this->assertEquals('global', $result['type']);
        $this->assertNull($result['organization']);
    }

    public function test_perform_returns_error_for_invalid_mode()
    {
        $mode = 'invalid_mode';
        $token = 'any_token';

        $result = $this->useCase->perform($mode, $token);

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid mode', $result['error']);
    }

    public function test_perform_returns_error_for_invalid_token()
    {
        $mode = 'subscribe';
        $token = 'invalid_token';
        
        config(['whatsapp.webhook_verify_token' => 'different_global_token']);

        $this->organizationRepository
            ->shouldReceive('fetchByWebhookToken')
            ->with($token)
            ->once()
            ->andReturn(null);

        $result = $this->useCase->perform($mode, $token);

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid token', $result['error']);
    }

    public function test_perform_prioritizes_organization_token_over_global()
    {
        $mode = 'subscribe';
        $token = 'shared_token_123';
        
        // Set same token as global
        config(['whatsapp.webhook_verify_token' => $token]);
        
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false,
            whatsapp_webhook_verify_token: $token
        );

        $this->organizationRepository
            ->shouldReceive('fetchByWebhookToken')
            ->with($token)
            ->once()
            ->andReturn($organization);

        $result = $this->useCase->perform($mode, $token);

        $this->assertTrue($result['success']);
        $this->assertEquals('organization', $result['type']);
        $this->assertInstanceOf(Organization::class, $result['organization']);
        $this->assertEquals($organization, $result['organization']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
