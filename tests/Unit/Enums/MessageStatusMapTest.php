<?php

namespace Tests\Unit\Enums;

use App\Enums\MessageStatus;
use Tests\TestCase;

class MessageStatusMapTest extends TestCase
{
    public function test_map_whatsapp_status_to_message_status_maps_sent_correctly()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('sent');

        // Assert
        $this->assertEquals(MessageStatus::is_sent, $result);
    }

    public function test_map_whatsapp_status_to_message_status_maps_delivered_correctly()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('delivered');

        // Assert
        $this->assertEquals(MessageStatus::is_delivered, $result);
    }

    public function test_map_whatsapp_status_to_message_status_maps_read_correctly()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('read');

        // Assert
        $this->assertEquals(MessageStatus::is_read, $result);
    }

    public function test_map_whatsapp_status_to_message_status_maps_failed_correctly()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('failed');

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $result);
    }

    public function test_map_whatsapp_status_to_message_status_maps_unknown_status_to_failed()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('unknown_status');

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $result);
    }

    public function test_map_whatsapp_status_to_message_status_handles_empty_string()
    {
        // Act
        $result = MessageStatus::mapWhatsAppStatusToMessageStatus('');

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $result);
    }

    public function test_map_whatsapp_status_to_message_status_is_case_sensitive()
    {
        // Act
        $resultUppercase = MessageStatus::mapWhatsAppStatusToMessageStatus('SENT');
        $resultMixedCase = MessageStatus::mapWhatsAppStatusToMessageStatus('Delivered');

        // Assert
        $this->assertEquals(MessageStatus::is_failed, $resultUppercase);
        $this->assertEquals(MessageStatus::is_failed, $resultMixedCase);
    }

    public function test_map_whatsapp_status_to_message_status_handles_all_valid_statuses()
    {
        // Arrange
        $validMappings = [
            'sent' => MessageStatus::is_sent,
            'delivered' => MessageStatus::is_delivered,
            'read' => MessageStatus::is_read,
            'failed' => MessageStatus::is_failed,
        ];

        // Act & Assert
        foreach ($validMappings as $whatsappStatus => $expectedMessageStatus) {
            $result = MessageStatus::mapWhatsAppStatusToMessageStatus($whatsappStatus);
            $this->assertEquals($expectedMessageStatus, $result, "Failed mapping for status: {$whatsappStatus}");
        }
    }
}
