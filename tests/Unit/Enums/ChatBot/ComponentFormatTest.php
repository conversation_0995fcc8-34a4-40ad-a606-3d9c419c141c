<?php

namespace Tests\Unit\Enums\ChatBot;

use App\Enums\ChatBot\ComponentFormat;
use PHPUnit\Framework\TestCase;

class ComponentFormatTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('TEXT', ComponentFormat::TEXT->value);
        $this->assertEquals('IMAGE', ComponentFormat::IMAGE->value);
        $this->assertEquals('VIDEO', ComponentFormat::VIDEO->value);
        $this->assertEquals('DOCUMENT', ComponentFormat::DOCUMENT->value);
    }

    public function test_get_all()
    {
        $expected = [
            'TEXT',
            'IMAGE',
            'VIDEO',
            'DOCUMENT'
        ];

        $this->assertEquals($expected, ComponentFormat::getAll());
    }

    public function test_get_description()
    {
        $this->assertEquals('Conteúdo de texto simples', ComponentFormat::TEXT->getDescription());
        $this->assertEquals('Conteúdo de mídia de imagem (JPEG, PNG, WebP)', ComponentFormat::IMAGE->getDescription());
        $this->assertEquals('Conteúdo de mídia de vídeo (MP4, 3GPP)', ComponentFormat::VIDEO->getDescription());
        $this->assertEquals('Conteúdo de documento (PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX)', ComponentFormat::DOCUMENT->getDescription());
    }

    public function test_get_display_name()
    {
        $this->assertEquals('Texto', ComponentFormat::TEXT->getDisplayName());
        $this->assertEquals('Imagem', ComponentFormat::IMAGE->getDisplayName());
        $this->assertEquals('Vídeo', ComponentFormat::VIDEO->getDisplayName());
        $this->assertEquals('Documento', ComponentFormat::DOCUMENT->getDisplayName());
    }

    public function test_is_valid()
    {
        $this->assertTrue(ComponentFormat::isValid('TEXT'));
        $this->assertTrue(ComponentFormat::isValid('IMAGE'));
        $this->assertTrue(ComponentFormat::isValid('VIDEO'));
        $this->assertTrue(ComponentFormat::isValid('DOCUMENT'));

        $this->assertFalse(ComponentFormat::isValid('invalid'));
        $this->assertFalse(ComponentFormat::isValid(''));
        $this->assertFalse(ComponentFormat::isValid('text'));
    }

    public function test_from_string()
    {
        $this->assertEquals(ComponentFormat::TEXT, ComponentFormat::fromString('text'));
        $this->assertEquals(ComponentFormat::TEXT, ComponentFormat::fromString('TEXT'));
        $this->assertEquals(ComponentFormat::IMAGE, ComponentFormat::fromString('image'));
        $this->assertEquals(ComponentFormat::IMAGE, ComponentFormat::fromString('IMAGE'));
        $this->assertEquals(ComponentFormat::VIDEO, ComponentFormat::fromString('video'));
        $this->assertEquals(ComponentFormat::VIDEO, ComponentFormat::fromString('VIDEO'));
        $this->assertEquals(ComponentFormat::DOCUMENT, ComponentFormat::fromString('document'));
        $this->assertEquals(ComponentFormat::DOCUMENT, ComponentFormat::fromString('DOCUMENT'));

        $this->assertNull(ComponentFormat::fromString('invalid'));
        $this->assertNull(ComponentFormat::fromString(''));
    }

    public function test_is_media_type()
    {
        $this->assertFalse(ComponentFormat::TEXT->isMediaType());
        $this->assertTrue(ComponentFormat::IMAGE->isMediaType());
        $this->assertTrue(ComponentFormat::VIDEO->isMediaType());
        $this->assertTrue(ComponentFormat::DOCUMENT->isMediaType());
    }

    public function test_supports_caption()
    {
        $this->assertFalse(ComponentFormat::TEXT->supportsCaption());
        $this->assertTrue(ComponentFormat::IMAGE->supportsCaption());
        $this->assertTrue(ComponentFormat::VIDEO->supportsCaption());
        $this->assertTrue(ComponentFormat::DOCUMENT->supportsCaption());
    }

    public function test_get_max_file_size()
    {
        $this->assertEquals(0, ComponentFormat::TEXT->getMaxFileSize());
        $this->assertEquals(5 * 1024 * 1024, ComponentFormat::IMAGE->getMaxFileSize()); // 5MB
        $this->assertEquals(16 * 1024 * 1024, ComponentFormat::VIDEO->getMaxFileSize()); // 16MB
        $this->assertEquals(100 * 1024 * 1024, ComponentFormat::DOCUMENT->getMaxFileSize()); // 100MB
    }

    public function test_get_supported_extensions()
    {
        $this->assertEquals([], ComponentFormat::TEXT->getSupportedExtensions());
        $this->assertEquals(['jpg', 'jpeg', 'png', 'webp'], ComponentFormat::IMAGE->getSupportedExtensions());
        $this->assertEquals(['mp4', '3gpp'], ComponentFormat::VIDEO->getSupportedExtensions());
        $this->assertEquals(['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'], ComponentFormat::DOCUMENT->getSupportedExtensions());
    }

    public function test_get_supported_mime_types()
    {
        $this->assertEquals([], ComponentFormat::TEXT->getSupportedMimeTypes());

        $imageTypes = ComponentFormat::IMAGE->getSupportedMimeTypes();
        $this->assertContains('image/jpeg', $imageTypes);
        $this->assertContains('image/png', $imageTypes);
        $this->assertContains('image/webp', $imageTypes);

        $videoTypes = ComponentFormat::VIDEO->getSupportedMimeTypes();
        $this->assertContains('video/mp4', $videoTypes);
        $this->assertContains('video/3gpp', $videoTypes);

        $documentTypes = ComponentFormat::DOCUMENT->getSupportedMimeTypes();
        $this->assertContains('application/pdf', $documentTypes);
        $this->assertContains('application/msword', $documentTypes);
        $this->assertContains('application/vnd.openxmlformats-officedocument.wordprocessingml.document', $documentTypes);
    }

    public function test_validate_file_image()
    {
        // Valid image file
        $this->assertTrue(ComponentFormat::IMAGE->validateFile(
            'test.jpg',
            'image/jpeg',
            1024 * 1024 // 1MB
        ));

        // Invalid - file too large
        $this->assertFalse(ComponentFormat::IMAGE->validateFile(
            'test.jpg',
            'image/jpeg',
            6 * 1024 * 1024 // 6MB
        ));

        // Invalid - wrong MIME type
        $this->assertFalse(ComponentFormat::IMAGE->validateFile(
            'test.jpg',
            'video/mp4',
            1024 * 1024
        ));

        // Invalid - wrong extension
        $this->assertFalse(ComponentFormat::IMAGE->validateFile(
            'test.mp4',
            'image/jpeg',
            1024 * 1024
        ));
    }

    public function test_validate_file_video()
    {
        // Valid video file
        $this->assertTrue(ComponentFormat::VIDEO->validateFile(
            'test.mp4',
            'video/mp4',
            10 * 1024 * 1024 // 10MB
        ));

        // Invalid - file too large
        $this->assertFalse(ComponentFormat::VIDEO->validateFile(
            'test.mp4',
            'video/mp4',
            20 * 1024 * 1024 // 20MB
        ));
    }

    public function test_validate_file_document()
    {
        // Valid document file
        $this->assertTrue(ComponentFormat::DOCUMENT->validateFile(
            'test.pdf',
            'application/pdf',
            50 * 1024 * 1024 // 50MB
        ));

        // Invalid - file too large
        $this->assertFalse(ComponentFormat::DOCUMENT->validateFile(
            'test.pdf',
            'application/pdf',
            150 * 1024 * 1024 // 150MB
        ));
    }

    public function test_get_whatsapp_media_type()
    {
        $this->assertNull(ComponentFormat::TEXT->getWhatsAppMediaType());
        $this->assertEquals('image', ComponentFormat::IMAGE->getWhatsAppMediaType());
        $this->assertEquals('video', ComponentFormat::VIDEO->getWhatsAppMediaType());
        $this->assertEquals('document', ComponentFormat::DOCUMENT->getWhatsAppMediaType());
    }

    public function test_to_whatsapp_format()
    {
        $this->assertEquals('text', ComponentFormat::TEXT->toWhatsAppFormat());
        $this->assertEquals('image', ComponentFormat::IMAGE->toWhatsAppFormat());
        $this->assertEquals('video', ComponentFormat::VIDEO->toWhatsAppFormat());
        $this->assertEquals('document', ComponentFormat::DOCUMENT->toWhatsAppFormat());
    }

    public function test_get_media_formats()
    {
        $mediaFormats = ComponentFormat::getMediaFormats();

        $this->assertContains(ComponentFormat::IMAGE, $mediaFormats);
        $this->assertContains(ComponentFormat::VIDEO, $mediaFormats);
        $this->assertContains(ComponentFormat::DOCUMENT, $mediaFormats);
        $this->assertNotContains(ComponentFormat::TEXT, $mediaFormats);
        $this->assertCount(3, $mediaFormats);
    }

    public function test_get_caption_supported_formats()
    {
        $captionFormats = ComponentFormat::getCaptionSupportedFormats();

        $this->assertContains(ComponentFormat::IMAGE, $captionFormats);
        $this->assertContains(ComponentFormat::VIDEO, $captionFormats);
        $this->assertContains(ComponentFormat::DOCUMENT, $captionFormats);
        $this->assertNotContains(ComponentFormat::TEXT, $captionFormats);
        $this->assertCount(3, $captionFormats);
    }

    public function test_get_icon()
    {
        $this->assertEquals('text', ComponentFormat::TEXT->getIcon());
        $this->assertEquals('image', ComponentFormat::IMAGE->getIcon());
        $this->assertEquals('video', ComponentFormat::VIDEO->getIcon());
        $this->assertEquals('file-text', ComponentFormat::DOCUMENT->getIcon());
    }

    public function test_get_css_class()
    {
        $this->assertEquals('format-text', ComponentFormat::TEXT->getCssClass());
        $this->assertEquals('format-image', ComponentFormat::IMAGE->getCssClass());
        $this->assertEquals('format-video', ComponentFormat::VIDEO->getCssClass());
        $this->assertEquals('format-document', ComponentFormat::DOCUMENT->getCssClass());
    }

    public function test_app_make_works()
    {
        // Test that we can use the enum class directly
        $this->assertInstanceOf(ComponentFormat::class, ComponentFormat::TEXT);

        // Test that enum values work correctly
        $this->assertEquals('TEXT', ComponentFormat::TEXT->value);
        $this->assertTrue(ComponentFormat::isValid('TEXT'));
    }

    public function test_all_cases_covered()
    {
        $allCases = ComponentFormat::cases();
        $this->assertCount(4, $allCases);

        $expectedCases = [
            ComponentFormat::TEXT,
            ComponentFormat::IMAGE,
            ComponentFormat::VIDEO,
            ComponentFormat::DOCUMENT
        ];

        foreach ($expectedCases as $expectedCase) {
            $this->assertContains($expectedCase, $allCases);
        }
    }

    public function test_file_validation_edge_cases()
    {
        // Test with exact file size limits
        $this->assertTrue(ComponentFormat::IMAGE->validateFile(
            'test.jpg',
            'image/jpeg',
            5 * 1024 * 1024 // Exactly 5MB
        ));

        $this->assertFalse(ComponentFormat::IMAGE->validateFile(
            'test.jpg',
            'image/jpeg',
            5 * 1024 * 1024 + 1 // 5MB + 1 byte
        ));

        // Test case insensitive extension matching
        $this->assertTrue(ComponentFormat::IMAGE->validateFile(
            'test.JPG',
            'image/jpeg',
            1024 * 1024
        ));

        $this->assertTrue(ComponentFormat::IMAGE->validateFile(
            'test.JPEG',
            'image/jpeg',
            1024 * 1024
        ));
    }
}
