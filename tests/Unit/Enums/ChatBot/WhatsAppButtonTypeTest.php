<?php

namespace Tests\Unit\Enums\ChatBot;

use App\Enums\ChatBot\WhatsAppButtonType;
use PHPUnit\Framework\TestCase;

class WhatsAppButtonTypeTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('reply', WhatsAppButtonType::REPLY->value);
        $this->assertEquals('url', WhatsAppButtonType::URL->value);
        $this->assertEquals('phone_number', WhatsAppButtonType::PHONE_NUMBER->value);
        $this->assertEquals('copy_code', WhatsAppButtonType::COPY_CODE->value);
        $this->assertEquals('flow', WhatsAppButtonType::FLOW->value);
    }

    public function test_get_all()
    {
        $expected = [
            'reply',
            'url',
            'phone_number',
            'copy_code',
            'flow'
        ];

        $this->assertEquals($expected, WhatsAppButtonType::getAll());
    }

    public function test_get_description()
    {
        $this->assertEquals('Botão de resposta rápida que envia uma resposta predefinida', WhatsAppButtonType::REPLY->getDescription());
        $this->assertEquals('Botão que abre uma URL no navegador', WhatsAppButtonType::URL->getDescription());
        $this->assertEquals('Botão que inicia uma chamada telefônica', WhatsAppButtonType::PHONE_NUMBER->getDescription());
        $this->assertEquals('Botão que copia um código para a área de transferência', WhatsAppButtonType::COPY_CODE->getDescription());
        $this->assertEquals('Botão que aciona um WhatsApp Flow', WhatsAppButtonType::FLOW->getDescription());
    }

    public function test_is_valid()
    {
        $this->assertTrue(WhatsAppButtonType::isValid('reply'));
        $this->assertTrue(WhatsAppButtonType::isValid('url'));
        $this->assertTrue(WhatsAppButtonType::isValid('phone_number'));
        $this->assertTrue(WhatsAppButtonType::isValid('copy_code'));
        $this->assertTrue(WhatsAppButtonType::isValid('flow'));

        $this->assertFalse(WhatsAppButtonType::isValid('invalid'));
        $this->assertFalse(WhatsAppButtonType::isValid(''));
        $this->assertFalse(WhatsAppButtonType::isValid('REPLY'));
    }

    public function test_from_string()
    {
        $this->assertEquals(WhatsAppButtonType::REPLY, WhatsAppButtonType::fromString('reply'));
        $this->assertEquals(WhatsAppButtonType::URL, WhatsAppButtonType::fromString('url'));
        $this->assertEquals(WhatsAppButtonType::PHONE_NUMBER, WhatsAppButtonType::fromString('phone_number'));
        $this->assertEquals(WhatsAppButtonType::COPY_CODE, WhatsAppButtonType::fromString('copy_code'));
        $this->assertEquals(WhatsAppButtonType::FLOW, WhatsAppButtonType::fromString('flow'));

        $this->assertNull(WhatsAppButtonType::fromString('invalid'));
        $this->assertNull(WhatsAppButtonType::fromString(''));
        $this->assertNull(WhatsAppButtonType::fromString('REPLY'));
    }

    public function test_get_max_buttons()
    {
        $this->assertEquals(3, WhatsAppButtonType::REPLY->getMaxButtons());
        $this->assertEquals(1, WhatsAppButtonType::URL->getMaxButtons());
        $this->assertEquals(1, WhatsAppButtonType::PHONE_NUMBER->getMaxButtons());
        $this->assertEquals(1, WhatsAppButtonType::COPY_CODE->getMaxButtons());
        $this->assertEquals(1, WhatsAppButtonType::FLOW->getMaxButtons());
    }

    public function test_get_max_title_length()
    {
        $this->assertEquals(20, WhatsAppButtonType::REPLY->getMaxTitleLength());
        $this->assertEquals(20, WhatsAppButtonType::URL->getMaxTitleLength());
        $this->assertEquals(20, WhatsAppButtonType::PHONE_NUMBER->getMaxTitleLength());
        $this->assertEquals(25, WhatsAppButtonType::COPY_CODE->getMaxTitleLength());
        $this->assertEquals(25, WhatsAppButtonType::FLOW->getMaxTitleLength());
    }

    public function test_requires_additional_data()
    {
        $this->assertFalse(WhatsAppButtonType::REPLY->requiresAdditionalData());
        $this->assertTrue(WhatsAppButtonType::URL->requiresAdditionalData());
        $this->assertTrue(WhatsAppButtonType::PHONE_NUMBER->requiresAdditionalData());
        $this->assertTrue(WhatsAppButtonType::COPY_CODE->requiresAdditionalData());
        $this->assertTrue(WhatsAppButtonType::FLOW->requiresAdditionalData());
    }

    public function test_get_additional_data_field()
    {
        $this->assertNull(WhatsAppButtonType::REPLY->getAdditionalDataField());
        $this->assertEquals('url', WhatsAppButtonType::URL->getAdditionalDataField());
        $this->assertEquals('phone_number', WhatsAppButtonType::PHONE_NUMBER->getAdditionalDataField());
        $this->assertEquals('copy_code', WhatsAppButtonType::COPY_CODE->getAdditionalDataField());
        $this->assertEquals('flow_token', WhatsAppButtonType::FLOW->getAdditionalDataField());
    }

    public function test_validate_button_data_reply()
    {
        // Valid reply button data
        $this->assertTrue(WhatsAppButtonType::REPLY->validateButtonData(['title' => 'Yes']));
        $this->assertTrue(WhatsAppButtonType::REPLY->validateButtonData(['title' => str_repeat('a', 20)]));

        // Invalid reply button data - title too long
        $this->assertFalse(WhatsAppButtonType::REPLY->validateButtonData(['title' => str_repeat('a', 21)]));
    }

    public function test_validate_button_data_url()
    {
        // Valid URL button data
        $this->assertTrue(WhatsAppButtonType::URL->validateButtonData([
            'title' => 'Visit',
            'url' => 'https://example.com'
        ]));

        // Invalid URL button data - missing URL
        $this->assertFalse(WhatsAppButtonType::URL->validateButtonData(['title' => 'Visit']));

        // Invalid URL button data - invalid URL
        $this->assertFalse(WhatsAppButtonType::URL->validateButtonData([
            'title' => 'Visit',
            'url' => 'not-a-url'
        ]));

        // Invalid URL button data - title too long
        $this->assertFalse(WhatsAppButtonType::URL->validateButtonData([
            'title' => str_repeat('a', 21),
            'url' => 'https://example.com'
        ]));
    }

    public function test_validate_button_data_phone_number()
    {
        // Valid phone number button data
        $this->assertTrue(WhatsAppButtonType::PHONE_NUMBER->validateButtonData([
            'title' => 'Call',
            'phone_number' => '+5511999999999'
        ]));

        // Invalid phone number button data - missing phone number
        $this->assertFalse(WhatsAppButtonType::PHONE_NUMBER->validateButtonData(['title' => 'Call']));

        // Invalid phone number button data - invalid phone number
        $this->assertFalse(WhatsAppButtonType::PHONE_NUMBER->validateButtonData([
            'title' => 'Call',
            'phone_number' => '123'
        ]));

        // Invalid phone number button data - title too long
        $this->assertFalse(WhatsAppButtonType::PHONE_NUMBER->validateButtonData([
            'title' => str_repeat('a', 21),
            'phone_number' => '+5511999999999'
        ]));
    }

    public function test_validate_button_data_copy_code()
    {
        // Valid copy code button data
        $this->assertTrue(WhatsAppButtonType::COPY_CODE->validateButtonData([
            'title' => 'Copy',
            'copy_code' => 'PROMO123'
        ]));

        // Invalid copy code button data - missing copy code
        $this->assertFalse(WhatsAppButtonType::COPY_CODE->validateButtonData(['title' => 'Copy']));

        // Invalid copy code button data - title too long
        $this->assertFalse(WhatsAppButtonType::COPY_CODE->validateButtonData([
            'title' => str_repeat('a', 26),
            'copy_code' => 'PROMO123'
        ]));
    }

    public function test_validate_button_data_flow()
    {
        // Valid flow button data
        $this->assertTrue(WhatsAppButtonType::FLOW->validateButtonData([
            'title' => 'Start',
            'flow_token' => 'flow123'
        ]));

        // Invalid flow button data - missing flow token
        $this->assertFalse(WhatsAppButtonType::FLOW->validateButtonData(['title' => 'Start']));

        // Invalid flow button data - title too long
        $this->assertFalse(WhatsAppButtonType::FLOW->validateButtonData([
            'title' => str_repeat('a', 26),
            'flow_token' => 'flow123'
        ]));
    }

    public function test_to_whatsapp_payload_reply()
    {
        $payload = WhatsAppButtonType::REPLY->toWhatsAppPayload([
            'id' => 'btn1',
            'title' => 'Yes'
        ]);

        $expected = [
            'type' => 'reply',
            'reply' => [
                'id' => 'btn1',
                'title' => 'Yes'
            ]
        ];

        $this->assertEquals($expected, $payload);
    }

    public function test_to_whatsapp_payload_url()
    {
        $payload = WhatsAppButtonType::URL->toWhatsAppPayload([
            'title' => 'Visit',
            'url' => 'https://example.com'
        ]);

        $expected = [
            'type' => 'url',
            'url' => 'https://example.com',
            'text' => 'Visit'
        ];

        $this->assertEquals($expected, $payload);
    }

    public function test_to_whatsapp_payload_phone_number()
    {
        $payload = WhatsAppButtonType::PHONE_NUMBER->toWhatsAppPayload([
            'title' => 'Call',
            'phone_number' => '+5511999999999'
        ]);

        $expected = [
            'type' => 'phone_number',
            'phone_number' => '+5511999999999',
            'text' => 'Call'
        ];

        $this->assertEquals($expected, $payload);
    }

    public function test_from_legacy_type()
    {
        $this->assertEquals(WhatsAppButtonType::REPLY, WhatsAppButtonType::fromLegacyType('QUICK_REPLY'));
        $this->assertEquals(WhatsAppButtonType::URL, WhatsAppButtonType::fromLegacyType('URL'));
        $this->assertEquals(WhatsAppButtonType::PHONE_NUMBER, WhatsAppButtonType::fromLegacyType('PHONE_NUMBER'));
        $this->assertEquals(WhatsAppButtonType::COPY_CODE, WhatsAppButtonType::fromLegacyType('COPY_CODE'));
        $this->assertEquals(WhatsAppButtonType::FLOW, WhatsAppButtonType::fromLegacyType('FLOW'));

        $this->assertNull(WhatsAppButtonType::fromLegacyType('INVALID'));
        $this->assertNull(WhatsAppButtonType::fromLegacyType(''));
    }

    public function test_app_make_works()
    {
        // Test that we can use the enum class directly
        $this->assertInstanceOf(WhatsAppButtonType::class, WhatsAppButtonType::REPLY);

        // Test that enum values work correctly
        $this->assertEquals('reply', WhatsAppButtonType::REPLY->value);
        $this->assertTrue(WhatsAppButtonType::isValid('reply'));
    }

    public function test_all_cases_covered()
    {
        $allCases = WhatsAppButtonType::cases();
        $this->assertCount(5, $allCases);

        $expectedCases = [
            WhatsAppButtonType::REPLY,
            WhatsAppButtonType::URL,
            WhatsAppButtonType::PHONE_NUMBER,
            WhatsAppButtonType::COPY_CODE,
            WhatsAppButtonType::FLOW
        ];

        foreach ($expectedCases as $expectedCase) {
            $this->assertContains($expectedCase, $allCases);
        }
    }
}
