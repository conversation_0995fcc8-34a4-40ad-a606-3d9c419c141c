<?php

namespace Tests\Unit\Enums\ChatBot;

use App\Enums\ChatBot\InteractiveType;
use App\Enums\ChatBot\WhatsAppButtonType;
use PHPUnit\Framework\TestCase;

class InteractiveTypeTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('button', InteractiveType::BUTTON->value);
        $this->assertEquals('list', InteractiveType::LIST->value);
        $this->assertEquals('flow', InteractiveType::FLOW->value);
    }

    public function test_get_all()
    {
        $expected = [
            'button',
            'list',
            'flow'
        ];

        $this->assertEquals($expected, InteractiveType::getAll());
    }

    public function test_get_description()
    {
        $this->assertEquals('Mensagem interativa com até 3 botões de resposta', InteractiveType::BUTTON->getDescription());
        $this->assertEquals('Mensagem interativa com lista de opções selecionáveis', InteractiveType::LIST->getDescription());
        $this->assertEquals('Mensagem interativa que aciona um WhatsApp Flow', InteractiveType::FLOW->getDescription());
    }

    public function test_is_valid()
    {
        $this->assertTrue(InteractiveType::isValid('button'));
        $this->assertTrue(InteractiveType::isValid('list'));
        $this->assertTrue(InteractiveType::isValid('flow'));

        $this->assertFalse(InteractiveType::isValid('invalid'));
        $this->assertFalse(InteractiveType::isValid(''));
        $this->assertFalse(InteractiveType::isValid('BUTTON'));
    }

    public function test_from_string()
    {
        $this->assertEquals(InteractiveType::BUTTON, InteractiveType::fromString('button'));
        $this->assertEquals(InteractiveType::LIST, InteractiveType::fromString('list'));
        $this->assertEquals(InteractiveType::FLOW, InteractiveType::fromString('flow'));

        $this->assertNull(InteractiveType::fromString('invalid'));
        $this->assertNull(InteractiveType::fromString(''));
        $this->assertNull(InteractiveType::fromString('BUTTON'));
    }

    public function test_get_max_options()
    {
        $this->assertEquals(3, InteractiveType::BUTTON->getMaxOptions());
        $this->assertEquals(10, InteractiveType::LIST->getMaxOptions());
        $this->assertEquals(1, InteractiveType::FLOW->getMaxOptions());
    }

    public function test_get_min_options()
    {
        $this->assertEquals(1, InteractiveType::BUTTON->getMinOptions());
        $this->assertEquals(1, InteractiveType::LIST->getMinOptions());
        $this->assertEquals(1, InteractiveType::FLOW->getMinOptions());
    }

    public function test_supports_sections()
    {
        $this->assertFalse(InteractiveType::BUTTON->supportsSections());
        $this->assertTrue(InteractiveType::LIST->supportsSections());
        $this->assertFalse(InteractiveType::FLOW->supportsSections());
    }

    public function test_requires_header()
    {
        $this->assertFalse(InteractiveType::BUTTON->requiresHeader());
        $this->assertTrue(InteractiveType::LIST->requiresHeader());
        $this->assertFalse(InteractiveType::FLOW->requiresHeader());
    }

    public function test_supports_footer()
    {
        $this->assertTrue(InteractiveType::BUTTON->supportsFooter());
        $this->assertTrue(InteractiveType::LIST->supportsFooter());
        $this->assertFalse(InteractiveType::FLOW->supportsFooter());
    }

    public function test_get_max_body_length()
    {
        $this->assertEquals(1024, InteractiveType::BUTTON->getMaxBodyLength());
        $this->assertEquals(1024, InteractiveType::LIST->getMaxBodyLength());
        $this->assertEquals(1024, InteractiveType::FLOW->getMaxBodyLength());
    }

    public function test_get_max_footer_length()
    {
        $this->assertEquals(60, InteractiveType::BUTTON->getMaxFooterLength());
        $this->assertEquals(60, InteractiveType::LIST->getMaxFooterLength());
        $this->assertEquals(0, InteractiveType::FLOW->getMaxFooterLength());
    }

    public function test_validate_message_data_button()
    {
        // Valid button message data
        $this->assertTrue(InteractiveType::BUTTON->validateMessageData([
            'body' => 'Choose an option',
            'footer' => 'Footer text',
            'buttons' => [
                ['id' => '1', 'title' => 'Option 1'],
                ['id' => '2', 'title' => 'Option 2']
            ]
        ]));

        // Invalid - body too long
        $this->assertFalse(InteractiveType::BUTTON->validateMessageData([
            'body' => str_repeat('a', 1025),
            'buttons' => [['id' => '1', 'title' => 'Option 1']]
        ]));

        // Invalid - footer too long
        $this->assertFalse(InteractiveType::BUTTON->validateMessageData([
            'body' => 'Choose an option',
            'footer' => str_repeat('a', 61),
            'buttons' => [['id' => '1', 'title' => 'Option 1']]
        ]));

        // Invalid - no buttons
        $this->assertFalse(InteractiveType::BUTTON->validateMessageData([
            'body' => 'Choose an option',
            'buttons' => []
        ]));

        // Invalid - too many buttons
        $this->assertFalse(InteractiveType::BUTTON->validateMessageData([
            'body' => 'Choose an option',
            'buttons' => [
                ['id' => '1', 'title' => 'Option 1'],
                ['id' => '2', 'title' => 'Option 2'],
                ['id' => '3', 'title' => 'Option 3'],
                ['id' => '4', 'title' => 'Option 4']
            ]
        ]));
    }

    public function test_validate_message_data_list()
    {
        // Valid list message data
        $this->assertTrue(InteractiveType::LIST->validateMessageData([
            'header' => 'Select an option',
            'body' => 'Choose from the list',
            'footer' => 'Footer text',
            'sections' => [
                [
                    'title' => 'Section 1',
                    'rows' => [
                        ['id' => '1', 'title' => 'Option 1'],
                        ['id' => '2', 'title' => 'Option 2']
                    ]
                ]
            ]
        ]));

        // Invalid - missing header
        $this->assertFalse(InteractiveType::LIST->validateMessageData([
            'body' => 'Choose from the list',
            'sections' => [
                [
                    'title' => 'Section 1',
                    'rows' => [['id' => '1', 'title' => 'Option 1']]
                ]
            ]
        ]));

        // Invalid - no sections/rows
        $this->assertFalse(InteractiveType::LIST->validateMessageData([
            'header' => 'Select an option',
            'body' => 'Choose from the list',
            'sections' => []
        ]));
    }

    public function test_validate_message_data_flow()
    {
        // Valid flow message data
        $this->assertTrue(InteractiveType::FLOW->validateMessageData([
            'body' => 'Start the flow',
            'flow_parameters' => ['param1' => 'value1']
        ]));

        // Invalid - body too long
        $this->assertFalse(InteractiveType::FLOW->validateMessageData([
            'body' => str_repeat('a', 1025),
            'flow_parameters' => ['param1' => 'value1']
        ]));

        // Valid - footer should be ignored for flow
        $this->assertTrue(InteractiveType::FLOW->validateMessageData([
            'body' => 'Start the flow',
            'footer' => 'This footer will be ignored',
            'flow_parameters' => ['param1' => 'value1']
        ]));
    }

    public function test_to_whatsapp_payload_button()
    {
        $data = [
            'body' => 'Choose an option',
            'footer' => 'Footer text',
            'buttons' => [
                ['id' => '1', 'title' => 'Option 1'],
                ['id' => '2', 'title' => 'Option 2']
            ]
        ];

        $payload = InteractiveType::BUTTON->toWhatsAppPayload($data);

        $expected = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => ['text' => 'Choose an option'],
                'footer' => ['text' => 'Footer text'],
                'action' => [
                    'buttons' => [
                        ['id' => '1', 'title' => 'Option 1'],
                        ['id' => '2', 'title' => 'Option 2']
                    ]
                ]
            ]
        ];

        $this->assertEquals($expected, $payload);
    }

    public function test_to_whatsapp_payload_list()
    {
        $data = [
            'header' => 'Select an option',
            'body' => 'Choose from the list',
            'footer' => 'Footer text',
            'button_text' => 'View options',
            'sections' => [
                [
                    'title' => 'Section 1',
                    'rows' => [
                        ['id' => '1', 'title' => 'Option 1'],
                        ['id' => '2', 'title' => 'Option 2']
                    ]
                ]
            ]
        ];

        $payload = InteractiveType::LIST->toWhatsAppPayload($data);

        $expected = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'header' => ['type' => 'text', 'text' => 'Select an option'],
                'body' => ['text' => 'Choose from the list'],
                'footer' => ['text' => 'Footer text'],
                'action' => [
                    'button' => 'View options',
                    'sections' => [
                        [
                            'title' => 'Section 1',
                            'rows' => [
                                ['id' => '1', 'title' => 'Option 1'],
                                ['id' => '2', 'title' => 'Option 2']
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->assertEquals($expected, $payload);
    }

    public function test_get_compatible_button_types()
    {
        $buttonCompatible = InteractiveType::BUTTON->getCompatibleButtonTypes();
        $this->assertContains(WhatsAppButtonType::REPLY, $buttonCompatible);
        $this->assertCount(1, $buttonCompatible);

        $listCompatible = InteractiveType::LIST->getCompatibleButtonTypes();
        $this->assertEmpty($listCompatible);

        $flowCompatible = InteractiveType::FLOW->getCompatibleButtonTypes();
        $this->assertContains(WhatsAppButtonType::FLOW, $flowCompatible);
        $this->assertCount(1, $flowCompatible);
    }

    public function test_is_compatible_with()
    {
        $this->assertTrue(InteractiveType::BUTTON->isCompatibleWith(WhatsAppButtonType::REPLY));
        $this->assertFalse(InteractiveType::BUTTON->isCompatibleWith(WhatsAppButtonType::URL));
        $this->assertFalse(InteractiveType::BUTTON->isCompatibleWith(WhatsAppButtonType::FLOW));

        $this->assertFalse(InteractiveType::LIST->isCompatibleWith(WhatsAppButtonType::REPLY));
        $this->assertFalse(InteractiveType::LIST->isCompatibleWith(WhatsAppButtonType::URL));

        $this->assertTrue(InteractiveType::FLOW->isCompatibleWith(WhatsAppButtonType::FLOW));
        $this->assertFalse(InteractiveType::FLOW->isCompatibleWith(WhatsAppButtonType::REPLY));
    }

    public function test_app_make_works()
    {
        // Test that we can use the enum class directly
        $this->assertInstanceOf(InteractiveType::class, InteractiveType::BUTTON);

        // Test that enum values work correctly
        $this->assertEquals('button', InteractiveType::BUTTON->value);
        $this->assertTrue(InteractiveType::isValid('button'));
    }

    public function test_all_cases_covered()
    {
        $allCases = InteractiveType::cases();
        $this->assertCount(3, $allCases);

        $expectedCases = [
            InteractiveType::BUTTON,
            InteractiveType::LIST,
            InteractiveType::FLOW
        ];

        foreach ($expectedCases as $expectedCase) {
            $this->assertContains($expectedCase, $allCases);
        }
    }
}
