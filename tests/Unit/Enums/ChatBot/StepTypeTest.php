<?php

namespace Tests\Unit\Enums\ChatBot;

use App\Enums\ChatBot\StepType;
use PHPUnit\Framework\TestCase;

class StepTypeTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('message', StepType::MESSAGE->value);
        $this->assertEquals('interactive', StepType::INTERACTIVE->value);
        $this->assertEquals('input', StepType::INPUT->value);
        $this->assertEquals('command', StepType::COMMAND->value);
        $this->assertEquals('condition', StepType::CONDITION->value);
        $this->assertEquals('webhook', StepType::WEBHOOK->value);
        $this->assertEquals('delay', StepType::DELAY->value);
    }

    public function test_get_all()
    {
        $expected = [
            'message',
            'interactive',
            'input',
            'command',
            'condition',
            'webhook',
            'delay'
        ];

        $this->assertEquals($expected, StepType::getAll());
    }

    public function test_get_description()
    {
        $this->assertEquals('Exibir mensagem e avançar automaticamente', StepType::MESSAGE->getDescription());
        $this->assertEquals('Mostrar botões/listas e aguardar seleção', StepType::INTERACTIVE->getDescription());
        $this->assertEquals('Solicitar entrada de texto, validar e armazenar', StepType::INPUT->getDescription());
        $this->assertEquals('Executar lógica de negócio (pedidos, cálculos)', StepType::COMMAND->getDescription());
        $this->assertEquals('Navegação condicional baseada na entrada do usuário', StepType::CONDITION->getDescription());
        $this->assertEquals('Chamar APIs ou serviços externos', StepType::WEBHOOK->getDescription());
        $this->assertEquals('Aguardar tempo especificado antes de continuar', StepType::DELAY->getDescription());
    }

    public function test_is_valid()
    {
        $this->assertTrue(StepType::isValid('message'));
        $this->assertTrue(StepType::isValid('interactive'));
        $this->assertTrue(StepType::isValid('input'));
        $this->assertTrue(StepType::isValid('command'));
        $this->assertTrue(StepType::isValid('condition'));
        $this->assertTrue(StepType::isValid('webhook'));
        $this->assertTrue(StepType::isValid('delay'));

        $this->assertFalse(StepType::isValid('invalid'));
        $this->assertFalse(StepType::isValid(''));
        $this->assertFalse(StepType::isValid('MESSAGE'));
    }

    public function test_from_string()
    {
        $this->assertEquals(StepType::MESSAGE, StepType::fromString('message'));
        $this->assertEquals(StepType::INTERACTIVE, StepType::fromString('interactive'));
        $this->assertEquals(StepType::INPUT, StepType::fromString('input'));
        $this->assertEquals(StepType::COMMAND, StepType::fromString('command'));
        $this->assertEquals(StepType::CONDITION, StepType::fromString('condition'));
        $this->assertEquals(StepType::WEBHOOK, StepType::fromString('webhook'));
        $this->assertEquals(StepType::DELAY, StepType::fromString('delay'));

        $this->assertNull(StepType::fromString('invalid'));
        $this->assertNull(StepType::fromString(''));
        $this->assertNull(StepType::fromString('MESSAGE'));
    }

    public function test_requires_user_interaction()
    {
        $this->assertTrue(StepType::INTERACTIVE->requiresUserInteraction());
        $this->assertTrue(StepType::INPUT->requiresUserInteraction());

        $this->assertFalse(StepType::MESSAGE->requiresUserInteraction());
        $this->assertFalse(StepType::COMMAND->requiresUserInteraction());
        $this->assertFalse(StepType::CONDITION->requiresUserInteraction());
        $this->assertFalse(StepType::WEBHOOK->requiresUserInteraction());
        $this->assertFalse(StepType::DELAY->requiresUserInteraction());
    }

    public function test_can_auto_advance()
    {
        $this->assertTrue(StepType::MESSAGE->canAutoAdvance());
        $this->assertTrue(StepType::COMMAND->canAutoAdvance());
        $this->assertTrue(StepType::WEBHOOK->canAutoAdvance());
        $this->assertTrue(StepType::DELAY->canAutoAdvance());

        $this->assertFalse(StepType::INTERACTIVE->canAutoAdvance());
        $this->assertFalse(StepType::INPUT->canAutoAdvance());
        $this->assertFalse(StepType::CONDITION->canAutoAdvance());
    }

    public function test_get_interactive_types()
    {
        $interactiveTypes = StepType::getInteractiveTypes();

        $this->assertContains(StepType::INTERACTIVE, $interactiveTypes);
        $this->assertContains(StepType::INPUT, $interactiveTypes);
        $this->assertNotContains(StepType::MESSAGE, $interactiveTypes);
        $this->assertNotContains(StepType::COMMAND, $interactiveTypes);
        $this->assertNotContains(StepType::CONDITION, $interactiveTypes);
        $this->assertNotContains(StepType::WEBHOOK, $interactiveTypes);
        $this->assertNotContains(StepType::DELAY, $interactiveTypes);
    }

    public function test_get_auto_advance_types()
    {
        $autoAdvanceTypes = StepType::getAutoAdvanceTypes();

        $this->assertContains(StepType::MESSAGE, $autoAdvanceTypes);
        $this->assertContains(StepType::COMMAND, $autoAdvanceTypes);
        $this->assertContains(StepType::WEBHOOK, $autoAdvanceTypes);
        $this->assertContains(StepType::DELAY, $autoAdvanceTypes);
        $this->assertNotContains(StepType::INTERACTIVE, $autoAdvanceTypes);
        $this->assertNotContains(StepType::INPUT, $autoAdvanceTypes);
        $this->assertNotContains(StepType::CONDITION, $autoAdvanceTypes);
    }

    public function test_app_make_works()
    {
        // Test that we can use the enum class directly
        $this->assertInstanceOf(StepType::class, StepType::MESSAGE);

        // Test that enum values work correctly
        $this->assertEquals('message', StepType::MESSAGE->value);
        $this->assertTrue(StepType::isValid('message'));
    }

    public function test_all_cases_covered()
    {
        $allCases = StepType::cases();
        $this->assertCount(7, $allCases);

        $expectedCases = [
            StepType::MESSAGE,
            StepType::INTERACTIVE,
            StepType::INPUT,
            StepType::COMMAND,
            StepType::CONDITION,
            StepType::WEBHOOK,
            StepType::DELAY
        ];

        foreach ($expectedCases as $expectedCase) {
            $this->assertContains($expectedCase, $allCases);
        }
    }
}
