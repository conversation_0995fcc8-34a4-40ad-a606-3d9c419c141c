<?php

namespace Tests\Unit\Enums;

use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class StepTypeTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('message', StepType::MESSAGE->value);
        $this->assertEquals('interactive', StepType::INTERACTIVE->value);
        $this->assertEquals('input', StepType::INPUT->value);
        $this->assertEquals('command', StepType::COMMAND->value);
        $this->assertEquals('condition', StepType::CONDITION->value);
        $this->assertEquals('webhook', StepType::WEBHOOK->value);
        $this->assertEquals('delay', StepType::DELAY->value);
    }

    public function test_get_values()
    {
        $values = StepType::getValues();
        
        $this->assertIsArray($values);
        $this->assertContains('message', $values);
        $this->assertContains('interactive', $values);
        $this->assertContains('input', $values);
        $this->assertContains('command', $values);
        $this->assertContains('condition', $values);
        $this->assertContains('webhook', $values);
        $this->assertContains('delay', $values);
    }

    public function test_from_string()
    {
        $this->assertEquals(StepType::MESSAGE, StepType::fromString('message'));
        $this->assertEquals(StepType::INTERACTIVE, StepType::fromString('interactive'));
        $this->assertEquals(StepType::INPUT, StepType::fromString('input'));
        $this->assertNull(StepType::fromString('invalid'));
    }

    public function test_requires_configuration()
    {
        $this->assertFalse(StepType::MESSAGE->requiresConfiguration());
        $this->assertTrue(StepType::INTERACTIVE->requiresConfiguration());
        $this->assertTrue(StepType::INPUT->requiresConfiguration());
        $this->assertFalse(StepType::COMMAND->requiresConfiguration());
        $this->assertTrue(StepType::CONDITION->requiresConfiguration());
        $this->assertTrue(StepType::WEBHOOK->requiresConfiguration());
        $this->assertTrue(StepType::DELAY->requiresConfiguration());
    }

    public function test_supports_navigation_rules()
    {
        $this->assertFalse(StepType::MESSAGE->supportsNavigationRules());
        $this->assertTrue(StepType::INTERACTIVE->supportsNavigationRules());
        $this->assertFalse(StepType::INPUT->supportsNavigationRules());
        $this->assertFalse(StepType::COMMAND->supportsNavigationRules());
        $this->assertTrue(StepType::CONDITION->supportsNavigationRules());
        $this->assertFalse(StepType::WEBHOOK->supportsNavigationRules());
        $this->assertFalse(StepType::DELAY->supportsNavigationRules());
    }

    public function test_get_default_configuration()
    {
        $messageConfig = StepType::MESSAGE->getDefaultConfiguration();
        $this->assertArrayHasKey('text', $messageConfig);
        $this->assertArrayHasKey('media_type', $messageConfig);

        $interactiveConfig = StepType::INTERACTIVE->getDefaultConfiguration();
        $this->assertArrayHasKey('text', $interactiveConfig);
        $this->assertArrayHasKey('buttons', $interactiveConfig);
        $this->assertArrayHasKey('interaction_type', $interactiveConfig);

        $inputConfig = StepType::INPUT->getDefaultConfiguration();
        $this->assertArrayHasKey('prompt', $inputConfig);
        $this->assertArrayHasKey('field_mapping', $inputConfig);

        $conditionConfig = StepType::CONDITION->getDefaultConfiguration();
        $this->assertArrayHasKey('conditions', $conditionConfig);

        $webhookConfig = StepType::WEBHOOK->getDefaultConfiguration();
        $this->assertArrayHasKey('url', $webhookConfig);
        $this->assertArrayHasKey('method', $webhookConfig);

        $delayConfig = StepType::DELAY->getDefaultConfiguration();
        $this->assertArrayHasKey('duration_seconds', $delayConfig);
    }

    public function test_validate_configuration()
    {
        // Test MESSAGE validation
        $this->assertTrue(StepType::MESSAGE->validateConfiguration(['text' => 'Hello']));
        $this->assertFalse(StepType::MESSAGE->validateConfiguration([]));

        // Test INTERACTIVE validation
        $this->assertTrue(StepType::INTERACTIVE->validateConfiguration([
            'text' => 'Choose option',
            'buttons' => [['text' => 'Yes'], ['text' => 'No']]
        ]));
        $this->assertFalse(StepType::INTERACTIVE->validateConfiguration(['text' => 'Choose option']));

        // Test INPUT validation
        $this->assertTrue(StepType::INPUT->validateConfiguration([
            'prompt' => 'Enter name',
            'field_mapping' => 'client.name'
        ]));
        $this->assertFalse(StepType::INPUT->validateConfiguration(['prompt' => 'Enter name']));

        // Test COMMAND validation
        $this->assertTrue(StepType::COMMAND->validateConfiguration(['command' => 'start']));
        $this->assertFalse(StepType::COMMAND->validateConfiguration([]));

        // Test CONDITION validation
        $this->assertTrue(StepType::CONDITION->validateConfiguration(['conditions' => []]));
        $this->assertFalse(StepType::CONDITION->validateConfiguration([]));

        // Test WEBHOOK validation
        $this->assertTrue(StepType::WEBHOOK->validateConfiguration(['url' => 'https://example.com']));
        $this->assertFalse(StepType::WEBHOOK->validateConfiguration(['url' => 'invalid-url']));

        // Test DELAY validation
        $this->assertTrue(StepType::DELAY->validateConfiguration(['duration_seconds' => 30]));
        $this->assertFalse(StepType::DELAY->validateConfiguration(['duration_seconds' => 'invalid']));
    }
}
