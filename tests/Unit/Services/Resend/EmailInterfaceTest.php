<?php

namespace Tests\Unit\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\Domains\CampaignReportEmail;
use App\Services\Resend\Domains\WelcomeEmail;
use App\Services\Resend\Domains\InvoiceEmail;
use App\Domains\User;
use App\Domains\Organization;
use App\Domains\ChatBot\Campaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class EmailInterfaceTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null,
            Carbon::now(),
            Carbon::now()
        );

        $this->user = new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $this->organization
        );

        $this->campaign = new Campaign(
            1,
            1,
            1,
            1,
            1,
            'Test Campaign',
            'Test Description'
        );
    }

    public function test_password_reset_email_implements_interface()
    {
        $email = new PasswordResetEmail($this->user, 'test-token', 60);

        $this->assertIsArray($email->getTo());
        $this->assertEquals('<EMAIL>', $email->getTo()['email']);
        $this->assertEquals('John Doe', $email->getTo()['name']);

        $this->assertIsString($email->getFrom());
        $this->assertIsString($email->getSubject());
        $this->assertEquals('emails.password-reset', $email->getTemplatePath());

        $templateData = $email->getTemplateData();
        $this->assertIsArray($templateData);
        $this->assertArrayHasKey('user', $templateData);
        $this->assertArrayHasKey('token', $templateData);
        $this->assertArrayHasKey('reset_url', $templateData);

        $tags = $email->getTags();
        $this->assertIsArray($tags);
        $this->assertEquals('auth', $tags['type']);
        $this->assertEquals('high', $tags['priority']);

        $payload = $email->toResendPayload();
        $this->assertIsArray($payload);
        $this->assertArrayHasKey('from', $payload);
        $this->assertArrayHasKey('to', $payload);
        $this->assertArrayHasKey('subject', $payload);
        $this->assertArrayHasKey('html', $payload);
    }

    public function test_campaign_report_email_implements_interface()
    {
        $metrics = [
            'Mensagens Enviadas' => 1500,
            'Taxa de Entrega' => '98.5%',
            'Respostas Recebidas' => 45
        ];

        $email = new CampaignReportEmail(
            $this->campaign,
            $metrics,
            '01/01/2024 - 31/01/2024',
            $this->organization,
            $this->user
        );

        $this->assertIsArray($email->getTo());
        $this->assertEquals('<EMAIL>', $email->getTo()['email']);

        $this->assertIsString($email->getSubject());
        $this->assertStringContainsString('Test Campaign', $email->getSubject());

        $this->assertEquals('emails.campaign-report', $email->getTemplatePath());

        $templateData = $email->getTemplateData();
        $this->assertArrayHasKey('campaign', $templateData);
        $this->assertArrayHasKey('metrics', $templateData);
        $this->assertArrayHasKey('period', $templateData);

        $tags = $email->getTags();
        $this->assertEquals('report', $tags['type']);
        $this->assertEquals('medium', $tags['priority']);
        $this->assertEquals(1, $tags['campaign_id']);
    }

    public function test_welcome_email_implements_interface()
    {
        $email = new WelcomeEmail($this->user, $this->organization);

        $this->assertIsArray($email->getTo());
        $this->assertEquals('<EMAIL>', $email->getTo()['email']);

        $this->assertStringContainsString('Bem-vindo', $email->getSubject());
        $this->assertEquals('emails.welcome', $email->getTemplatePath());

        $templateData = $email->getTemplateData();
        $this->assertArrayHasKey('user', $templateData);
        $this->assertArrayHasKey('organization', $templateData);
        $this->assertArrayHasKey('next_steps', $templateData);
        $this->assertIsArray($templateData['next_steps']);

        $tags = $email->getTags();
        $this->assertEquals('onboarding', $tags['type']);
        $this->assertEquals('medium', $tags['priority']);
    }

    public function test_invoice_email_implements_interface()
    {
        $invoice = (object) ['id' => 123];
        $client = (object) ['name' => 'Test Client', 'email' => '<EMAIL>'];
        $dueDate = Carbon::now()->addDays(30);
        $paymentLink = 'https://example.com/pay/123';
        $items = [
            ['description' => 'Service 1', 'quantity' => 1, 'unit_price' => 100.00, 'total' => 100.00]
        ];

        $email = new InvoiceEmail(
            $invoice,
            $client,
            $dueDate,
            $paymentLink,
            $items,
            100.00,
            $this->organization
        );

        $this->assertIsArray($email->getTo());
        $this->assertEquals('<EMAIL>', $email->getTo()['email']);
        $this->assertEquals('Test Client', $email->getTo()['name']);

        $this->assertStringContainsString('Fatura #123', $email->getSubject());
        $this->assertEquals('emails.invoice', $email->getTemplatePath());

        $templateData = $email->getTemplateData();
        $this->assertArrayHasKey('invoice', $templateData);
        $this->assertArrayHasKey('client', $templateData);
        $this->assertArrayHasKey('due_date', $templateData);
        $this->assertArrayHasKey('payment_link', $templateData);

        $tags = $email->getTags();
        $this->assertEquals('billing', $tags['type']);
        $this->assertEquals('high', $tags['priority']);
        $this->assertEquals(123, $tags['invoice_id']);
    }

    public function test_all_emails_return_valid_payload_structure()
    {
        $emails = [
            new PasswordResetEmail($this->user, 'test-token'),
            new CampaignReportEmail($this->campaign, [], 'test-period', $this->organization, $this->user),
            new WelcomeEmail($this->user, $this->organization),
            new InvoiceEmail(
                (object) ['id' => 1],
                (object) ['name' => 'Test', 'email' => '<EMAIL>'],
                Carbon::now(),
                'https://example.com/pay',
                [],
                0.0,
                $this->organization
            )
        ];

        foreach ($emails as $email) {
            $payload = $email->toResendPayload();

            $this->assertIsArray($payload);
            $this->assertArrayHasKey('from', $payload);
            $this->assertArrayHasKey('to', $payload);
            $this->assertArrayHasKey('subject', $payload);
            $this->assertArrayHasKey('html', $payload);
            $this->assertArrayHasKey('tags', $payload);

            $this->assertIsString($payload['from']);
            $this->assertIsArray($payload['to']);
            $this->assertIsString($payload['subject']);
            $this->assertIsString($payload['html']);
            $this->assertIsArray($payload['tags']);

            // Validate email format
            $this->assertStringContainsString('@', $payload['from']);
            $this->assertNotEmpty($payload['to']);
            $this->assertStringContainsString('@', $payload['to'][0]['email']);
        }
    }
}
