<?php

namespace Tests\Unit\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\UseCases\Send;
use App\Services\Resend\ResendService;
use App\Services\Resend\Contracts\EmailInterface;
use App\Services\Resend\Exceptions\ResendException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\View;
use Mockery;

class SendUseCaseTest extends TestCase
{
    use RefreshDatabase;

    private Send $useCase;
    private ResendService $mockResendService;
    private EmailInterface $mockEmail;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResendService = Mockery::mock(ResendService::class);
        $this->useCase = new Send($this->mockResendService);

        $this->mockEmail = Mockery::mock(EmailInterface::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_sends_email_successfully()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
        $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('emails.test');
        $this->mockEmail->shouldReceive('getTags')->andReturn(['type' => 'test']);

        View::shouldReceive('exists')->with('emails.test')->andReturn(true);

        $expectedResult = ['id' => 'test-email-id', 'status' => 'sent'];
        $this->mockResendService->shouldReceive('send')->with($this->mockEmail)->andReturn($expectedResult);

        $result = $this->useCase->perform($this->mockEmail);

        $this->assertEquals($expectedResult, $result);
    }

    public function test_perform_validates_empty_recipient_email()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '', 'name' => 'Test User']);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Recipient email is required');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_validates_invalid_email_format()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => 'invalid-email', 'name' => 'Test User']);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Invalid recipient email format');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_validates_empty_subject()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('');

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Email subject is required');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_validates_empty_template_path()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
        $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('');

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Email template path is required');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_validates_template_exists()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
        $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('emails.nonexistent');

        View::shouldReceive('exists')->with('emails.nonexistent')->andReturn(false);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Email template does not exist: emails.nonexistent');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_handles_resend_service_exception()
    {
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
        $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('emails.test');
        $this->mockEmail->shouldReceive('getTags')->andReturn(['type' => 'test']);

        View::shouldReceive('exists')->with('emails.test')->andReturn(true);

        $exception = new ResendException('Service error');
        $this->mockResendService->shouldReceive('send')->with($this->mockEmail)->andThrow($exception);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Service error');

        $this->useCase->perform($this->mockEmail);
    }

    public function test_perform_validates_valid_email_formats()
    {
        $validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($validEmails as $email) {
            $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => $email, 'name' => 'Test User']);
            $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
            $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('emails.test');
            $this->mockEmail->shouldReceive('getTags')->andReturn(['type' => 'test']);

            View::shouldReceive('exists')->with('emails.test')->andReturn(true);

            $this->mockResendService->shouldReceive('send')->with($this->mockEmail)->andReturn(['id' => 'test-id']);

            $result = $this->useCase->perform($this->mockEmail);
            $this->assertIsArray($result);
        }
    }

    public function test_perform_validates_invalid_email_formats()
    {
        $invalidEmails = [
            'invalid-email',
            '@example.com',
            'test@',
            '<EMAIL>',
            'test@.com',
            'test@example.'
        ];

        foreach ($invalidEmails as $email) {
            $mockEmail = Mockery::mock(EmailInterface::class);
            $mockEmail->shouldReceive('getTo')->andReturn(['email' => $email, 'name' => 'Test User']);

            try {
                $this->useCase->perform($mockEmail);
                $this->fail('Expected ResendException was not thrown for email: ' . $email);
            } catch (ResendException $e) {
                $this->assertStringContainsString('email', strtolower($e->getMessage()));
            }
        }
    }
}
