<?php

namespace Tests\Unit\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\ResendService;
use App\Services\Resend\Contracts\EmailInterface;
use App\Services\Resend\Exceptions\ResendException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Mockery;

class ResendServiceTest extends TestCase
{
    use RefreshDatabase;

    private ResendService $service;
    private EmailInterface $mockEmail;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('resend.api_key', 'test-api-key');
        Config::set('resend.api_url', 'https://api.resend.com');
        Config::set('resend.timeout', 30);
        Config::set('resend.retry_attempts', 3);
        Config::set('resend.sandbox_mode', false);

        $this->service = new ResendService();

        $this->mockEmail = Mockery::mock(EmailInterface::class);
        $this->mockEmail->shouldReceive('toResendPayload')->andReturn([
            'from' => '<EMAIL>',
            'to' => [['email' => '<EMAIL>', 'name' => 'Test User']],
            'subject' => 'Test Subject',
            'html' => '<p>Test content</p>',
            'tags' => ['type' => 'test']
        ]);
        $this->mockEmail->shouldReceive('getTo')->andReturn(['email' => '<EMAIL>', 'name' => 'Test User']);
        $this->mockEmail->shouldReceive('getSubject')->andReturn('Test Subject');
        $this->mockEmail->shouldReceive('getTemplatePath')->andReturn('emails.test');
        $this->mockEmail->shouldReceive('getTags')->andReturn(['type' => 'test']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_constructor_throws_exception_when_api_key_missing()
    {
        Config::set('resend.api_key', null);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend API key is not configured');

        new ResendService();
    }

    public function test_send_email_success()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'id' => 'test-email-id',
                'from' => '<EMAIL>',
                'to' => ['<EMAIL>'],
                'created_at' => '2024-01-01T00:00:00Z'
            ], 200)
        ]);

        // Create service after HTTP fake is set up
        $service = new ResendService();
        $result = $service->send($this->mockEmail);

        $this->assertIsArray($result);
        $this->assertEquals('test-email-id', $result['id']);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.resend.com/emails' &&
                   $request->hasHeader('Authorization', 'Bearer test-api-key') &&
                   $request->hasHeader('Content-Type', 'application/json');
        });
    }

    public function test_send_email_with_sandbox_mode()
    {
        Config::set('resend.sandbox_mode', true);
        $service = app()->make(ResendService::class);

        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $service->send($this->mockEmail);

        Http::assertSent(function ($request) {
            $data = $request->data();
            return isset($data['to']) && $data['to'][0]['email'] === '<EMAIL>';
        });
    }

    public function test_send_email_handles_authentication_error()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Invalid API key'
            ], 401)
        ]);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend authentication failed');

        $this->service->send($this->mockEmail);
    }

    public function test_send_email_handles_bad_request()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Invalid email format'
            ], 400)
        ]);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend API bad request: Invalid email format');

        $this->service->send($this->mockEmail);
    }

    public function test_send_email_handles_rate_limit()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Rate limit exceeded'
            ], 429)
        ]);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend API rate limit exceeded');

        $this->service->send($this->mockEmail);
    }

    public function test_send_email_handles_server_error()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Internal server error'
            ], 500)
        ]);

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend server error: Internal server error');

        $this->service->send($this->mockEmail);
    }

    public function test_send_email_handles_network_timeout()
    {
        Http::fake(function () {
            throw new \Illuminate\Http\Client\ConnectionException('Connection timeout');
        });

        $this->expectException(ResendException::class);

        $this->service->send($this->mockEmail);
    }

    public function test_send_email_includes_correct_headers()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $this->service->send($this->mockEmail);

        Http::assertSent(function ($request) {
            return $request->hasHeader('Authorization', 'Bearer test-api-key') &&
                   $request->hasHeader('Content-Type', 'application/json');
        });
    }

    public function test_send_email_sends_correct_payload()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $this->service->send($this->mockEmail);

        Http::assertSent(function ($request) {
            $data = $request->data();
            return $data['from'] === '<EMAIL>' &&
                   $data['subject'] === 'Test Subject' &&
                   isset($data['html']) &&
                   isset($data['tags']);
        });
    }
}
