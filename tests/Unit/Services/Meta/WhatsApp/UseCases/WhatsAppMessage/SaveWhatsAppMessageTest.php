<?php

namespace Tests\Unit\Services\Meta\WhatsApp\UseCases\WhatsAppMessage;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage\SaveWhatsAppMessage;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use Illuminate\Support\Facades\DB;

class SaveWhatsAppMessageTest extends TestCase
{
    private SaveWhatsAppMessage $useCase;
    private WhatsAppMessageRepository $repository;
    private WhatsAppMessageFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = $this->createMock(WhatsAppMessageRepository::class);
        $this->factory = $this->createMock(WhatsAppMessageFactory::class);
        $this->useCase = new SaveWhatsAppMessage($this->repository, $this->factory);
    }

    public function test_perform_saves_whatsapp_message_successfully()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+5579991514957',
                    'wa_id' => '557991514957'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==',
                    'message_status' => 'accepted'
                ]
            ]
        ];

        $whatsAppMessage = $this->createMock(WhatsAppMessage::class);
        $savedWhatsAppMessage = $this->createMock(WhatsAppMessage::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->factory
            ->expects($this->once())
            ->method('buildFromMetaApiResponse')
            ->with($messageId, $whatsappResponse)
            ->willReturn($whatsAppMessage);

        $this->repository
            ->expects($this->once())
            ->method('store')
            ->with($whatsAppMessage)
            ->willReturn($savedWhatsAppMessage);

        // Act
        $result = $this->useCase->perform($messageId, $whatsappResponse);

        // Assert
        $this->assertEquals($savedWhatsAppMessage, $result);
    }

    public function test_perform_rolls_back_transaction_on_exception()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [],
            'messages' => []
        ];

        $whatsAppMessage = $this->createMock(WhatsAppMessage::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $this->factory
            ->expects($this->once())
            ->method('buildFromMetaApiResponse')
            ->with($messageId, $whatsappResponse)
            ->willReturn($whatsAppMessage);

        $this->repository
            ->expects($this->once())
            ->method('store')
            ->with($whatsAppMessage)
            ->willThrowException(new \Exception('Database error'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($messageId, $whatsappResponse);
    }

    public function test_perform_handles_factory_exception()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [];

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $this->factory
            ->expects($this->once())
            ->method('buildFromMetaApiResponse')
            ->with($messageId, $whatsappResponse)
            ->willThrowException(new \Exception('Factory error'));

        $this->repository
            ->expects($this->never())
            ->method('store');

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($messageId, $whatsappResponse);
    }

    public function test_perform_with_minimal_whatsapp_response()
    {
        // Arrange
        $messageId = 456;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp'
        ];

        $whatsAppMessage = $this->createMock(WhatsAppMessage::class);
        $savedWhatsAppMessage = $this->createMock(WhatsAppMessage::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->factory
            ->expects($this->once())
            ->method('buildFromMetaApiResponse')
            ->with($messageId, $whatsappResponse)
            ->willReturn($whatsAppMessage);

        $this->repository
            ->expects($this->once())
            ->method('store')
            ->with($whatsAppMessage)
            ->willReturn($savedWhatsAppMessage);

        // Act
        $result = $this->useCase->perform($messageId, $whatsappResponse);

        // Assert
        $this->assertEquals($savedWhatsAppMessage, $result);
    }

    public function test_perform_with_complete_whatsapp_response()
    {
        // Arrange
        $messageId = 789;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+5511999999999',
                    'wa_id' => '5511999999999'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.COMPLETE_MESSAGE_ID',
                    'message_status' => 'delivered'
                ]
            ],
            'meta' => [
                'api_status' => 'stable',
                'version' => 'v17.0'
            ]
        ];

        $whatsAppMessage = $this->createMock(WhatsAppMessage::class);
        $savedWhatsAppMessage = $this->createMock(WhatsAppMessage::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->factory
            ->expects($this->once())
            ->method('buildFromMetaApiResponse')
            ->with($messageId, $whatsappResponse)
            ->willReturn($whatsAppMessage);

        $this->repository
            ->expects($this->once())
            ->method('store')
            ->with($whatsAppMessage)
            ->willReturn($savedWhatsAppMessage);

        // Act
        $result = $this->useCase->perform($messageId, $whatsappResponse);

        // Assert
        $this->assertEquals($savedWhatsAppMessage, $result);
    }
}
