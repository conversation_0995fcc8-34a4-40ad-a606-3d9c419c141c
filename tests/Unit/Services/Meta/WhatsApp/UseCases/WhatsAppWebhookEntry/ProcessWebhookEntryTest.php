<?php

namespace Tests\Unit\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry\ProcessWebhookEntry;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppWebhookEntryRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppWebhookEntryFactory;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Domains\WhatsAppWebhookEntry;
use Illuminate\Support\Facades\DB;

class ProcessWebhookEntryTest extends TestCase
{
    private ProcessWebhookEntry $useCase;
    private WhatsAppWebhookEntryRepository $webhookEntryRepository;
    private WhatsAppWebhookEntryFactory $webhookEntryFactory;
    private WhatsAppMessageRepository $messageRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->webhookEntryRepository = $this->createMock(WhatsAppWebhookEntryRepository::class);
        $this->webhookEntryFactory = $this->createMock(WhatsAppWebhookEntryFactory::class);
        $this->messageRepository = $this->createMock(WhatsAppMessageRepository::class);

        $this->useCase = new ProcessWebhookEntry(
            $this->webhookEntryRepository,
            $this->webhookEntryFactory,
            $this->messageRepository
        );
    }

    public function test_perform_processes_webhook_entry_successfully()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.HBgMNTU5...',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999',
                                        'conversation' => [
                                            'id' => 'conversation_id',
                                            'origin' => ['type' => 'user_initiated']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $whatsAppMessage = $this->createMock(WhatsAppMessage::class);
        $whatsAppMessage->id = 123;

        $webhookEntryDomain = $this->createMock(WhatsAppWebhookEntry::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->messageRepository
            ->expects($this->once())
            ->method('fetchByExternalWamId')
            ->with('wamid.HBgMNTU5...')
            ->willReturn($whatsAppMessage);

        $this->webhookEntryFactory
            ->expects($this->once())
            ->method('buildFromWebhookStatus')
            ->with(123, $webhookEntry['entry'][0]['changes'][0]['value']['statuses'][0], $webhookEntry)
            ->willReturn($webhookEntryDomain);

        $this->webhookEntryRepository
            ->expects($this->once())
            ->method('store')
            ->with($webhookEntryDomain)
            ->willReturn($webhookEntryDomain);

        // Act
        $result = $this->useCase->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['processed_entries']);
        $this->assertCount(1, $result['entries']);
        $this->assertEquals($webhookEntryDomain, $result['entries'][0]);
    }

    public function test_perform_handles_missing_whatsapp_message()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.NONEXISTENT',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->messageRepository
            ->expects($this->once())
            ->method('fetchByExternalWamId')
            ->with('wamid.NONEXISTENT')
            ->willReturn(null);

        $this->webhookEntryFactory
            ->expects($this->never())
            ->method('buildFromWebhookStatus');

        $this->webhookEntryRepository
            ->expects($this->never())
            ->method('store');

        // Act
        $result = $this->useCase->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['processed_entries']);
        $this->assertEmpty($result['entries']);
    }

    public function test_perform_handles_missing_status_id()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                        // Missing 'id' field
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->messageRepository
            ->expects($this->never())
            ->method('fetchByExternalWamId');

        // Act
        $result = $this->useCase->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['processed_entries']);
        $this->assertEmpty($result['entries']);
    }

    public function test_perform_handles_multiple_status_updates()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.FIRST',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ],
                                    [
                                        'id' => 'wamid.SECOND',
                                        'status' => 'read',
                                        'timestamp' => '1659463600',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $whatsAppMessage1 = $this->createMock(WhatsAppMessage::class);
        $whatsAppMessage1->id = 123;
        $whatsAppMessage2 = $this->createMock(WhatsAppMessage::class);
        $whatsAppMessage2->id = 456;

        $webhookEntry1 = $this->createMock(WhatsAppWebhookEntry::class);
        $webhookEntry2 = $this->createMock(WhatsAppWebhookEntry::class);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $this->messageRepository
            ->expects($this->exactly(2))
            ->method('fetchByExternalWamId')
            ->willReturnCallback(function ($externalWamId) use ($whatsAppMessage1, $whatsAppMessage2) {
                if ($externalWamId === 'wamid.FIRST') {
                    return $whatsAppMessage1;
                } elseif ($externalWamId === 'wamid.SECOND') {
                    return $whatsAppMessage2;
                }
                return null;
            });

        $this->webhookEntryFactory
            ->expects($this->exactly(2))
            ->method('buildFromWebhookStatus')
            ->willReturnOnConsecutiveCalls($webhookEntry1, $webhookEntry2);

        $this->webhookEntryRepository
            ->expects($this->exactly(2))
            ->method('store')
            ->willReturnOnConsecutiveCalls($webhookEntry1, $webhookEntry2);

        // Act
        $result = $this->useCase->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['processed_entries']);
        $this->assertCount(2, $result['entries']);
    }

    public function test_perform_rolls_back_on_exception()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.ERROR',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $this->messageRepository
            ->expects($this->once())
            ->method('fetchByExternalWamId')
            ->willThrowException(new \Exception('Database error'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($webhookEntry);
    }
}
