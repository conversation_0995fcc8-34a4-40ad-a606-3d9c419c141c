<?php

namespace Tests\Unit\Services\Meta\WhatsApp\UseCases\Template;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\UseCases\Template\Publish;
use App\Http\Requests\Template\StoreRequest;
use App\Domains\ChatBot\Template;
use App\Factories\ChatBot\TemplateFactory;
use App\Repositories\TemplateRepository;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Exception;

class PublishTest extends TestCase
{
    use RefreshDatabase;

    protected Publish $useCase;
    protected TemplateRepository $templateRepository;
    protected TemplateFactory $templateFactory;
    protected StoreRequest $request;
    protected Organization $organization;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateRepository = $this->createMock(TemplateRepository::class);
        $this->templateFactory = $this->createMock(TemplateFactory::class);
        $this->useCase = new Publish($this->templateRepository, $this->templateFactory);

        // Create test organization and user
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);

        // Mock the request
        $this->request = $this->createMock(StoreRequest::class);
    }

    public function test_publishes_template_successfully()
    {
        // Arrange
        $templateData = [
            'name' => 'Test Template',
            'description' => 'Test Description',
            'language' => 'en',
            'category' => 'MARKETING',
            'components' => [
                [
                    'type' => 'HEADER',
                    'format' => 'TEXT',
                    'text' => 'Hello {{1}}'
                ],
                [
                    'type' => 'BODY',
                    'text' => 'This is a test template for {{1}}'
                ]
            ]
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        // Mock template domain creation
        $templateDomain = new Template(
            id: null,
            organization_id: null, // Will be set by use case
            user_id: null,
            name: 'Test Template',
            description: 'Test Description',
            language: 'en',
            category: 'MARKETING',
            json: json_encode($templateData['components'])
        );

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->with($this->request)
            ->willReturn($templateDomain);

        // Mock saved template
        $savedTemplate = new Template(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: 'Test Template',
            description: 'Test Description',
            language: 'en',
            category: 'MARKETING',
            json: json_encode($templateData['components'])
        );

        $this->templateRepository
            ->expects($this->once())
            ->method('store')
            ->with($this->callback(function ($template) {
                return $template->organization_id === $this->organization->id &&
                       $template->name === 'Test Template';
            }))
            ->willReturn($savedTemplate);

        // Mock authenticated user
        $this->actingAs($this->user);

        // Act
        $result = $this->useCase->perform($this->request);

        // Assert
        $this->assertInstanceOf(Template::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Test Template', $result->name);
        $this->assertEquals('Test Description', $result->description);
    }

    public function test_sets_organization_id_from_authenticated_user()
    {
        // Arrange
        $templateData = [
            'name' => 'Test Template',
            'description' => 'Test Description'
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        $templateDomain = new Template(
            id: null,
            organization_id: null,
            user_id: null,
            name: 'Test Template',
            description: 'Test Description'
        );

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->willReturn($templateDomain);

        $this->templateRepository
            ->expects($this->once())
            ->method('store')
            ->with($this->callback(function ($template) {
                return $template->organization_id === $this->organization->id;
            }))
            ->willReturn($templateDomain);

        // Mock authenticated user
        $this->actingAs($this->user);

        // Act
        $this->useCase->perform($this->request);

        // Assert - organization_id should be set from authenticated user
        $this->assertEquals($this->organization->id, $templateDomain->organization_id);
    }

    public function test_uses_database_transaction()
    {
        // Arrange
        $templateData = [
            'name' => 'Test Template',
            'description' => 'Test Description'
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        $templateDomain = new Template(
            id: null,
            organization_id: null,
            user_id: null,
            name: 'Test Template',
            description: 'Test Description'
        );

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->willReturn($templateDomain);

        $this->templateRepository
            ->expects($this->once())
            ->method('store')
            ->willReturn($templateDomain);

        // Mock authenticated user
        $this->actingAs($this->user);

        // Spy on DB transactions
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        // Act
        $this->useCase->perform($this->request);
    }

    public function test_rolls_back_transaction_on_exception()
    {
        // Arrange
        $templateData = [
            'name' => 'Test Template',
            'description' => 'Test Description'
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        $templateDomain = new Template(
            id: null,
            organization_id: null,
            user_id: null,
            name: 'Test Template',
            description: 'Test Description'
        );

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->willReturn($templateDomain);

        $this->templateRepository
            ->expects($this->once())
            ->method('store')
            ->willThrowException(new Exception('Database error'));

        // Mock authenticated user
        $this->actingAs($this->user);

        // Spy on DB transactions
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();
        DB::shouldReceive('commit')->never();

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');
        
        $this->useCase->perform($this->request);
    }

    public function test_handles_template_factory_exception()
    {
        // Arrange
        $templateData = [
            'name' => 'Invalid Template',
            'description' => 'Invalid Description'
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->willThrowException(new Exception('Invalid template data'));

        $this->templateRepository
            ->expects($this->never())
            ->method('store');

        // Mock authenticated user
        $this->actingAs($this->user);

        // Spy on DB transactions
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();
        DB::shouldReceive('commit')->never();

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid template data');
        
        $this->useCase->perform($this->request);
    }

    public function test_preserves_template_properties_during_processing()
    {
        // Arrange
        $templateData = [
            'name' => 'Complex Template',
            'description' => 'Complex Description',
            'language' => 'es',
            'category' => 'UTILITY',
            'components' => [
                [
                    'type' => 'HEADER',
                    'format' => 'TEXT',
                    'text' => 'Hola {{1}}'
                ],
                [
                    'type' => 'BODY',
                    'text' => 'Este es un mensaje para {{1}} sobre {{2}}'
                ],
                [
                    'type' => 'FOOTER',
                    'text' => 'Gracias por su atención'
                ]
            ]
        ];

        $this->request
            ->expects($this->any())
            ->method('validated')
            ->willReturn($templateData);

        $this->request
            ->expects($this->any())
            ->method('all')
            ->willReturn($templateData);

        $templateDomain = new Template(
            id: null,
            organization_id: null,
            user_id: null,
            name: 'Complex Template',
            description: 'Complex Description',
            language: 'es',
            category: 'UTILITY',
            json: json_encode($templateData['components'])
        );

        $this->templateFactory
            ->expects($this->once())
            ->method('buildFromStoreRequest')
            ->willReturn($templateDomain);

        $savedTemplate = new Template(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: 'Complex Template',
            description: 'Complex Description',
            language: 'es',
            category: 'UTILITY',
            json: json_encode($templateData['components'])
        );

        $this->templateRepository
            ->expects($this->once())
            ->method('store')
            ->willReturn($savedTemplate);

        // Mock authenticated user
        $this->actingAs($this->user);

        // Act
        $result = $this->useCase->perform($this->request);

        // Assert
        $this->assertEquals('Complex Template', $result->name);
        $this->assertEquals('Complex Description', $result->description);
        $this->assertEquals('es', $result->language);
        $this->assertEquals('UTILITY', $result->category);
        $this->assertNotNull($result->json);
    }
}
