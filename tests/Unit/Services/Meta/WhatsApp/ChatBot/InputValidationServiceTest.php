<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\Services\InputValidationService;
use App\Enums\ChatBot\InputType;

class InputValidationServiceTest extends TestCase
{
    protected InputValidationService $inputValidationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->inputValidationService = new InputValidationService();
    }

    public function test_validate_text_input_success()
    {
        $result = $this->inputValidationService->validateInput(
            '<PERSON>',
            InputType::TEXT
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('<PERSON>', $result['sanitized_input']);
        $this->assertEquals('text', $result['input_type']);
    }

    public function test_validate_text_input_empty_fails()
    {
        $result = $this->inputValidationService->validateInput(
            '',
            InputType::TEXT
        );

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('error', $result);
    }

    public function test_validate_email_input_success()
    {
        $result = $this->inputValidationService->validateInput(
            '<EMAIL>',
            InputType::EMAIL
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('<EMAIL>', $result['sanitized_input']);
        $this->assertEquals('email', $result['input_type']);
    }

    public function test_validate_email_input_invalid_fails()
    {
        $result = $this->inputValidationService->validateInput(
            'invalid-email',
            InputType::EMAIL
        );

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('email', strtolower($result['error']));
    }

    public function test_validate_phone_input_success()
    {
        $validPhones = [
            '11999999999',
            '(11) 99999-9999',
            '+55 11 99999-9999',
            '5511999999999'
        ];

        foreach ($validPhones as $phone) {
            $result = $this->inputValidationService->validateInput(
                $phone,
                InputType::PHONE
            );

            $this->assertTrue($result['valid'], "Phone {$phone} should be valid");
        }
    }

    public function test_validate_phone_input_invalid_fails()
    {
        $invalidPhones = [
            '123',
            'abc',
            '11 9999',
            '999999999999999'
        ];

        foreach ($invalidPhones as $phone) {
            $result = $this->inputValidationService->validateInput(
                $phone,
                InputType::PHONE
            );

            $this->assertFalse($result['valid'], "Phone {$phone} should be invalid");
        }
    }

    public function test_validate_cpf_input_success()
    {
        $validCpfs = [
            '11144477735',
            '111.444.777-35',
            '123.456.789-09'
        ];

        foreach ($validCpfs as $cpf) {
            $result = $this->inputValidationService->validateInput(
                $cpf,
                InputType::CPF
            );

            $this->assertTrue($result['valid'], "CPF {$cpf} should be valid");
        }
    }

    public function test_validate_cpf_input_invalid_fails()
    {
        $invalidCpfs = [
            '11111111111', // repeated digits
            '123456789', // too short
            '12345678901234', // too long
            'abc.def.ghi-jk', // non-numeric
            '111.444.777-34' // invalid check digit
        ];

        foreach ($invalidCpfs as $cpf) {
            $result = $this->inputValidationService->validateInput(
                $cpf,
                InputType::CPF
            );

            $this->assertFalse($result['valid'], "CPF {$cpf} should be invalid");
        }
    }

    public function test_validate_cnpj_input_success()
    {
        $validCnpjs = [
            '11222333000181',
            '11.222.333/0001-81'
        ];

        foreach ($validCnpjs as $cnpj) {
            $result = $this->inputValidationService->validateInput(
                $cnpj,
                InputType::CNPJ
            );

            $this->assertTrue($result['valid'], "CNPJ {$cnpj} should be valid");
        }
    }

    public function test_validate_cnpj_input_invalid_fails()
    {
        $invalidCnpjs = [
            '1122233300018', // too short
            '112223330001812', // too long
            'ab.cde.fgh/ijkl-mn', // non-numeric
            '11.222.333/0001-82' // invalid check digit
        ];

        foreach ($invalidCnpjs as $cnpj) {
            $result = $this->inputValidationService->validateInput(
                $cnpj,
                InputType::CNPJ
            );

            $this->assertFalse($result['valid'], "CNPJ {$cnpj} should be invalid");
        }
    }

    public function test_validate_number_input_success()
    {
        $validNumbers = [
            '123',
            '123.45',
            '1,234.56',
            '-123'
        ];

        foreach ($validNumbers as $number) {
            $result = $this->inputValidationService->validateInput(
                $number,
                InputType::NUMBER
            );

            $this->assertTrue($result['valid'], "Number {$number} should be valid");
        }
    }

    public function test_validate_number_input_invalid_fails()
    {
        $invalidNumbers = [
            'abc',
            '12a34',
            'not a number'
        ];

        foreach ($invalidNumbers as $number) {
            $result = $this->inputValidationService->validateInput(
                $number,
                InputType::NUMBER
            );

            $this->assertFalse($result['valid'], "Number {$number} should be invalid");
        }
    }

    public function test_validate_date_input_success()
    {
        $validDates = [
            '01/01/2023',
            '31/12/2023',
            '29/02/2024' // leap year
        ];

        foreach ($validDates as $date) {
            $result = $this->inputValidationService->validateInput(
                $date,
                InputType::DATE
            );

            $this->assertTrue($result['valid'], "Date {$date} should be valid");
        }
    }

    public function test_validate_date_input_invalid_fails()
    {
        $invalidDates = [
            '2023-01-01', // wrong format
            '32/01/2023', // invalid day
            '01/13/2023', // invalid month
            '29/02/2023', // not a leap year
            'abc'
        ];

        foreach ($invalidDates as $date) {
            $result = $this->inputValidationService->validateInput(
                $date,
                InputType::DATE
            );

            $this->assertFalse($result['valid'], "Date {$date} should be invalid");
        }
    }

    public function test_validate_with_custom_rules()
    {
        $result = $this->inputValidationService->validateInput(
            'ab',
            InputType::TEXT,
            'required|string|min:5'
        );

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('error', $result);
    }

    public function test_validate_with_string_input_type()
    {
        $result = $this->inputValidationService->validateInput(
            '<EMAIL>',
            'email'
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('email', $result['input_type']);
    }

    public function test_validate_with_invalid_string_input_type()
    {
        $result = $this->inputValidationService->validateInput(
            'test',
            'invalid_type'
        );

        $this->assertFalse($result['valid']);
        $this->assertEquals('INVALID_INPUT_TYPE', $result['error_code']);
    }

    public function test_sanitize_input_removes_extra_spaces()
    {
        $result = $this->inputValidationService->validateInput(
            '  João Silva  ',
            InputType::TEXT
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('João Silva', $result['sanitized_input']);
    }

    public function test_sanitize_cpf_removes_formatting()
    {
        $result = $this->inputValidationService->validateInput(
            '111.444.777-35',
            InputType::CPF
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('11144477735', $result['sanitized_input']);
    }

    public function test_sanitize_email_converts_to_lowercase()
    {
        $result = $this->inputValidationService->validateInput(
            '<EMAIL>',
            InputType::EMAIL
        );

        $this->assertTrue($result['valid']);
        $this->assertEquals('<EMAIL>', $result['sanitized_input']);
    }
}
