<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use Exception;

class ProcessWebhookMessageTest extends TestCase
{
    protected ProcessWebhookMessage $processWebhookMessage;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processWebhookMessage = new ProcessWebhookMessage();
    }

    public function test_perform_with_valid_text_message()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text',
                'timestamp' => '1234567890'
            ],
            'metadata' => [
                'display_phone_number' => '15551234567',
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => '<PERSON> Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        // Act
        $result = $this->processWebhookMessage->perform($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('wamid.123456789', $result['message_id']);
        $this->assertEquals('text', $result['type']);
        $this->assertEquals('Hello World', $result['text']['body']);
        $this->assertEquals('1234567890', $result['timestamp']);
        $this->assertEquals('15551234567', $result['metadata']['display_phone_number']);
        $this->assertEquals('phone_123', $result['metadata']['phone_number_id']);
        $this->assertEquals('John Doe', $result['contacts'][0]['profile']['name']);
    }

    public function test_perform_with_valid_interactive_message()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'interactive' => [
                    'type' => 'button_reply',
                    'button_reply' => [
                        'id' => 'button_1',
                        'title' => 'Yes'
                    ]
                ],
                'type' => 'interactive',
                'timestamp' => '1234567890'
            ],
            'metadata' => [
                'display_phone_number' => '15551234567',
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Jane Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        // Act
        $result = $this->processWebhookMessage->perform($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('wamid.123456789', $result['message_id']);
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('button_reply', $result['interactive']['type']);
        $this->assertEquals('button_1', $result['interactive']['button_reply']['id']);
        $this->assertEquals('Yes', $result['interactive']['button_reply']['title']);
    }

    public function test_perform_with_image_message()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'image' => [
                    'id' => 'image_123',
                    'mime_type' => 'image/jpeg',
                    'sha256' => 'abc123def456',
                    'caption' => 'Check this out!'
                ],
                'type' => 'image',
                'timestamp' => '1234567890'
            ],
            'metadata' => [
                'display_phone_number' => '15551234567',
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => []
        ];

        // Act
        $result = $this->processWebhookMessage->perform($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('wamid.123456789', $result['message_id']);
        $this->assertEquals('image', $result['type']);
        $this->assertEquals('image_123', $result['image']['id']);
        $this->assertEquals('image/jpeg', $result['image']['mime_type']);
        $this->assertEquals('Check this out!', $result['image']['caption']);
    }

    public function test_perform_throws_exception_when_missing_from()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text'
                // Missing 'from' field
            ],
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing sender phone number in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_throws_exception_when_missing_message_id()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text'
                // Missing 'id' field
            ],
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing message ID in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_with_empty_message_array()
    {
        // Arrange
        $webhookData = [
            'message' => [],
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing sender phone number in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_with_missing_message_key()
    {
        // Arrange
        $webhookData = [
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing sender phone number in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_with_empty_from_field()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '',
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text'
            ],
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing sender phone number in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_with_empty_message_id_field()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => '',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text'
            ],
            'metadata' => [],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing message ID in webhook data');

        $this->processWebhookMessage->perform($webhookData);
    }

    public function test_perform_with_minimal_valid_data()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789'
            ],
            'metadata' => [],
            'contacts' => []
        ];

        // Act
        $result = $this->processWebhookMessage->perform($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('wamid.123456789', $result['message_id']);
        $this->assertArrayHasKey('metadata', $result);
        $this->assertArrayHasKey('contacts', $result);
        $this->assertIsArray($result['metadata']);
        $this->assertIsArray($result['contacts']);
    }

    public function test_perform_preserves_all_message_fields()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello World'
                ],
                'type' => 'text',
                'timestamp' => '1234567890',
                'context' => [
                    'from' => '5511888888888',
                    'id' => 'wamid.previous_message'
                ],
                'custom_field' => 'custom_value'
            ],
            'metadata' => [
                'display_phone_number' => '15551234567',
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Test User'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        // Act
        $result = $this->processWebhookMessage->perform($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('wamid.123456789', $result['message_id']);
        $this->assertEquals('Hello World', $result['text']['body']);
        $this->assertEquals('text', $result['type']);
        $this->assertEquals('1234567890', $result['timestamp']);
        $this->assertEquals('5511888888888', $result['context']['from']);
        $this->assertEquals('custom_value', $result['custom_field']);
    }
}
