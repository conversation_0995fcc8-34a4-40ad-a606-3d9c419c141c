<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Repositories\ClientRepository;
use App\Repositories\PhoneNumberRepository;
use App\Repositories\OrganizationRepository;
use App\Factories\Inventory\ClientFactory;
use App\Domains\Inventory\Client;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Organization;
use Exception;

class FindOrCreateClientTest extends TestCase
{
    use RefreshDatabase;

    protected FindOrCreateClient $findOrCreateClient;
    protected $mockClientRepository;
    protected $mockPhoneNumberRepository;
    protected $mockOrganizationRepository;
    protected $mockClientFactory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks
        $this->mockClientRepository = $this->createMock(ClientRepository::class);
        $this->mockPhoneNumberRepository = $this->createMock(PhoneNumberRepository::class);
        $this->mockOrganizationRepository = $this->createMock(OrganizationRepository::class);
        $this->mockClientFactory = $this->createMock(ClientFactory::class);

        $this->findOrCreateClient = new FindOrCreateClient(
            $this->mockClientRepository,
            $this->mockPhoneNumberRepository,
            $this->mockOrganizationRepository,
            $this->mockClientFactory
        );
    }

    public function test_perform_returns_existing_client()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'John Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $existingClient = $this->createMock(Client::class);
        $existingClient->id = 1;
        $existingClient->name = 'John Doe';
        $existingClient->phone = '5511999999999';

        // Set up mocks
        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('whatsapp_phone_123')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->with('5511999999999', 1)
            ->willReturn($existingClient);

        // Act
        $result = $this->findOrCreateClient->perform($messageData);

        // Assert
        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('John Doe', $result->name);
        $this->assertEquals('5511999999999', $result->phone);
    }

    public function test_perform_creates_new_client_when_not_found()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Jane Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $newClient = $this->createMock(Client::class);
        $newClient->id = 2;
        $newClient->name = 'Jane Doe';
        $newClient->phone = '5511999999999';

        $savedClient = $this->createMock(Client::class);
        $savedClient->id = 2;
        $savedClient->name = 'Jane Doe';
        $savedClient->phone = '5511999999999';

        // Set up mocks
        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('whatsapp_phone_123')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->with('5511999999999', 1)
            ->willReturn(null);

        $this->mockClientFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->with($messageData, 1)
            ->willReturn($newClient);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('save')
            ->with($newClient)
            ->willReturn($savedClient);

        // Act
        $result = $this->findOrCreateClient->perform($messageData);

        // Assert
        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(2, $result->id);
        $this->assertEquals('Jane Doe', $result->name);
        $this->assertEquals('5511999999999', $result->phone);
    }

    public function test_perform_updates_existing_client_with_better_name()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'John Smith Updated'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $existingClient = $this->createMock(Client::class);
        $existingClient->id = 1;
        $existingClient->name = 'John';
        $existingClient->phone = '5511999999999';

        $updatedClient = $this->createMock(Client::class);
        $updatedClient->id = 1;
        $updatedClient->name = 'John Smith Updated';
        $updatedClient->phone = '5511999999999';

        // Set up mocks
        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('whatsapp_phone_123')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->with('5511999999999', 1)
            ->willReturn($existingClient);

        // Mock the protected method behavior
        $this->mockClientRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($updatedClient);

        // Act
        $result = $this->findOrCreateClient->perform($messageData);

        // Assert
        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('John Smith Updated', $result->name);
    }

    public function test_perform_throws_exception_when_phone_number_not_found()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'invalid_phone_id'
            ],
            'contacts' => []
        ];

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('invalid_phone_id')
            ->willReturn(null);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Phone number not found for WhatsApp ID: invalid_phone_id');

        $this->findOrCreateClient->perform($messageData);
    }

    public function test_perform_handles_missing_metadata()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Test User'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
            // Missing metadata
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing phone number ID in metadata');

        $this->findOrCreateClient->perform($messageData);
    }

    public function test_perform_handles_missing_phone_number_id_in_metadata()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'display_phone_number' => '15551234567'
                // Missing phone_number_id
            ],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing phone number ID in metadata');

        $this->findOrCreateClient->perform($messageData);
    }

    public function test_perform_handles_client_factory_exception()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => []
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->willReturn(null);

        $this->mockClientFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willThrowException(new Exception('Client creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Client creation failed');

        $this->findOrCreateClient->perform($messageData);
    }

    public function test_perform_handles_client_save_exception()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => []
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $newClient = $this->createMock(Client::class);

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->willReturn(null);

        $this->mockClientFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willReturn($newClient);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('save')
            ->willThrowException(new Exception('Database save failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database save failed');

        $this->findOrCreateClient->perform($messageData);
    }

    public function test_perform_with_empty_contacts_array()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => []
        ];

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->organization_id = 1;

        $newClient = $this->createMock(Client::class);
        $savedClient = $this->createMock(Client::class);

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('findByPhoneAndOrganization')
            ->willReturn(null);

        $this->mockClientFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willReturn($newClient);

        $this->mockClientRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedClient);

        // Act
        $result = $this->findOrCreateClient->perform($messageData);

        // Assert
        $this->assertInstanceOf(Client::class, $result);
    }
}
