<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Repositories\ClientRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use Exception;

class DynamicInputServiceTest extends TestCase
{
    use RefreshDatabase;

    protected DynamicInputService $service;
    protected ClientRepository $clientRepository;
    protected WhatsAppConversation $conversation;
    protected Client $client;
    protected Step $inputStep;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientRepository = $this->createMock(ClientRepository::class);
        $this->service = new DynamicInputService($this->clientRepository);

        // Create mock client
        $this->client = new Client(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: '<PERSON>',
            phone: '+1234567890',
            email: '<EMAIL>'
        );

        // Create mock conversation
        $this->conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            client: $this->client
        );

        // Create mock input step
        $this->inputStep = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Name Input',
            message: 'Please enter your name',
            is_input: true,
            json: json_encode([
                'input_field' => 'client.name',
                'validation' => 'required|string|max:255'
            ])
        );
    }

    public function test_processes_client_name_input_successfully()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Jane Smith'
            ]
        ];

        $updatedClient = new Client(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: 'Jane Smith',
            phone: '+1234567890',
            email: '<EMAIL>'
        );

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->callback(function ($client) {
                return $client->name === 'Jane Smith' && $client->id === 1;
            }))
            ->willReturn($updatedClient);

        // Act
        $result = $this->service->processInput($this->conversation, $this->inputStep, $messageData);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('Jane Smith', $this->conversation->client->name);
    }

    public function test_processes_client_email_input_successfully()
    {
        // Arrange
        $emailStep = new Step(
            id: 2,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 2,
            name: 'Email Input',
            message: 'Please enter your email',
            is_input: true,
            json: json_encode([
                'input_field' => 'client.email',
                'validation' => 'required|email'
            ])
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => '<EMAIL>'
            ]
        ];

        $updatedClient = new Client(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>'
        );

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($updatedClient);

        // Act
        $result = $this->service->processInput($this->conversation, $emailStep, $messageData);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('<EMAIL>', $this->conversation->client->email);
    }

    public function test_processes_client_phone_input_successfully()
    {
        // Arrange
        $phoneStep = new Step(
            id: 3,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 3,
            name: 'Phone Input',
            message: 'Please enter your phone number',
            is_input: true,
            json: json_encode([
                'input_field' => 'client.phone',
                'validation' => 'required|string'
            ])
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => '+1987654321'
            ]
        ];

        $updatedClient = new Client(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: 'John Doe',
            phone: '+1987654321',
            email: '<EMAIL>'
        );

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($updatedClient);

        // Act
        $result = $this->service->processInput($this->conversation, $phoneStep, $messageData);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('+1987654321', $this->conversation->client->phone);
    }

    public function test_returns_false_for_non_input_step()
    {
        // Arrange
        $nonInputStep = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Regular Step',
            message: 'This is not an input step',
            is_input: false
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Some input'
            ]
        ];

        $this->clientRepository
            ->expects($this->never())
            ->method('save');

        // Act
        $result = $this->service->processInput($this->conversation, $nonInputStep, $messageData);

        // Assert
        $this->assertFalse($result);
    }

    public function test_returns_false_when_no_input_field_configured()
    {
        // Arrange
        $stepWithoutInputField = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Input Step Without Field',
            message: 'Input step but no field configured',
            is_input: true,
            json: json_encode([])
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Some input'
            ]
        ];

        $this->clientRepository
            ->expects($this->never())
            ->method('save');

        // Act
        $result = $this->service->processInput($this->conversation, $stepWithoutInputField, $messageData);

        // Assert
        $this->assertFalse($result);
    }

    public function test_returns_false_when_repository_save_fails()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Jane Smith'
            ]
        ];

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->willThrowException(new Exception('Database error'));

        // Act
        $result = $this->service->processInput($this->conversation, $this->inputStep, $messageData);

        // Assert
        $this->assertFalse($result);
    }

    public function test_handles_malformed_json_gracefully()
    {
        // Arrange
        $stepWithBadJson = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Bad JSON Step',
            message: 'Input step with bad JSON',
            is_input: true,
            json: 'invalid json'
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Some input'
            ]
        ];

        $this->clientRepository
            ->expects($this->never())
            ->method('save');

        // Act
        $result = $this->service->processInput($this->conversation, $stepWithBadJson, $messageData);

        // Assert
        $this->assertFalse($result);
    }

    public function test_handles_unsupported_input_field()
    {
        // Arrange
        $stepWithUnsupportedField = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Unsupported Field Step',
            message: 'Input step with unsupported field',
            is_input: true,
            json: json_encode([
                'input_field' => 'client.unsupported_field'
            ])
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'Some input'
            ]
        ];

        $this->clientRepository
            ->expects($this->never())
            ->method('save');

        // Act
        $result = $this->service->processInput($this->conversation, $stepWithUnsupportedField, $messageData);

        // Assert
        $this->assertFalse($result);
    }

    public function test_extracts_text_from_different_message_types()
    {
        // Test text message
        $textMessage = [
            'type' => 'text',
            'text' => [
                'body' => 'Text Input'
            ]
        ];

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($this->client);

        $result = $this->service->processInput($this->conversation, $this->inputStep, $textMessage);
        $this->assertTrue($result);
    }

    public function test_trims_whitespace_from_input()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => '  Jane Smith  '
            ]
        ];

        $this->clientRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->callback(function ($client) {
                return $client->name === 'Jane Smith'; // Should be trimmed
            }))
            ->willReturn($this->client);

        // Act
        $result = $this->service->processInput($this->conversation, $this->inputStep, $messageData);

        // Assert
        $this->assertTrue($result);
    }
}
