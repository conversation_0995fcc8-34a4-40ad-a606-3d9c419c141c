<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Services\ChatBotMessageService;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

class ChatBotMessageServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ChatBotMessageService $service;
    protected VariableSubstitutionService $substitutionService;
    protected GuzzleClient $guzzleClient;
    protected WhatsAppConversation $conversation;
    protected Client $client;
    protected PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Guzzle client
        $this->guzzleClient = $this->createMock(GuzzleClient::class);
        
        // Create phone number
        $this->phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            phone_number: '+1234567890',
            whatsapp_phone_number_id: 'whatsapp_123',
            whatsapp_access_token: 'token_123'
        );

        // Create service
        $this->service = new ChatBotMessageService($this->phoneNumber);
        
        // Replace the Guzzle client with our mock
        $reflection = new \ReflectionClass($this->service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->service, $this->guzzleClient);

        // Create mock client
        $this->client = new Client(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>'
        );

        // Create mock conversation
        $this->conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            client: $this->client
        );
    }

    public function test_sends_text_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Hello {{client.name}}!',
            type: 'text'
        );

        $expectedPayload = [
            'messaging_product' => 'whatsapp',
            'to' => '+1234567890',
            'type' => 'text',
            'text' => [
                'body' => 'Hello John Doe!'
            ]
        ];

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) use ($expectedPayload) {
                    return isset($options['json']) && 
                           $options['json']['type'] === 'text' &&
                           $options['json']['text']['body'] === 'Hello John Doe!';
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_sends_interactive_button_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Choose an option, {{client.name}}:',
            type: 'interactive',
            json: json_encode([
                'buttons' => [
                    [
                        'id' => 'btn1',
                        'title' => 'Option 1'
                    ],
                    [
                        'id' => 'btn2',
                        'title' => 'Option 2'
                    ]
                ]
            ])
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) && 
                           $options['json']['type'] === 'interactive' &&
                           isset($options['json']['interactive']['action']['buttons']);
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_sends_interactive_list_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Select from list:',
            type: 'interactive',
            json: json_encode([
                'list' => [
                    'header' => 'Choose Option',
                    'body' => 'Please select one',
                    'sections' => [
                        [
                            'title' => 'Options',
                            'rows' => [
                                [
                                    'id' => 'opt1',
                                    'title' => 'Option 1',
                                    'description' => 'First option'
                                ]
                            ]
                        ]
                    ]
                ]
            ])
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) && 
                           $options['json']['type'] === 'interactive' &&
                           isset($options['json']['interactive']['action']['sections']);
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_substitutes_variables_in_message_content()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Hello {{client.name}}, your email is {{client.email}}',
            type: 'text'
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) && 
                           $options['json']['text']['body'] === 'Hello John Doe, your <NAME_EMAIL>';
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_substitutes_variables_in_button_titles()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Choose option:',
            type: 'interactive',
            json: json_encode([
                'buttons' => [
                    [
                        'id' => 'btn1',
                        'title' => 'Hello {{client.name}}'
                    ]
                ]
            ])
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    $buttons = $options['json']['interactive']['action']['buttons'] ?? [];
                    return !empty($buttons) && $buttons[0]['reply']['title'] === 'Hello John Doe';
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_throws_exception_when_guzzle_request_fails()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Test message',
            type: 'text'
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new GuzzleException('Network error'));

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->expectExceptionMessage('Network error');
        
        $this->service->sendMessage($message, $this->conversation);
    }

    public function test_handles_malformed_json_in_message()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            phone_number_id: 1,
            to: '+1234567890',
            message: 'Test message',
            type: 'interactive',
            json: 'invalid json'
        );

        $this->guzzleClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    // Should fall back to text message
                    return $options['json']['type'] === 'text';
                })
            )
            ->willReturn(new Response(200, [], json_encode(['id' => 'msg_123'])));

        // Act
        $response = $this->service->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_uses_correct_phone_number_configuration()
    {
        // Arrange
        $customPhoneNumber = new PhoneNumber(
            id: 2,
            organization_id: 1,
            user_id: 1,
            phone_number: '+9876543210',
            whatsapp_phone_number_id: 'custom_123',
            whatsapp_access_token: 'custom_token'
        );

        $customService = new ChatBotMessageService($customPhoneNumber);
        
        // Use reflection to check internal properties
        $reflection = new \ReflectionClass($customService);
        $phoneNumberIdProperty = $reflection->getProperty('phoneNumberId');
        $phoneNumberIdProperty->setAccessible(true);
        $tokenProperty = $reflection->getProperty('token');
        $tokenProperty->setAccessible(true);

        // Assert
        $this->assertEquals('custom_123', $phoneNumberIdProperty->getValue($customService));
        $this->assertEquals('custom_token', $tokenProperty->getValue($customService));
    }
}
