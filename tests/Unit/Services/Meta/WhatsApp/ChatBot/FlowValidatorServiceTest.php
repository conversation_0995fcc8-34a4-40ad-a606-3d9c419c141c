<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowValidatorService;
use App\Repositories\StepRepository;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use Mockery;

class FlowValidatorServiceTest extends TestCase
{
    protected FlowValidatorService $flowValidatorService;
    protected $mockStepRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockStepRepository = Mockery::mock(StepRepository::class);
        $this->flowValidatorService = new FlowValidatorService($this->mockStepRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_validate_flow_with_empty_steps_returns_error()
    {
        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains('Flow has no steps defined', $result['errors']);
    }

    public function test_validate_flow_without_initial_step_returns_error()
    {
        config(['chatbot.flow_validation.rules.require_initial_step' => true]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, 2, null, false, false, true, false, false, false, '{}'),
            new Step(2, 1, 1, 'step2', 'message', 2, null, 1, false, true, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains('No initial step found in flow', $result['errors']);
    }

    public function test_validate_flow_without_ending_step_returns_error()
    {
        config(['chatbot.flow_validation.rules.require_ending_step' => true]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, 2, null, true, false, true, false, false, false, '{}'),
            new Step(2, 1, 1, 'step2', 'message', 2, 3, 1, false, false, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains('No ending step found in flow', $result['errors']);
    }

    public function test_validate_flow_with_multiple_initial_steps_returns_error()
    {
        config(['chatbot.flow_validation.rules.require_initial_step' => true]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, 2, null, true, false, true, false, false, false, '{}'),
            new Step(2, 1, 1, 'step2', 'message', 2, null, 1, true, true, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains('Multiple initial steps found. Only one initial step is allowed.', $result['errors']);
    }

    public function test_validate_flow_with_valid_structure_returns_success()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => true,
            'chatbot.flow_validation.rules.require_ending_step' => true,
            'chatbot.flow_validation.rules.check_orphaned_steps' => false,
            'chatbot.flow_validation.rules.check_infinite_loops' => false,
            'chatbot.flow_validation.rules.validate_conditional_targets' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, 2, null, true, false, true, false, false, false, '{}'),
            new Step(2, 1, 1, 'step2', 'message', 2, null, 1, false, true, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEquals(2, $result['total_steps']);
    }

    public function test_validate_step_configuration_with_no_step_identifier()
    {
        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, '', 'message', 1, null, null, true, true, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains('Step ID 1 has no step identifier', $result['errors']);
    }

    public function test_validate_step_configuration_with_no_type_flags()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, null, null, false, false, false, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertTrue($result['valid']); // Should be valid but with warnings
        $this->assertContains("Step 'step1' has no type flags set", $result['warnings']);
    }

    public function test_validate_step_configuration_with_multiple_type_flags()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, null, null, false, false, true, true, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertTrue($result['valid']); // Should be valid but with warnings
        $this->assertContains("Step 'step1' has multiple type flags set", $result['warnings']);
    }

    public function test_validate_step_configuration_non_ending_step_without_next_step()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, null, null, false, false, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains("Non-ending step 'step1' has no next_step defined", $result['errors']);
    }

    public function test_validate_command_step_without_json_configuration()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'command', 1, null, null, false, true, false, false, true, false, null)
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains("Command step 'step1' has no JSON configuration", $result['errors']);
    }

    public function test_validate_command_step_with_invalid_json()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'command', 1, null, null, false, true, false, false, true, false, 'invalid json')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains("Command step 'step1' has invalid JSON configuration", $result['errors']);
    }

    public function test_validate_command_step_without_command_type()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'command', 1, null, null, false, true, false, false, true, false, '{"command_config": {}}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertFalse($result['valid']);
        $this->assertContains("Command step 'step1' has no command_type specified", $result['errors']);
    }

    public function test_validate_command_step_without_command_config()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'command', 1, null, null, false, true, false, false, true, false, '{"command_type": "update_client"}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertTrue($result['valid']); // Should be valid but with warnings
        $this->assertContains("Command step 'step1' has no command_config specified", $result['warnings']);
    }

    public function test_validate_input_step_without_field_name()
    {
        config([
            'chatbot.flow_validation.rules.require_initial_step' => false,
            'chatbot.flow_validation.rules.require_ending_step' => false
        ]);

        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'input', 1, null, null, false, true, false, false, false, true, '{"input_config": {}}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertTrue($result['valid']); // Should be valid but with warnings
        $this->assertContains("Input step 'step1' has no field_name specified", $result['warnings']);
    }

    public function test_validate_flow_returns_correct_metadata()
    {
        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);
        $flow->steps = [
            new Step(1, 1, 1, 'step1', 'message', 1, 2, null, true, false, true, false, false, false, '{}'),
            new Step(2, 1, 1, 'step2', 'message', 2, null, 1, false, true, true, false, false, false, '{}')
        ];

        $result = $this->flowValidatorService->validateFlow($flow);

        $this->assertEquals(1, $result['flow_id']);
        $this->assertEquals('Test Flow', $result['flow_name']);
        $this->assertEquals(2, $result['total_steps']);
        $this->assertIsArray($result['errors']);
        $this->assertIsArray($result['warnings']);
    }
}
