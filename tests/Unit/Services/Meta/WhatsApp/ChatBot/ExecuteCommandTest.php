<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\UpdateClientData;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\CreateLead;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\SendNotification;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Enums\ChatBot\CommandType;
use Mockery;

class ExecuteCommandTest extends TestCase
{
    protected ExecuteCommand $executeCommand;
    protected $mockUpdateClientData;
    protected $mockCreateLead;
    protected $mockSendNotification;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockUpdateClientData = Mockery::mock(UpdateClientData::class);
        $this->mockCreateLead = Mockery::mock(CreateLead::class);
        $this->mockSendNotification = Mockery::mock(SendNotification::class);

        $this->executeCommand = new ExecuteCommand(
            $this->mockUpdateClientData,
            $this->mockCreateLead,
            $this->mockSendNotification
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_parse_command_configuration_with_valid_json()
    {
        $stepConfig = [
            'command_type' => 'update_client',
            'command_config' => ['field' => 'name'],
            'success_message' => 'Success!',
            'error_message' => 'Error!'
        ];

        $step = new Step(
            1, 1, 1, 'test_step', 'command', 1, 2, null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $reflection = new \ReflectionClass($this->executeCommand);
        $method = $reflection->getMethod('parseCommandConfiguration');
        $method->setAccessible(true);

        $result = $method->invoke($this->executeCommand, $step);

        $this->assertIsArray($result);
        $this->assertEquals('update_client', $result['command_type']);
        $this->assertEquals(['field' => 'name'], $result['command_config']);
        $this->assertEquals('Success!', $result['success_message']);
        $this->assertEquals('Error!', $result['error_message']);
    }

    public function test_parse_command_configuration_with_invalid_json()
    {
        $step = new Step(
            1, 1, 1, 'test_step', 'command', 1, 2, null,
            false, false, false, false, true, false,
            'invalid json'
        );

        $reflection = new \ReflectionClass($this->executeCommand);
        $method = $reflection->getMethod('parseCommandConfiguration');
        $method->setAccessible(true);

        $result = $method->invoke($this->executeCommand, $step);

        $this->assertNull($result);
    }

    public function test_parse_command_configuration_without_command_type()
    {
        $stepConfig = [
            'command_config' => ['field' => 'name'],
            'success_message' => 'Success!'
        ];

        $step = new Step(
            1, 1, 1, 'test_step', 'command', 1, 2, null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $reflection = new \ReflectionClass($this->executeCommand);
        $method = $reflection->getMethod('parseCommandConfiguration');
        $method->setAccessible(true);

        $result = $method->invoke($this->executeCommand, $step);

        $this->assertNull($result);
    }

    public function test_execute_update_client_command()
    {
        $stepConfig = [
            'command_type' => CommandType::UPDATE_CLIENT->value,
            'command_config' => ['field' => 'name'],
            'success_message' => 'Updated!',
            'error_message' => 'Failed!'
        ];

        $step = new Step(
            1, 1, 1, 'update_name', 'command', 1, 2, null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(1, 1, null, 1, 1, 1, 1, 'João Silva', null, null, null, now(), null, null, 'text');

        $expectedResult = ['field_updated' => 'name', 'new_value' => 'João Silva'];

        $this->mockUpdateClientData
            ->shouldReceive('perform')
            ->once()
            ->with(['field' => 'name'], $interaction, $conversation)
            ->andReturn($expectedResult);

        $result = $this->executeCommand->perform($step, $interaction, $conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::UPDATE_CLIENT->value, $result['command_type']);
        $this->assertEquals($expectedResult, $result['result']);
        $this->assertEquals('Updated!', $result['message']);
    }

    public function test_execute_create_lead_command()
    {
        $stepConfig = [
            'command_type' => CommandType::CREATE_LEAD->value,
            'command_config' => ['status' => 'new'],
            'success_message' => 'Lead created!',
            'error_message' => 'Failed to create lead!'
        ];

        $step = new Step(
            2, 1, 1, 'create_lead', 'command', 2, 3, 1,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 2);
        $interaction = new WhatsAppInteraction(2, 1, null, 1, 1, 2, 1, 'Interested in service', null, null, null, now(), null, null, 'text');

        $expectedResult = ['lead_id' => 123, 'client_id' => 1];

        $this->mockCreateLead
            ->shouldReceive('perform')
            ->once()
            ->with(['status' => 'new'], $interaction, $conversation)
            ->andReturn($expectedResult);

        $result = $this->executeCommand->perform($step, $interaction, $conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::CREATE_LEAD->value, $result['command_type']);
        $this->assertEquals($expectedResult, $result['result']);
        $this->assertEquals('Lead created!', $result['message']);
    }

    public function test_execute_send_notification_command()
    {
        $stepConfig = [
            'command_type' => CommandType::SEND_NOTIFICATION->value,
            'command_config' => ['type' => 'email', 'to' => '<EMAIL>'],
            'success_message' => 'Notification sent!',
            'error_message' => 'Failed to send notification!'
        ];

        $step = new Step(
            3, 1, 1, 'send_notification', 'command', 3, null, 2,
            false, true, false, false, true, false,
            json_encode($stepConfig)
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 3);
        $interaction = new WhatsAppInteraction(3, 1, null, 1, 1, 3, 1, 'Thank you!', null, null, null, now(), null, null, 'text');

        $expectedResult = ['notification_type' => 'email', 'recipient' => '<EMAIL>'];

        $this->mockSendNotification
            ->shouldReceive('perform')
            ->once()
            ->with(['type' => 'email', 'to' => '<EMAIL>'], $interaction, $conversation)
            ->andReturn($expectedResult);

        $result = $this->executeCommand->perform($step, $interaction, $conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::SEND_NOTIFICATION->value, $result['command_type']);
        $this->assertEquals($expectedResult, $result['result']);
        $this->assertEquals('Notification sent!', $result['message']);
    }

    public function test_execute_command_handles_exceptions()
    {
        $stepConfig = [
            'command_type' => CommandType::UPDATE_CLIENT->value,
            'command_config' => ['field' => 'name'],
            'success_message' => 'Updated!',
            'error_message' => 'Failed to update!'
        ];

        $step = new Step(
            1, 1, 1, 'update_name', 'command', 1, 2, null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(1, 1, null, 1, 1, 1, 1, 'João Silva', null, null, null, now(), null, null, 'text');

        $this->mockUpdateClientData
            ->shouldReceive('perform')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $result = $this->executeCommand->perform($step, $interaction, $conversation);

        $this->assertFalse($result['success']);
        $this->assertEquals(CommandType::UPDATE_CLIENT->value, $result['command_type']);
        $this->assertEquals('Database error', $result['error']);
        $this->assertEquals('Failed to update!', $result['message']);
    }

    public function test_perform_with_invalid_command_configuration()
    {
        $step = new Step(
            1, 1, 1, 'invalid_step', 'command', 1, 2, null,
            false, false, false, false, true, false,
            '{"invalid": "config"}'
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(1, 1, null, 1, 1, 1, 1, 'test', null, null, null, now(), null, null, 'text');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid command configuration');

        $this->executeCommand->perform($step, $interaction, $conversation);
    }

    public function test_perform_with_unknown_command_type()
    {
        $stepConfig = [
            'command_type' => 'unknown_command',
            'command_config' => [],
            'success_message' => 'Success',
            'error_message' => 'Error'
        ];

        $step = new Step(
            1, 1, 1, 'unknown_step', 'command', 1, 2, null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(1, 1, null, 1, 1, 1, 1, 'test', null, null, null, now(), null, null, 'text');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unknown command type');

        $this->executeCommand->perform($step, $interaction, $conversation);
    }
}
