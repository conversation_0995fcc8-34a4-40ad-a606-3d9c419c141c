<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConversationTimeoutService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use Carbon\Carbon;
use Mockery;

class ConversationTimeoutServiceTest extends TestCase
{
    protected ConversationTimeoutService $timeoutService;
    protected $mockConversationRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockConversationRepository = Mockery::mock(WhatsAppConversationRepository::class);
        $this->timeoutService = new ConversationTimeoutService($this->mockConversationRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_has_timed_out_returns_false_for_finished_conversation()
    {
        $conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            json: null,
            is_finished: true
        );

        $result = $this->timeoutService->hasTimedOut($conversation);

        $this->assertFalse($result);
    }

    public function test_has_timed_out_returns_false_for_recent_conversation()
    {
        // Mock config to return 3600 seconds (1 hour) timeout
        config(['chatbot.timeouts.conversation' => 3600]);

        $conversation = new WhatsAppConversation(
            id: 1, organization_id: 1, user_id: 1, client_id: 1, flow_id: 1,
            phone_number_id: 1, current_step_id: 1, json: null, is_finished: false
        );
        $conversation->updated_at = Carbon::now()->subMinutes(30); // 30 minutes ago

        $result = $this->timeoutService->hasTimedOut($conversation);

        $this->assertFalse($result);
    }

    public function test_has_timed_out_returns_true_for_old_conversation()
    {
        // Mock config to return 3600 seconds (1 hour) timeout
        config(['chatbot.timeouts.conversation' => 3600]);

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = Carbon::now()->subHours(2); // 2 hours ago

        $result = $this->timeoutService->hasTimedOut($conversation);

        $this->assertTrue($result);
    }

    public function test_has_timed_out_uses_created_at_when_updated_at_is_null()
    {
        config(['chatbot.timeouts.conversation' => 3600]);

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->created_at = Carbon::now()->subHours(2);
        $conversation->updated_at = null;

        $result = $this->timeoutService->hasTimedOut($conversation);

        $this->assertTrue($result);
    }

    public function test_is_inactive_returns_true_for_finished_conversation()
    {
        $conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            json: null,
            is_finished: true
        );

        $result = $this->timeoutService->isInactive($conversation);

        $this->assertTrue($result);
    }

    public function test_is_inactive_returns_false_for_recent_conversation()
    {
        config(['chatbot.timeouts.inactive_after' => 86400]); // 24 hours

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = Carbon::now()->subHours(12); // 12 hours ago

        $result = $this->timeoutService->isInactive($conversation);

        $this->assertFalse($result);
    }

    public function test_is_inactive_returns_true_for_old_conversation()
    {
        config(['chatbot.timeouts.inactive_after' => 86400]); // 24 hours

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = Carbon::now()->subHours(30); // 30 hours ago

        $result = $this->timeoutService->isInactive($conversation);

        $this->assertTrue($result);
    }

    public function test_get_timeout_remaining_returns_zero_for_finished_conversation()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, true);

        $result = $this->timeoutService->getTimeoutRemaining($conversation);

        $this->assertEquals(0, $result);
    }

    public function test_get_timeout_remaining_returns_correct_time_for_active_conversation()
    {
        config(['chatbot.timeouts.conversation' => 3600]); // 1 hour

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = Carbon::now()->subMinutes(30); // 30 minutes ago

        $result = $this->timeoutService->getTimeoutRemaining($conversation);

        // Should be approximately 30 minutes (1800 seconds) remaining
        $this->assertGreaterThan(1700, $result);
        $this->assertLessThan(1900, $result);
    }

    public function test_get_timeout_remaining_returns_zero_for_timed_out_conversation()
    {
        config(['chatbot.timeouts.conversation' => 3600]); // 1 hour

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = Carbon::now()->subHours(2); // 2 hours ago

        $result = $this->timeoutService->getTimeoutRemaining($conversation);

        $this->assertEquals(0, $result);
    }

    public function test_handle_timeout_marks_conversation_as_finished()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);

        $this->mockConversationRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($savedConversation) {
                return $savedConversation->is_finished === true;
            }))
            ->andReturn($conversation);

        $result = $this->timeoutService->handleTimeout($conversation);

        $this->assertEquals('timeout', $result['action']);
        $this->assertEquals($conversation->id, $result['conversation_id']);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('timeout_at', $result);
    }

    public function test_handle_timeout_adds_timeout_info_to_conversation_json()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, '{"existing": "data"}', false);

        $this->mockConversationRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($savedConversation) {
                $jsonData = json_decode($savedConversation->json, true);
                return isset($jsonData['timeout_info']) &&
                       isset($jsonData['existing']) &&
                       $jsonData['timeout_info']['reason'] === 'conversation_timeout';
            }))
            ->andReturn($conversation);

        $this->timeoutService->handleTimeout($conversation);
    }

    public function test_cleanup_inactive_conversations_with_dry_run()
    {
        $result = $this->timeoutService->cleanupInactiveConversations(true);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('total_found', $result);
        $this->assertArrayHasKey('cleaned', $result);
        $this->assertArrayHasKey('errors', $result);
        $this->assertTrue($result['dry_run']);
    }

    public function test_send_timeout_warning_returns_correct_format()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);

        $result = $this->timeoutService->sendTimeoutWarning($conversation, 5);

        $this->assertEquals('timeout_warning', $result['action']);
        $this->assertEquals($conversation->id, $result['conversation_id']);
        $this->assertEquals(5, $result['minutes_remaining']);
        $this->assertStringContainsString('5 minutos', $result['message']);
    }

    public function test_extend_timeout_updates_conversation()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $originalTime = $conversation->updated_at;

        $this->mockConversationRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($savedConversation) use ($originalTime) {
                return $savedConversation->updated_at > $originalTime;
            }))
            ->andReturn($conversation);

        $result = $this->timeoutService->extendTimeout($conversation, 1800);

        $this->assertEquals('timeout_extended', $result['action']);
        $this->assertEquals($conversation->id, $result['conversation_id']);
        $this->assertEquals(1800, $result['additional_seconds']);
        $this->assertArrayHasKey('extended_at', $result);
    }

    public function test_extend_timeout_adds_extension_info_to_json()
    {
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, '{}', false);

        $this->mockConversationRepository
            ->shouldReceive('save')
            ->once()
            ->with(Mockery::on(function ($savedConversation) {
                $jsonData = json_decode($savedConversation->json, true);
                return isset($jsonData['timeout_extensions']) &&
                       is_array($jsonData['timeout_extensions']) &&
                       count($jsonData['timeout_extensions']) === 1;
            }))
            ->andReturn($conversation);

        $this->timeoutService->extendTimeout($conversation, 1800);
    }

    public function test_get_timeout_remaining_returns_full_timeout_when_no_timestamps()
    {
        config(['chatbot.timeouts.conversation' => 3600]);

        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1, null, false);
        $conversation->updated_at = null;
        $conversation->created_at = null;

        $result = $this->timeoutService->getTimeoutRemaining($conversation);

        $this->assertEquals(3600, $result);
    }
}
