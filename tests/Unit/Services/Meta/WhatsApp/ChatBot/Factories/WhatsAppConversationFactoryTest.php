<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Factories;

use Tests\Unit\Factories\BaseFactoryTest;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppConversationFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Models\Conversation as ConversationModel;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use Carbon\Carbon;

class WhatsAppConversationFactoryTest extends BaseFactoryTest
{
    protected WhatsAppConversationFactory $factory;
    protected ClientFactory $clientFactory;
    protected Client $client;
    protected PhoneNumber $phoneNumber;
    protected Flow $flow;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientFactory = $this->createMock(ClientFactory::class);
        $this->factory = new WhatsAppConversationFactory($this->clientFactory);

        // Create mock client
        $this->client = new Client(
            id: 1,
            organization_id: 1,
            name: '<PERSON>',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        // Create mock phone number
        $this->phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: null,
            flow_id: 1,
            phone_number: '+1234567890',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'whatsapp_123',
            whatsapp_access_token: 'token_123'
        );

        // Create mock flow
        $this->flow = new Flow(
            id: 1,
            organization_id: 1,
            name: 'Test Flow',
            description: 'Test flow description',
            steps_count: 0,
            json: '{}',
            is_default_flow: false
        );
    }

    public function test_builds_from_model_successfully()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $model = new ConversationModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number_id' => 1,
            'current_step_id' => 2,
            'json' => json_encode([
                'whatsapp_contact_name' => 'John Contact',
                'whatsapp_profile_name' => 'John Profile',
                'whatsapp_metadata' => ['key' => 'value']
            ]),
            'is_finished' => false,
            'created_at' => $createdAt,
            'updated_at' => $updatedAt
        ]);

        $model->setRelation('client', (object)[
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'name' => 'John Doe',
            'phone' => '+1234567890',
            'email' => '<EMAIL>'
        ]);

        $this->clientFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($this->client);

        // Act
        $conversation = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $conversation);
        $this->assertEquals(1, $conversation->id);
        $this->assertEquals(1, $conversation->organization_id);
        $this->assertEquals(1, $conversation->user_id);
        $this->assertEquals(1, $conversation->client_id);
        $this->assertEquals(1, $conversation->flow_id);
        $this->assertEquals(1, $conversation->phone_number_id);
        $this->assertEquals(2, $conversation->current_step_id);
        $this->assertEquals('John Contact', $conversation->whatsapp_contact_name);
        $this->assertEquals('John Profile', $conversation->whatsapp_profile_name);
        $this->assertEquals(['key' => 'value'], $conversation->whatsapp_metadata);
        $this->assertFalse($conversation->is_finished);
        $this->assertEquals($createdAt, $conversation->created_at);
        $this->assertEquals($updatedAt, $conversation->updated_at);
        $this->assertInstanceOf(Client::class, $conversation->client);
    }

    public function test_builds_from_model_with_null_model()
    {
        // Act
        $conversation = $this->factory->buildFromModel(null);

        // Assert
        $this->assertNull($conversation);
    }

    public function test_builds_from_model_with_malformed_json()
    {
        // Arrange
        $model = new ConversationModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number_id' => 1,
            'current_step_id' => 2,
            'json' => 'invalid json',
            'is_finished' => false,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        $model->setRelation('client', (object)[
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'name' => 'John Doe',
            'phone' => '+1234567890',
            'email' => '<EMAIL>'
        ]);

        $this->clientFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($this->client);

        // Act
        $conversation = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $conversation);
        $this->assertNull($conversation->whatsapp_contact_name);
        $this->assertNull($conversation->whatsapp_profile_name);
        $this->assertNull($conversation->whatsapp_metadata);
    }

    public function test_builds_from_model_without_client_relation()
    {
        // Arrange
        $model = new ConversationModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number_id' => 1,
            'current_step_id' => 2,
            'json' => json_encode([]),
            'is_finished' => false,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        $this->clientFactory
            ->expects($this->never())
            ->method('buildFromModel');

        // Act
        $conversation = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $conversation);
        $this->assertNull($conversation->client);
    }

    public function test_builds_from_webhook_data_successfully()
    {
        // Arrange
        $messageData = [
            'from' => '+1234567890',
            'contact_name' => '+1234567890',
            'profile_name' => 'John Profile',
            'timestamp' => '1234567890',
            'message_id' => 'msg_123'
        ];

        // Act
        $conversation = $this->factory->buildFromWebhookData(
            $messageData,
            $this->client,
            $this->phoneNumber,
            $this->flow
        );

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $conversation);
        $this->assertNull($conversation->id); // New conversation
        $this->assertEquals(1, $conversation->organization_id);
        $this->assertEquals(1, $conversation->client_id);
        $this->assertEquals(1, $conversation->flow_id);
        $this->assertEquals(1, $conversation->phone_number_id);
        $this->assertEquals('John Profile', $conversation->whatsapp_profile_name);
        $this->assertEquals('+1234567890', $conversation->whatsapp_contact_name);
        $this->assertIsArray($conversation->whatsapp_metadata);
        $this->assertFalse($conversation->is_finished);
        $this->assertInstanceOf(Client::class, $conversation->client);
    }

    public function test_builds_for_client_and_phone_number_successfully()
    {
        // Arrange
        $whatsappData = [
            'contact_name' => '+1234567890',
            'profile_name' => 'John Profile',
            'metadata' => [
                'from' => '+1234567890',
                'timestamp' => '1234567890'
            ]
        ];

        // Act
        $conversation = $this->factory->buildForClientAndPhoneNumber(
            $this->client,
            $this->phoneNumber,
            $this->flow,
            $whatsappData
        );

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $conversation);
        $this->assertNull($conversation->id); // New conversation
        $this->assertEquals(1, $conversation->organization_id);
        $this->assertEquals(1, $conversation->client_id);
        $this->assertEquals(1, $conversation->flow_id);
        $this->assertEquals(1, $conversation->phone_number_id);
        $this->assertEquals('John Profile', $conversation->whatsapp_profile_name);
        $this->assertEquals('+1234567890', $conversation->whatsapp_contact_name);
        $this->assertIsArray($conversation->whatsapp_metadata);
        $this->assertFalse($conversation->is_finished);
        $this->assertInstanceOf(Client::class, $conversation->client);
    }

    public function test_builds_new_conversation_extracts_contact_info_correctly()
    {
        // Arrange
        $messageData = [
            'from' => '+1234567890',
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Custom Profile Name'
                    ],
                    'wa_id' => '+1234567890'
                ]
            ],
            'metadata' => [
                'display_phone_number' => '+1234567890',
                'phone_number_id' => 'whatsapp_123',
                'custom_field' => 'custom_value'
            ]
        ];

        // Act
        $conversation = $this->factory->buildNewConversation(
            $messageData,
            $this->client,
            $this->phoneNumber,
            $this->flow
        );

        // Assert
        $this->assertEquals('Custom Profile Name', $conversation->whatsapp_profile_name);
        $this->assertEquals('+1234567890', $conversation->whatsapp_contact_name);
        $this->assertArrayHasKey('display_phone_number', $conversation->whatsapp_metadata);
        $this->assertArrayHasKey('phone_number_id', $conversation->whatsapp_metadata);
        $this->assertArrayHasKey('custom_field', $conversation->whatsapp_metadata);
    }

    public function test_builds_new_conversation_handles_missing_contacts()
    {
        // Arrange
        $messageData = [
            'from' => '+1234567890',
            'metadata' => [
                'display_phone_number' => '+1234567890'
            ]
        ];

        // Act
        $conversation = $this->factory->buildNewConversation(
            $messageData,
            $this->client,
            $this->phoneNumber,
            $this->flow
        );

        // Assert
        $this->assertEquals('+1234567890', $conversation->whatsapp_contact_name);
        $this->assertNull($conversation->whatsapp_profile_name);
    }

    public function test_builds_new_conversation_sets_current_step_to_first_step()
    {
        // Arrange
        $flowWithFirstStep = new Flow(
            id: 1,
            organization_id: 1,
            user_id: 1,
            name: 'Test Flow',
            description: 'Test flow description',
            first_step_id: 5
        );

        $messageData = [
            'from' => '+1234567890'
        ];

        // Act
        $conversation = $this->factory->buildNewConversation(
            $messageData,
            $this->client,
            $this->phoneNumber,
            $flowWithFirstStep
        );

        // Assert
        $this->assertEquals(5, $conversation->current_step_id);
    }

    public function test_builds_new_conversation_defaults_to_null_current_step()
    {
        // Arrange
        $messageData = [
            'from' => '+1234567890'
        ];

        // Act
        $conversation = $this->factory->buildNewConversation(
            $messageData,
            $this->client,
            $this->phoneNumber,
            $this->flow
        );

        // Assert
        $this->assertNull($conversation->current_step_id);
    }

    public function test_build_from_model()
    {
        // Arrange
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();

        // Act
        $domain = $factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($model->current_step_id, $domain->current_step_id);
        $this->assertEquals($model->is_finished, $domain->is_finished);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    protected function createFactoryInstance()
    {
        return $this->factory;
    }

    protected function getDomainClass(): string
    {
        return WhatsAppConversation::class;
    }

    protected function createModelInstance()
    {
        return new ConversationModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number_id' => 1,
            'current_step_id' => 2,
            'json' => json_encode([
                'whatsapp_contact_name' => '+1234567890',
                'whatsapp_profile_name' => 'John Profile',
                'whatsapp_metadata' => [
                    'from' => '+1234567890',
                    'timestamp' => '1234567890'
                ]
            ]),
            'is_finished' => false,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }
}
