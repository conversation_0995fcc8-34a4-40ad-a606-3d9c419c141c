<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Factories;

use Tests\Unit\Factories\BaseFactoryTest;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Models\Interaction as InteractionModel;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use Carbon\Carbon;

class WhatsAppInteractionFactoryTest extends BaseFactoryTest
{
    protected WhatsAppInteractionFactory $factory;
    protected WhatsAppConversation $conversation;
    protected Step $step;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->factory = new WhatsAppInteractionFactory();

        // Create mock client
        $this->client = new Client(
            id: 1,
            organization_id: 1,
            name: '<PERSON>',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        // Create mock conversation
        $this->conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            client: $this->client
        );

        // Create mock step
        $this->step = new Step(
            id: 1,
            organization_id: 1,
            flow_id: 1,
            step: 'Test Step',
            type: 'text',
            position: 1,
            next_step: null,
            earlier_step: null,
            is_initial_step: true,
            is_ending_step: false,
            is_message: true,
            is_interactive: false,
            is_command: false,
            is_input: false,
            json: '{}',
            input: null
        );
    }

    public function test_builds_from_model_successfully()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $model = new InteractionModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'step_id' => 1,
            'conversation_id' => 1,
            'message' => 'User message',
            'answer' => 'Bot response',
            'result' => 'success',
            'json' => json_encode([
                'whatsapp_message_id' => 'msg_123',
                'whatsapp_message_type' => 'text',
                'whatsapp_raw_data' => ['text' => ['body' => 'Hello']]
            ]),
            'created_at' => $createdAt,
            'updated_at' => $updatedAt
        ]);

        // Act
        $interaction = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertEquals(1, $interaction->id);
        $this->assertEquals(1, $interaction->organization_id);
        $this->assertEquals(1, $interaction->user_id);
        $this->assertEquals(1, $interaction->client_id);
        $this->assertEquals(1, $interaction->flow_id);
        $this->assertEquals(1, $interaction->step_id);
        $this->assertEquals(1, $interaction->conversation_id);
        $this->assertEquals('User message', $interaction->message);
        $this->assertEquals('Bot response', $interaction->answer);
        $this->assertEquals('success', $interaction->result);
        $this->assertEquals('msg_123', $interaction->whatsapp_message_id);
        $this->assertEquals('text', $interaction->whatsapp_message_type);
        $this->assertEquals(['text' => ['body' => 'Hello']], $interaction->whatsapp_raw_data);
        $this->assertEquals($createdAt, $interaction->created_at);
        $this->assertEquals($updatedAt, $interaction->updated_at);
    }

    public function test_builds_from_model_with_null_model()
    {
        // Act
        $interaction = $this->factory->buildFromModel(null);

        // Assert
        $this->assertNull($interaction);
    }

    public function test_builds_from_model_with_malformed_json()
    {
        // Arrange
        $model = new InteractionModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'step_id' => 1,
            'conversation_id' => 1,
            'message' => 'User message',
            'answer' => 'Bot response',
            'result' => 'success',
            'json' => 'invalid json',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        // Act
        $interaction = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertNull($interaction->whatsapp_message_id);
        $this->assertNull($interaction->whatsapp_message_type);
        $this->assertNull($interaction->whatsapp_raw_data);
    }

    public function test_builds_from_webhook_message_successfully()
    {
        // Arrange
        $messageData = [
            'id' => 'msg_123',
            'type' => 'text',
            'from' => '+1234567890',
            'timestamp' => '1234567890',
            'text' => [
                'body' => 'Hello World'
            ]
        ];

        // Act
        $interaction = $this->factory->buildFromWebhookMessage(
            $this->conversation,
            $this->step,
            $messageData
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertNull($interaction->id); // New interaction
        $this->assertEquals(1, $interaction->organization_id);
        $this->assertEquals(1, $interaction->user_id);
        $this->assertEquals(1, $interaction->client_id);
        $this->assertEquals(1, $interaction->flow_id);
        $this->assertEquals(1, $interaction->step_id);
        $this->assertEquals(1, $interaction->conversation_id);
        $this->assertEquals('Hello World', $interaction->message);
        $this->assertEquals('msg_123', $interaction->whatsapp_message_id);
        $this->assertEquals('text', $interaction->whatsapp_message_type);
        $this->assertEquals($messageData, $interaction->whatsapp_raw_data);
    }

    public function test_builds_from_webhook_interactive_button_message()
    {
        // Arrange
        $messageData = [
            'id' => 'msg_456',
            'type' => 'interactive',
            'from' => '+1234567890',
            'timestamp' => '1234567890',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => [
                    'id' => 'btn_1',
                    'title' => 'Option 1'
                ]
            ]
        ];

        // Act
        $interaction = $this->factory->buildFromWebhookMessage(
            $this->conversation,
            $this->step,
            $messageData
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertEquals('Option 1', $interaction->message);
        $this->assertEquals('msg_456', $interaction->whatsapp_message_id);
        $this->assertEquals('interactive', $interaction->whatsapp_message_type);
        $this->assertEquals($messageData, $interaction->whatsapp_raw_data);
    }

    public function test_builds_from_webhook_interactive_list_message()
    {
        // Arrange
        $messageData = [
            'id' => 'msg_789',
            'type' => 'interactive',
            'from' => '+1234567890',
            'timestamp' => '1234567890',
            'interactive' => [
                'type' => 'list_reply',
                'list_reply' => [
                    'id' => 'list_1',
                    'title' => 'List Option 1'
                ]
            ]
        ];

        // Act
        $interaction = $this->factory->buildFromWebhookMessage(
            $this->conversation,
            $this->step,
            $messageData
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertEquals('List Option 1', $interaction->message);
        $this->assertEquals('msg_789', $interaction->whatsapp_message_id);
        $this->assertEquals('interactive', $interaction->whatsapp_message_type);
    }

    public function test_builds_from_webhook_message_with_minimal_data()
    {
        // Arrange
        $messageData = [
            'id' => 'msg_minimal',
            'type' => 'unknown',
            'from' => '+1234567890'
        ];

        // Act
        $interaction = $this->factory->buildFromWebhookMessage(
            $this->conversation,
            $this->step,
            $messageData
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertNull($interaction->message);
        $this->assertEquals('msg_minimal', $interaction->whatsapp_message_id);
        $this->assertEquals('unknown', $interaction->whatsapp_message_type);
        $this->assertEquals($messageData, $interaction->whatsapp_raw_data);
    }

    public function test_builds_response_interaction_successfully()
    {
        // Arrange
        $responseMessage = 'Bot response message';
        $result = 'success';

        // Act
        $interaction = $this->factory->buildResponseInteraction(
            $this->conversation,
            $this->step,
            $responseMessage,
            $result
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertNull($interaction->id); // New interaction
        $this->assertEquals(1, $interaction->organization_id);
        $this->assertEquals(1, $interaction->user_id);
        $this->assertEquals(1, $interaction->client_id);
        $this->assertEquals(1, $interaction->flow_id);
        $this->assertEquals(1, $interaction->step_id);
        $this->assertEquals(1, $interaction->conversation_id);
        $this->assertNull($interaction->message); // No user message
        $this->assertEquals('Bot response message', $interaction->answer);
        $this->assertEquals('success', $interaction->result);
        $this->assertNull($interaction->whatsapp_message_id);
        $this->assertNull($interaction->whatsapp_message_type);
        $this->assertNull($interaction->whatsapp_raw_data);
    }

    public function test_builds_response_interaction_with_error_result()
    {
        // Arrange
        $responseMessage = 'Error occurred';
        $result = 'error';

        // Act
        $interaction = $this->factory->buildResponseInteraction(
            $this->conversation,
            $this->step,
            $responseMessage,
            $result
        );

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $interaction);
        $this->assertEquals('Error occurred', $interaction->answer);
        $this->assertEquals('error', $interaction->result);
    }

    public function test_extracts_message_content_from_different_types()
    {
        // Test text message
        $textMessage = [
            'type' => 'text',
            'text' => ['body' => 'Text content']
        ];
        $textInteraction = $this->factory->buildFromWebhookMessage($this->conversation, $this->step, $textMessage);
        $this->assertEquals('Text content', $textInteraction->message);

        // Test button reply
        $buttonMessage = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => ['title' => 'Button text']
            ]
        ];
        $buttonInteraction = $this->factory->buildFromWebhookMessage($this->conversation, $this->step, $buttonMessage);
        $this->assertEquals('Button text', $buttonInteraction->message);

        // Test list reply
        $listMessage = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list_reply',
                'list_reply' => ['title' => 'List text']
            ]
        ];
        $listInteraction = $this->factory->buildFromWebhookMessage($this->conversation, $this->step, $listMessage);
        $this->assertEquals('List text', $listInteraction->message);
    }

    public function test_build_from_model()
    {
        // Arrange
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();

        // Act
        $domain = $factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->step_id, $domain->step_id);
        $this->assertEquals($model->conversation_id, $domain->conversation_id);
        $this->assertEquals($model->message, $domain->message);
        $this->assertEquals($model->answer, $domain->answer);
        $this->assertEquals($model->result, $domain->result);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    protected function createFactoryInstance()
    {
        return $this->factory;
    }

    protected function getDomainClass(): string
    {
        return WhatsAppInteraction::class;
    }

    protected function createModelInstance()
    {
        return new InteractionModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'step_id' => 1,
            'conversation_id' => 1,
            'message' => 'Test message',
            'answer' => 'Test answer',
            'result' => 'success',
            'json' => json_encode([
                'whatsapp_message_id' => 'msg_123',
                'whatsapp_message_type' => 'text',
                'whatsapp_raw_data' => [
                    'type' => 'text',
                    'text' => ['body' => 'Test message']
                ]
            ]),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }
}
