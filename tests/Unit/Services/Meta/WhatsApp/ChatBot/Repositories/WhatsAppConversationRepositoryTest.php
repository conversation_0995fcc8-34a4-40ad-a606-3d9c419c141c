<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppConversationFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Models\Conversation as ConversationModel;
use App\Models\Client;
use App\Models\PhoneNumber;
use App\Models\Flow;
use App\Models\Step;
use App\Models\Organization;
use App\Models\User;
use App\Domains\Inventory\Client as ClientDomain;
use App\Domains\ChatBot\PhoneNumber as PhoneNumberDomain;
use Carbon\Carbon;

class WhatsAppConversationRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected WhatsAppConversationRepository $repository;
    protected $mockFactory;
    protected Organization $organization;
    protected User $user;
    protected Client $client;
    protected PhoneNumber $phoneNumber;
    protected Flow $flow;
    protected Step $step;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFactory = $this->createMock(WhatsAppConversationFactory::class);
        $this->repository = new WhatsAppConversationRepository($this->mockFactory);

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $this->organization->id,
            'whatsapp_phone_number_id' => 'test_phone_123'
        ]);
        $this->flow = Flow::factory()->create([
            'organization_id' => $this->organization->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);
        $this->step = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id
        ]);
    }

    public function test_find_active_conversation_returns_existing_conversation()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id,
            'is_finished' => false,
            'json' => json_encode(['whatsapp_contact_name' => 'John Doe'])
        ]);

        $clientDomain = $this->createMock(ClientDomain::class);
        $phoneNumberDomain = $this->createMock(PhoneNumberDomain::class);

        $expectedConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $this->step->id,
            $conversationModel->json,
            false,
            $conversationModel->created_at,
            $conversationModel->updated_at,
            'John Doe',
            null,
            ['whatsapp_contact_name' => 'John Doe'],
            $clientDomain
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($conversationModel) {
                return $model instanceof ConversationModel &&
                       $model->id === $conversationModel->id &&
                       $model->is_finished === false;
            }))
            ->willReturn($expectedConversation);

        // Act
        $result = $this->repository->findActiveConversation($clientDomain, $phoneNumberDomain);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals($conversationModel->id, $result->id);
        $this->assertFalse($result->is_finished);
        $this->assertEquals('John Doe', $result->whatsapp_contact_name);
    }

    public function test_find_active_conversation_returns_null_when_not_found()
    {
        // Arrange
        $clientDomain = $this->createMock(ClientDomain::class);
        $clientDomain->id = 999; // Non-existent client

        $phoneNumberDomain = $this->createMock(PhoneNumberDomain::class);
        $phoneNumberDomain->id = 999; // Non-existent phone number

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findActiveConversation($clientDomain, $phoneNumberDomain);

        // Assert
        $this->assertNull($result);
    }

    public function test_find_active_conversation_ignores_finished_conversations()
    {
        // Arrange
        ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'is_finished' => true // Finished conversation should be ignored
        ]);

        $clientDomain = $this->createMock(ClientDomain::class);
        $clientDomain->id = $this->client->id;

        $phoneNumberDomain = $this->createMock(PhoneNumberDomain::class);
        $phoneNumberDomain->id = $this->phoneNumber->id;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findActiveConversation($clientDomain, $phoneNumberDomain);

        // Assert
        $this->assertNull($result);
    }

    public function test_save_creates_new_conversation()
    {
        // Arrange
        $whatsAppConversation = new WhatsAppConversation(
            null,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $this->step->id,
            null,
            false,
            null,
            null,
            'Jane Smith',
            'Jane',
            ['whatsapp_contact_name' => 'Jane Smith'],
            null
        );

        // Act
        $result = $this->repository->save($whatsAppConversation);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($this->client->id, $result->client_id);
        $this->assertEquals($this->phoneNumber->id, $result->phone_number_id);
        $this->assertEquals($this->flow->id, $result->flow_id);
        $this->assertFalse($result->is_finished);

        // Verify it was saved to database
        $this->assertDatabaseHas('conversations', [
            'id' => $result->id,
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id,
            'is_finished' => false
        ]);
    }

    public function test_save_updates_existing_conversation()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id,
            'is_finished' => false
        ]);

        $newStep = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id
        ]);

        $whatsAppConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $newStep->id, // Updated step
            null,
            false,
            $conversationModel->created_at,
            $conversationModel->updated_at,
            'Updated Name',
            'Updated',
            ['whatsapp_contact_name' => 'Updated Name'],
            null
        );

        // Act
        $result = $this->repository->save($whatsAppConversation);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals($conversationModel->id, $result->id);
        $this->assertEquals($newStep->id, $result->current_step_id);

        // Verify it was updated in database
        $this->assertDatabaseHas('conversations', [
            'id' => $conversationModel->id,
            'current_step_id' => $newStep->id
        ]);
    }

    public function test_update_current_step()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id,
            'is_finished' => false
        ]);

        $newStep = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id
        ]);

        $whatsAppConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $this->step->id,
            null,
            false,
            $conversationModel->created_at,
            $conversationModel->updated_at
        );

        $updatedConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $newStep->id,
            null,
            false,
            $conversationModel->created_at,
            Carbon::now()
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($updatedConversation);

        // Act
        $result = $this->repository->updateCurrentStep($whatsAppConversation, $newStep->id);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals($newStep->id, $result->current_step_id);

        // Verify it was updated in database
        $this->assertDatabaseHas('conversations', [
            'id' => $conversationModel->id,
            'current_step_id' => $newStep->id
        ]);
    }

    public function test_finish_conversation()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'is_finished' => false
        ]);

        $whatsAppConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $this->step->id,
            null,
            false,
            $conversationModel->created_at,
            $conversationModel->updated_at
        );

        $finishedConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            $this->step->id,
            null,
            true, // Finished
            $conversationModel->created_at,
            Carbon::now()
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($finishedConversation);

        // Act
        $result = $this->repository->finishConversation($whatsAppConversation);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertTrue($result->is_finished);

        // Verify it was updated in database
        $this->assertDatabaseHas('conversations', [
            'id' => $conversationModel->id,
            'is_finished' => true
        ]);
    }

    public function test_find_by_id()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id
        ]);

        $expectedConversation = new WhatsAppConversation(
            $conversationModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->phoneNumber->id,
            null,
            null,
            false,
            $conversationModel->created_at,
            $conversationModel->updated_at
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($expectedConversation);

        // Act
        $result = $this->repository->findById($conversationModel->id);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals($conversationModel->id, $result->id);
    }

    public function test_find_by_id_returns_null_when_not_found()
    {
        // Arrange
        $nonExistentId = 999;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findById($nonExistentId);

        // Assert
        $this->assertNull($result);
    }

    public function test_find_active_conversation_loads_relationships()
    {
        // Arrange
        $conversationModel = ConversationModel::factory()->create([
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id,
            'is_finished' => false
        ]);

        $clientDomain = $this->createMock(ClientDomain::class);
        $clientDomain->id = $this->client->id;

        $phoneNumberDomain = $this->createMock(PhoneNumberDomain::class);
        $phoneNumberDomain->id = $this->phoneNumber->id;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) {
                // Verify that relationships are loaded
                return $model instanceof ConversationModel &&
                       $model->flow !== null &&
                       $model->currentStep !== null &&
                       $model->phoneNumber !== null &&
                       $model->client !== null;
            }))
            ->willReturn(new WhatsAppConversation($conversationModel->id));

        // Act
        $result = $this->repository->findActiveConversation($clientDomain, $phoneNumberDomain);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
    }
}
