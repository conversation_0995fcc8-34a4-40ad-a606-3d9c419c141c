<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Models\Interaction as InteractionModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Client;
use App\Models\Flow;
use App\Models\Step;
use App\Models\Conversation;
use Carbon\Carbon;

class WhatsAppInteractionRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected WhatsAppInteractionRepository $repository;
    protected $mockFactory;
    protected Organization $organization;
    protected User $user;
    protected Client $client;
    protected Flow $flow;
    protected Step $step;
    protected Conversation $conversation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFactory = $this->createMock(WhatsAppInteractionFactory::class);
        $this->repository = new WhatsAppInteractionRepository($this->mockFactory);

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $this->flow = Flow::factory()->create(['organization_id' => $this->organization->id]);
        $this->step = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id
        ]);
        $this->conversation = Conversation::factory()->create([
            'client_id' => $this->client->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->step->id
        ]);
    }

    public function test_save_creates_new_interaction()
    {
        // Arrange
        $whatsAppInteraction = new WhatsAppInteraction(
            null,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->step->id,
            $this->conversation->id,
            'Hello World',
            null,
            null,
            null,
            null,
            null,
            'wamid.123456789',
            'text',
            ['text' => ['body' => 'Hello World'], 'type' => 'text']
        );

        // Act
        $result = $this->repository->save($whatsAppInteraction);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals($this->client->id, $result->client_id);
        $this->assertEquals('Hello World', $result->message);
        $this->assertEquals('wamid.123456789', $result->whatsapp_message_id);

        // Verify it was saved to database
        $this->assertDatabaseHas('interactions', [
            'id' => $result->id,
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'flow_id' => $this->flow->id,
            'step_id' => $this->step->id,
            'conversation_id' => $this->conversation->id,
            'message' => 'Hello World'
        ]);

        // Verify WhatsApp data is stored in JSON
        $interaction = InteractionModel::find($result->id);
        $jsonData = json_decode($interaction->json, true);
        $this->assertEquals('wamid.123456789', $jsonData['whatsapp_message_id']);
        $this->assertEquals('text', $jsonData['whatsapp_message_type']);
        $this->assertIsArray($jsonData['whatsapp_raw_data']);
    }

    public function test_save_updates_existing_interaction()
    {
        // Arrange
        $interactionModel = InteractionModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'flow_id' => $this->flow->id,
            'step_id' => $this->step->id,
            'conversation_id' => $this->conversation->id,
            'message' => 'Original message',
            'answer' => null,
            'result' => null,
            'json' => json_encode([
                'whatsapp_message_id' => 'wamid.original',
                'whatsapp_message_type' => 'text',
                'whatsapp_raw_data' => ['text' => ['body' => 'Original message']]
            ])
        ]);

        $whatsAppInteraction = new WhatsAppInteraction(
            $interactionModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->step->id,
            $this->conversation->id,
            'Original message',
            'Updated answer',
            '{"processed": true}',
            null,
            $interactionModel->created_at,
            $interactionModel->updated_at,
            'wamid.original',
            'text',
            ['text' => ['body' => 'Original message']]
        );

        // Act
        $result = $this->repository->save($whatsAppInteraction);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $result);
        $this->assertEquals($interactionModel->id, $result->id);
        $this->assertEquals('Updated answer', $result->answer);
        $this->assertEquals('{"processed": true}', $result->result);

        // Verify it was updated in database
        $this->assertDatabaseHas('interactions', [
            'id' => $interactionModel->id,
            'answer' => 'Updated answer',
            'result' => '{"processed": true}'
        ]);
    }

    public function test_find_by_id()
    {
        // Arrange
        $interactionModel = InteractionModel::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'flow_id' => $this->flow->id,
            'step_id' => $this->step->id,
            'conversation_id' => $this->conversation->id,
            'message' => 'Test message',
            'json' => json_encode([
                'whatsapp_message_id' => 'wamid.test',
                'whatsapp_message_type' => 'text',
                'whatsapp_raw_data' => ['text' => ['body' => 'Test message']]
            ])
        ]);

        $expectedInteraction = new WhatsAppInteraction(
            $interactionModel->id,
            $this->organization->id,
            $this->user->id,
            $this->client->id,
            $this->flow->id,
            $this->step->id,
            $this->conversation->id,
            'Test message',
            null,
            null,
            $interactionModel->json,
            $interactionModel->created_at,
            $interactionModel->updated_at,
            'wamid.test',
            'text',
            ['text' => ['body' => 'Test message']]
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($interactionModel) {
                return $model instanceof InteractionModel &&
                       $model->id === $interactionModel->id;
            }))
            ->willReturn($expectedInteraction);

        // Act
        $result = $this->repository->findById($interactionModel->id);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $result);
        $this->assertEquals($interactionModel->id, $result->id);
        $this->assertEquals('Test message', $result->message);
        $this->assertEquals('wamid.test', $result->whatsapp_message_id);
    }

    public function test_find_by_id_returns_null_when_not_found()
    {
        // Arrange
        $nonExistentId = 999;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findById($nonExistentId);

        // Assert
        $this->assertNull($result);
    }

    public function test_find_by_conversation_id()
    {
        // Arrange
        $interaction1 = InteractionModel::factory()->create([
            'conversation_id' => $this->conversation->id,
            'organization_id' => $this->organization->id,
            'message' => 'First message'
        ]);

        $interaction2 = InteractionModel::factory()->create([
            'conversation_id' => $this->conversation->id,
            'organization_id' => $this->organization->id,
            'message' => 'Second message'
        ]);

        // Create interaction for different conversation (should not be returned)
        $otherConversation = Conversation::factory()->create();
        InteractionModel::factory()->create([
            'conversation_id' => $otherConversation->id,
            'organization_id' => $this->organization->id,
            'message' => 'Other conversation message'
        ]);

        $expectedInteraction1 = new WhatsAppInteraction($interaction1->id);
        $expectedInteraction2 = new WhatsAppInteraction($interaction2->id);

        $this->mockFactory
            ->expects($this->exactly(2))
            ->method('buildFromModel')
            ->willReturnOnConsecutiveCalls($expectedInteraction1, $expectedInteraction2);

        // Act
        $result = $this->repository->findByConversationId($this->conversation->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertInstanceOf(WhatsAppInteraction::class, $result[0]);
        $this->assertInstanceOf(WhatsAppInteraction::class, $result[1]);
    }

    public function test_find_by_whatsapp_message_id()
    {
        // Arrange
        $whatsappMessageId = 'wamid.unique123';
        $interactionModel = InteractionModel::factory()->create([
            'organization_id' => $this->organization->id,
            'conversation_id' => $this->conversation->id,
            'message' => 'Unique message',
            'json' => json_encode([
                'whatsapp_message_id' => $whatsappMessageId,
                'whatsapp_message_type' => 'text',
                'whatsapp_raw_data' => ['text' => ['body' => 'Unique message']]
            ])
        ]);

        $expectedInteraction = new WhatsAppInteraction(
            $interactionModel->id,
            $this->organization->id,
            null,
            null,
            null,
            null,
            $this->conversation->id,
            'Unique message',
            null,
            null,
            $interactionModel->json,
            $interactionModel->created_at,
            $interactionModel->updated_at,
            $whatsappMessageId,
            'text',
            ['text' => ['body' => 'Unique message']]
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($expectedInteraction);

        // Act
        $result = $this->repository->findByWhatsAppMessageId($whatsappMessageId);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $result);
        $this->assertEquals($whatsappMessageId, $result->whatsapp_message_id);
        $this->assertEquals('Unique message', $result->message);
    }

    public function test_find_by_whatsapp_message_id_returns_null_when_not_found()
    {
        // Arrange
        $nonExistentMessageId = 'wamid.nonexistent';

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findByWhatsAppMessageId($nonExistentMessageId);

        // Assert
        $this->assertNull($result);
    }

    public function test_find_latest_by_conversation()
    {
        // Arrange
        $olderInteraction = InteractionModel::factory()->create([
            'conversation_id' => $this->conversation->id,
            'organization_id' => $this->organization->id,
            'message' => 'Older message',
            'created_at' => Carbon::now()->subMinutes(10)
        ]);

        $newerInteraction = InteractionModel::factory()->create([
            'conversation_id' => $this->conversation->id,
            'organization_id' => $this->organization->id,
            'message' => 'Newer message',
            'created_at' => Carbon::now()->subMinutes(5)
        ]);

        $expectedInteraction = new WhatsAppInteraction(
            $newerInteraction->id,
            $this->organization->id,
            null,
            null,
            null,
            null,
            $this->conversation->id,
            'Newer message'
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($newerInteraction) {
                return $model instanceof InteractionModel &&
                       $model->id === $newerInteraction->id;
            }))
            ->willReturn($expectedInteraction);

        // Act
        $result = $this->repository->findLatestByConversation($this->conversation->id);

        // Assert
        $this->assertInstanceOf(WhatsAppInteraction::class, $result);
        $this->assertEquals($newerInteraction->id, $result->id);
        $this->assertEquals('Newer message', $result->message);
    }

    public function test_find_latest_by_conversation_returns_null_when_no_interactions()
    {
        // Arrange
        $emptyConversation = Conversation::factory()->create();

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findLatestByConversation($emptyConversation->id);

        // Assert
        $this->assertNull($result);
    }

    public function test_delete_interaction()
    {
        // Arrange
        $interactionModel = InteractionModel::factory()->create([
            'organization_id' => $this->organization->id,
            'conversation_id' => $this->conversation->id
        ]);

        $whatsAppInteraction = new WhatsAppInteraction(
            $interactionModel->id,
            $this->organization->id,
            null,
            null,
            null,
            null,
            $this->conversation->id
        );

        // Act
        $result = $this->repository->delete($whatsAppInteraction);

        // Assert
        $this->assertTrue($result);

        // Verify it was soft deleted from database
        $this->assertSoftDeleted('interactions', [
            'id' => $interactionModel->id
        ]);
    }

    public function test_delete_returns_false_when_interaction_not_found()
    {
        // Arrange
        $whatsAppInteraction = new WhatsAppInteraction(
            999, // Non-existent ID
            $this->organization->id,
            null,
            null,
            null,
            null,
            $this->conversation->id
        );

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->repository->delete($whatsAppInteraction);
    }

    public function test_count_by_conversation()
    {
        // Arrange
        InteractionModel::factory()->count(3)->create([
            'conversation_id' => $this->conversation->id,
            'organization_id' => $this->organization->id
        ]);

        // Create interactions for different conversation (should not be counted)
        $otherConversation = Conversation::factory()->create();
        InteractionModel::factory()->count(2)->create([
            'conversation_id' => $otherConversation->id,
            'organization_id' => $this->organization->id
        ]);

        // Act
        $result = $this->repository->countByConversation($this->conversation->id);

        // Assert
        $this->assertEquals(3, $result);
    }
}
