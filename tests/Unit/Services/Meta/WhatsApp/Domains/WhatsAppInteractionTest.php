<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Domains;

use Tests\Unit\Domains\BaseDomainTest;
use App\Services\Meta\WhatsApp\Domains\WhatsAppInteraction;
use Carbon\Carbon;

class WhatsAppInteractionTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();
        $whatsappRawData = [
            'text' => ['body' => 'Hello World'],
            'type' => 'text',
            'from' => '5511999999999'
        ];

        // Act
        $interaction = new WhatsAppInteraction(
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            'Hello World',
            'Response message',
            '{"processed": true}',
            '{"metadata": "test"}',
            $createdAt,
            $updatedAt,
            'wamid.123456789',
            'text',
            $whatsappRawData
        );

        // Assert
        $this->assertEquals(1, $interaction->id);
        $this->assertEquals(1, $interaction->organization_id);
        $this->assertEquals(1, $interaction->user_id);
        $this->assertEquals(1, $interaction->client_id);
        $this->assertEquals(1, $interaction->flow_id);
        $this->assertEquals(1, $interaction->step_id);
        $this->assertEquals(1, $interaction->conversation_id);
        $this->assertEquals('Hello World', $interaction->message);
        $this->assertEquals('Response message', $interaction->answer);
        $this->assertEquals('{"processed": true}', $interaction->result);
        $this->assertEquals('{"metadata": "test"}', $interaction->json);
        $this->assertEquals($createdAt, $interaction->created_at);
        $this->assertEquals($updatedAt, $interaction->updated_at);
        $this->assertEquals('wamid.123456789', $interaction->whatsapp_message_id);
        $this->assertEquals('text', $interaction->whatsapp_message_type);
        $this->assertEquals($whatsappRawData, $interaction->whatsapp_raw_data);
    }

    public function test_domain_instantiation_with_nulls()
    {
        // Act
        $interaction = new WhatsAppInteraction();

        // Assert
        $this->assertNull($interaction->id);
        $this->assertNull($interaction->organization_id);
        $this->assertNull($interaction->user_id);
        $this->assertNull($interaction->client_id);
        $this->assertNull($interaction->flow_id);
        $this->assertNull($interaction->step_id);
        $this->assertNull($interaction->conversation_id);
        $this->assertNull($interaction->message);
        $this->assertNull($interaction->answer);
        $this->assertNull($interaction->result);
        $this->assertNull($interaction->json);
        $this->assertNull($interaction->created_at);
        $this->assertNull($interaction->updated_at);
        $this->assertNull($interaction->whatsapp_message_id);
        $this->assertNull($interaction->whatsapp_message_type);
        $this->assertNull($interaction->whatsapp_raw_data);
    }

    public function test_to_array_method()
    {
        // Arrange
        $interaction = $this->createDomainInstance();

        // Act
        $array = $interaction->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('Hello World', $array['message']);
        $this->assertEquals('wamid.123456789', $array['whatsapp_message_id']);
        $this->assertEquals('text', $array['whatsapp_message_type']);
        $this->assertIsArray($array['whatsapp_raw_data']);
        $this->assertEquals('Hello World', $array['whatsapp_raw_data']['text']['body']);
    }

    public function test_to_store_array_method()
    {
        // Arrange
        $interaction = $this->createDomainInstance();

        // Act
        $storeArray = $interaction->toStoreArray();

        // Assert
        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('whatsapp_message_id', $storeArray);
        $this->assertArrayNotHasKey('whatsapp_message_type', $storeArray);
        $this->assertArrayNotHasKey('whatsapp_raw_data', $storeArray);
        
        // Verify WhatsApp data is stored in JSON field
        $this->assertArrayHasKey('json', $storeArray);
        $jsonData = json_decode($storeArray['json'], true);
        $this->assertIsArray($jsonData);
        $this->assertEquals('wamid.123456789', $jsonData['whatsapp_message_id']);
        $this->assertEquals('text', $jsonData['whatsapp_message_type']);
        $this->assertIsArray($jsonData['whatsapp_raw_data']);
    }

    public function test_is_text_message()
    {
        // Arrange
        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        $interactiveInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.456', 'interactive', ['interactive' => ['type' => 'button_reply']]
        );

        // Act & Assert
        $this->assertTrue($textInteraction->isTextMessage());
        $this->assertFalse($interactiveInteraction->isTextMessage());
    }

    public function test_is_interactive_message()
    {
        // Arrange
        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        $interactiveInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.456', 'interactive', ['interactive' => ['type' => 'button_reply']]
        );

        // Act & Assert
        $this->assertFalse($textInteraction->isInteractiveMessage());
        $this->assertTrue($interactiveInteraction->isInteractiveMessage());
    }

    public function test_is_image_message()
    {
        // Arrange
        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        $imageInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.789', 'image', ['image' => ['id' => 'img_123', 'mime_type' => 'image/jpeg']]
        );

        // Act & Assert
        $this->assertFalse($textInteraction->isImageMessage());
        $this->assertTrue($imageInteraction->isImageMessage());
    }

    public function test_get_interactive_selection()
    {
        // Arrange
        $buttonInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.456', 'interactive', [
                'interactive' => [
                    'type' => 'button_reply',
                    'button_reply' => [
                        'id' => 'option_1',
                        'title' => 'Yes'
                    ]
                ]
            ]
        );

        $listInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.789', 'interactive', [
                'interactive' => [
                    'type' => 'list_reply',
                    'list_reply' => [
                        'id' => 'item_1',
                        'title' => 'Option A'
                    ]
                ]
            ]
        );

        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        // Act & Assert
        $buttonSelection = $buttonInteraction->getInteractiveSelection();
        $this->assertEquals('option_1', $buttonSelection['id']);
        $this->assertEquals('Yes', $buttonSelection['title']);

        $listSelection = $listInteraction->getInteractiveSelection();
        $this->assertEquals('item_1', $listSelection['id']);
        $this->assertEquals('Option A', $listSelection['title']);

        $textSelection = $textInteraction->getInteractiveSelection();
        $this->assertNull($textSelection);
    }

    public function test_get_text_content()
    {
        // Arrange
        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, 'Fallback message', null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello World']]
        );

        $nonTextInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, 'Fallback message', null, null, null, null, null,
            'wamid.456', 'image', ['image' => ['id' => 'img_123']]
        );

        // Act & Assert
        $this->assertEquals('Hello World', $textInteraction->getTextContent());
        $this->assertEquals('Fallback message', $nonTextInteraction->getTextContent());
    }

    public function test_get_text_content_with_null_raw_data()
    {
        // Arrange
        $interaction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, 'Fallback message', null, null, null, null, null,
            'wamid.123', 'text', null
        );

        // Act & Assert
        $this->assertEquals('Fallback message', $interaction->getTextContent());
    }

    public function test_get_image_data()
    {
        // Arrange
        $imageData = [
            'id' => 'img_123',
            'mime_type' => 'image/jpeg',
            'sha256' => 'abc123def456',
            'caption' => 'Check this out!'
        ];

        $imageInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.789', 'image', ['image' => $imageData]
        );

        $textInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        // Act & Assert
        $this->assertEquals($imageData, $imageInteraction->getImageData());
        $this->assertNull($textInteraction->getImageData());
    }

    public function test_has_whatsapp_data()
    {
        // Arrange
        $withDataInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            'wamid.123', 'text', ['text' => ['body' => 'Hello']]
        );

        $withoutDataInteraction = new WhatsAppInteraction(
            null, null, null, null, null, null, null, null, null, null, null, null, null,
            null, null, null
        );

        // Act & Assert
        $this->assertTrue($withDataInteraction->hasWhatsAppData());
        $this->assertFalse($withoutDataInteraction->hasWhatsAppData());
    }

    public function test_to_store_array_preserves_whatsapp_data_structure()
    {
        // Arrange
        $complexRawData = [
            'text' => ['body' => 'Complex message'],
            'context' => [
                'from' => '5511888888888',
                'id' => 'wamid.previous'
            ],
            'timestamp' => '1234567890',
            'metadata' => [
                'custom_field' => 'custom_value'
            ]
        ];

        $interaction = new WhatsAppInteraction(
            1, 1, 1, 1, 1, 1, 1, 'Complex message', null, null, null, null, null,
            'wamid.complex', 'text', $complexRawData
        );

        // Act
        $storeArray = $interaction->toStoreArray();

        // Assert
        $jsonData = json_decode($storeArray['json'], true);
        $this->assertEquals('wamid.complex', $jsonData['whatsapp_message_id']);
        $this->assertEquals('text', $jsonData['whatsapp_message_type']);
        $this->assertIsArray($jsonData['whatsapp_raw_data']);
        $this->assertEquals('Complex message', $jsonData['whatsapp_raw_data']['text']['body']);
        $this->assertEquals('5511888888888', $jsonData['whatsapp_raw_data']['context']['from']);
        $this->assertEquals('custom_value', $jsonData['whatsapp_raw_data']['metadata']['custom_field']);
    }

    protected function createDomainInstance()
    {
        return new WhatsAppInteraction(
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            'Hello World',
            'Response message',
            '{"processed": true}',
            '{"metadata": "test"}',
            Carbon::now(),
            Carbon::now(),
            'wamid.123456789',
            'text',
            [
                'text' => ['body' => 'Hello World'],
                'type' => 'text',
                'from' => '5511999999999'
            ]
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'user_id',
            'client_id',
            'flow_id',
            'step_id',
            'conversation_id',
            'message',
            'answer',
            'result',
            'json',
            'created_at',
            'updated_at',
            'whatsapp_message_id',
            'whatsapp_message_type',
            'whatsapp_raw_data'
        ];
    }
}
