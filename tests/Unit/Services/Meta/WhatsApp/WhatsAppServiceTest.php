<?php

namespace Tests\Unit\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\WhatsAppService;
use App\Domains\ChatBot\PhoneNumber;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;
use Carbon\Carbon;

class WhatsAppServiceTest extends TestCase
{
    use RefreshDatabase;

    protected WhatsAppService $whatsAppService;
    protected $mockClient;
    protected PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a mock PhoneNumber
        $this->phoneNumber = new PhoneNumber(
            1,
            1,
            1,
            null,
            null,
            '+5511999999999',
            'Test Phone',
            'Test Description',
            true,
            'whatsapp_phone_id_123',
            'test_access_token',
            Carbon::now(),
            Carbon::now()
        );

        // Mock the Guzzle client
        $this->mockClient = $this->createMock(Client::class);

        // Create service instance
        $this->whatsAppService = new WhatsAppService($this->phoneNumber);

        // Replace the client with our mock using reflection
        $reflection = new \ReflectionClass($this->whatsAppService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->whatsAppService, $this->mockClient);
    }

    public function test_constructor_with_phone_number()
    {
        // Act
        $service = new WhatsAppService($this->phoneNumber);

        // Assert - Use reflection to check protected properties
        $reflection = new \ReflectionClass($service);

        $phoneNumberIdProperty = $reflection->getProperty('phoneNumberId');
        $phoneNumberIdProperty->setAccessible(true);
        $phoneNumberId = $phoneNumberIdProperty->getValue($service);

        $tokenProperty = $reflection->getProperty('token');
        $tokenProperty->setAccessible(true);
        $token = $tokenProperty->getValue($service);

        $this->assertEquals('whatsapp_phone_id_123', $phoneNumberId);
        $this->assertEquals('test_access_token', $token);
    }

    public function test_constructor_without_phone_number_uses_config()
    {
        // Arrange
        config(['whatsapp.phone_number_id' => 'config_phone_id']);
        config(['whatsapp.access_token' => 'config_token']);

        // Act
        $service = new WhatsAppService();

        // Assert
        $reflection = new \ReflectionClass($service);

        $phoneNumberIdProperty = $reflection->getProperty('phoneNumberId');
        $phoneNumberIdProperty->setAccessible(true);
        $phoneNumberId = $phoneNumberIdProperty->getValue($service);

        $tokenProperty = $reflection->getProperty('token');
        $tokenProperty->setAccessible(true);
        $token = $tokenProperty->getValue($service);

        $this->assertEquals('config_phone_id', $phoneNumberId);
        $this->assertEquals('config_token', $token);
    }

    public function test_post_method_success()
    {
        // Arrange
        $payload = ['test' => 'data'];
        $expectedResponse = new Response(200, [], json_encode(['success' => true]));

        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('whatsapp_phone_id_123'),
                $this->callback(function ($options) use ($payload) {
                    return isset($options['headers']['Authorization']) &&
                           $options['headers']['Authorization'] === 'Bearer test_access_token' &&
                           isset($options['headers']['Content-Type']) &&
                           $options['headers']['Content-Type'] === 'application/json' &&
                           isset($options['json']) &&
                           $options['json'] === $payload;
                })
            )
            ->willReturn($expectedResponse);

        // Act
        $response = $this->callProtectedMethod($this->whatsAppService, 'post', [$payload]);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_post_method_handles_guzzle_exception()
    {
        // Arrange
        $payload = ['test' => 'data'];
        $guzzleException = $this->createMock(GuzzleException::class);

        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException($guzzleException);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->callProtectedMethod($this->whatsAppService, 'post', [$payload]);
    }

    public function test_get_method_success()
    {
        // Arrange
        $expectedResponse = new Response(200, [], json_encode(['data' => 'test']));

        $this->mockClient
            ->expects($this->once())
            ->method('get')
            ->with(
                $this->stringContains('whatsapp_phone_id_123'),
                $this->callback(function ($options) {
                    return isset($options['headers']['Authorization']) &&
                           $options['headers']['Authorization'] === 'Bearer test_access_token';
                })
            )
            ->willReturn($expectedResponse);

        // Act
        $response = $this->callProtectedMethod($this->whatsAppService, 'get');

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_get_method_handles_guzzle_exception()
    {
        // Arrange
        $guzzleException = $this->createMock(GuzzleException::class);

        $this->mockClient
            ->expects($this->once())
            ->method('get')
            ->willThrowException($guzzleException);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->callProtectedMethod($this->whatsAppService, 'get');
    }

    public function test_delete_method_success()
    {
        // Arrange
        $expectedResponse = new Response(200, [], json_encode(['deleted' => true]));

        $this->mockClient
            ->expects($this->once())
            ->method('delete')
            ->with(
                $this->stringContains('whatsapp_phone_id_123'),
                $this->callback(function ($options) {
                    return isset($options['headers']['Authorization']) &&
                           $options['headers']['Authorization'] === 'Bearer test_access_token';
                })
            )
            ->willReturn($expectedResponse);

        // Act
        $response = $this->callProtectedMethod($this->whatsAppService, 'delete');

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_delete_method_handles_guzzle_exception()
    {
        // Arrange
        $guzzleException = $this->createMock(GuzzleException::class);

        $this->mockClient
            ->expects($this->once())
            ->method('delete')
            ->willThrowException($guzzleException);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->callProtectedMethod($this->whatsAppService, 'delete');
    }

    public function test_api_url_construction()
    {
        // Arrange
        config(['whatsapp.base_url' => 'https://graph.facebook.com/v23.0']);

        // Act
        $service = new WhatsAppService($this->phoneNumber);

        // Assert
        $reflection = new \ReflectionClass($service);
        $apiUrlProperty = $reflection->getProperty('apiUrl');
        $apiUrlProperty->setAccessible(true);
        $apiUrl = $apiUrlProperty->getValue($service);

        $this->assertEquals('https://graph.facebook.com/v23.0', $apiUrl);
    }

    public function test_endpoint_property_initialization()
    {
        // Act
        $service = new WhatsAppService($this->phoneNumber);

        // Assert
        $reflection = new \ReflectionClass($service);
        $endpointProperty = $reflection->getProperty('endpoint');
        $endpointProperty->setAccessible(true);
        $endpoint = $endpointProperty->getValue($service);

        // Endpoint should be empty by default in base service
        $this->assertEmpty($endpoint);
    }

    /**
     * Helper method to call protected methods
     */
    protected function callProtectedMethod($object, $method, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }

    /**
     * Helper method to get protected property value
     */
    protected function getProtectedProperty($object, $property)
    {
        $reflection = new \ReflectionClass(get_class($object));
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);

        return $property->getValue($object);
    }

    /**
     * Helper method to set protected property value
     */
    protected function setProtectedProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass(get_class($object));
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);
        $property->setValue($object, $value);
    }
}
