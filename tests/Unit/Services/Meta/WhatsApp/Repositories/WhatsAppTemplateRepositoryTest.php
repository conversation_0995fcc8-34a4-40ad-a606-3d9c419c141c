<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate as WhatsAppTemplateModel;
use App\Models\Template;
use Carbon\Carbon;

class WhatsAppTemplateRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected WhatsAppTemplateRepository $repository;
    protected $mockFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFactory = $this->createMock(WhatsAppTemplateFactory::class);
        $this->repository = new WhatsAppTemplateRepository($this->mockFactory);
    }

    public function test_fetch_by_template_id_returns_whatsapp_template()
    {
        // Arrange
        $template = Template::factory()->create();
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_123',
            'json' => '{"status": "APPROVED"}'
        ]);

        $expectedDomain = new WhatsAppTemplate(
            $whatsAppTemplateModel->id,
            $template->id,
            null,
            'APPROVED',
            'whatsapp_123',
            '{"status": "APPROVED"}',
            $whatsAppTemplateModel->created_at,
            $whatsAppTemplateModel->updated_at
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($whatsAppTemplateModel) {
                return $model instanceof WhatsAppTemplateModel &&
                       $model->id === $whatsAppTemplateModel->id &&
                       $model->template_id === $whatsAppTemplateModel->template_id;
            }))
            ->willReturn($expectedDomain);

        // Act
        $result = $this->repository->fetchByTemplateId($template->id);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals($template->id, $result->template_id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_123', $result->external_id);
    }

    public function test_fetch_by_template_id_returns_null_when_not_found()
    {
        // Arrange
        $nonExistentTemplateId = 999;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->fetchByTemplateId($nonExistentTemplateId);

        // Assert
        $this->assertNull($result);
    }

    public function test_fetch_by_id_returns_whatsapp_template()
    {
        // Arrange
        $template = Template::factory()->create();
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_456',
            'json' => '{"status": "PENDING"}'
        ]);

        $expectedDomain = new WhatsAppTemplate(
            $whatsAppTemplateModel->id,
            $template->id,
            null,
            'PENDING',
            'whatsapp_456',
            '{"status": "PENDING"}',
            $whatsAppTemplateModel->created_at,
            $whatsAppTemplateModel->updated_at
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($whatsAppTemplateModel) {
                return $model instanceof WhatsAppTemplateModel &&
                       $model->id === $whatsAppTemplateModel->id;
            }))
            ->willReturn($expectedDomain);

        // Act
        $result = $this->repository->fetchById($whatsAppTemplateModel->id);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals('PENDING', $result->status);
        $this->assertEquals('whatsapp_456', $result->external_id);
    }

    public function test_fetch_by_id_returns_null_when_not_found()
    {
        // Arrange
        $nonExistentId = 999;

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->fetchById($nonExistentId);

        // Assert
        $this->assertNull($result);
    }

    public function test_store_creates_new_whatsapp_template()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            1,
            null,
            'APPROVED',
            'whatsapp_789',
            '{"status": "APPROVED"}',
            null,
            null
        );

        // Act
        $result = $this->repository->store($whatsAppTemplate);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(1, $result->template_id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_789', $result->external_id);

        // Verify it was saved to database
        $this->assertDatabaseHas('whatsapp_templates', [
            'id' => $result->id,
            'template_id' => 1,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_789',
            'json' => '{"status": "APPROVED"}'
        ]);
    }

    public function test_update_modifies_existing_whatsapp_template()
    {
        // Arrange
        $template = Template::factory()->create();
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_old',
            'json' => '{"status": "PENDING"}'
        ]);

        $whatsAppTemplate = new WhatsAppTemplate(
            $whatsAppTemplateModel->id,
            $template->id,
            null,
            'APPROVED',
            'whatsapp_updated',
            '{"status": "APPROVED"}',
            $whatsAppTemplateModel->created_at,
            $whatsAppTemplateModel->updated_at
        );

        // Act
        $result = $this->repository->update($whatsAppTemplate);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_updated', $result->external_id);

        // Verify it was updated in database
        $this->assertDatabaseHas('whatsapp_templates', [
            'id' => $whatsAppTemplateModel->id,
            'template_id' => $template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_updated',
            'json' => '{"status": "APPROVED"}'
        ]);
    }

    public function test_save_calls_store_for_new_template()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            null, // No ID means it's new
            1,
            null,
            'PENDING',
            'whatsapp_new',
            '{"status": "PENDING"}',
            null,
            null
        );

        // Act
        $result = $this->repository->save($whatsAppTemplate);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(1, $result->template_id);
        $this->assertEquals('PENDING', $result->status);
        $this->assertEquals('whatsapp_new', $result->external_id);

        // Verify it was created in database
        $this->assertDatabaseHas('whatsapp_templates', [
            'id' => $result->id,
            'template_id' => 1,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_new'
        ]);
    }

    public function test_save_calls_update_for_existing_template()
    {
        // Arrange
        $template = Template::factory()->create();
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_existing',
            'json' => '{"status": "PENDING"}'
        ]);

        $whatsAppTemplate = new WhatsAppTemplate(
            $whatsAppTemplateModel->id, // Has ID means it exists
            $template->id,
            null,
            'APPROVED',
            'whatsapp_modified',
            '{"status": "APPROVED"}',
            $whatsAppTemplateModel->created_at,
            $whatsAppTemplateModel->updated_at
        );

        // Act
        $result = $this->repository->save($whatsAppTemplate);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_modified', $result->external_id);

        // Verify it was updated in database
        $this->assertDatabaseHas('whatsapp_templates', [
            'id' => $whatsAppTemplateModel->id,
            'template_id' => $template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_modified'
        ]);
    }

    public function test_delete_removes_whatsapp_template()
    {
        // Arrange
        $template = Template::factory()->create();
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_to_delete',
            'json' => '{"status": "APPROVED"}'
        ]);

        $whatsAppTemplate = new WhatsAppTemplate(
            $whatsAppTemplateModel->id,
            $template->id,
            null,
            'APPROVED',
            'whatsapp_to_delete',
            '{"status": "APPROVED"}',
            $whatsAppTemplateModel->created_at,
            $whatsAppTemplateModel->updated_at
        );

        // Act
        $result = $this->repository->delete($whatsAppTemplate);

        // Assert
        $this->assertTrue($result);

        // Verify it was soft deleted from database
        $this->assertSoftDeleted('whatsapp_templates', [
            'id' => $whatsAppTemplateModel->id
        ]);
    }

    public function test_delete_returns_false_when_template_not_found()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            999, // Non-existent ID
            1,
            null,
            'APPROVED',
            'whatsapp_nonexistent',
            '{"status": "APPROVED"}',
            Carbon::now(),
            Carbon::now()
        );

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->repository->delete($whatsAppTemplate);
    }

    public function test_fetch_by_template_id_includes_template_relationship()
    {
        // Arrange
        $template = Template::factory()->create([
            'name' => 'test_template',
            'category' => 'UTILITY'
        ]);
        
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id
        ]);

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($template) {
                // Verify that the template relationship is loaded
                return $model instanceof WhatsAppTemplateModel &&
                       $model->template !== null &&
                       $model->template->name === 'test_template';
            }))
            ->willReturn(new WhatsAppTemplate(1, 1, null, 'APPROVED', 'ext_123', '{}'));

        // Act
        $result = $this->repository->fetchByTemplateId($template->id);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
    }

    public function test_fetch_by_id_includes_template_relationship()
    {
        // Arrange
        $template = Template::factory()->create([
            'name' => 'test_template_2',
            'category' => 'MARKETING'
        ]);
        
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $template->id
        ]);

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($template) {
                // Verify that the template relationship is loaded
                return $model instanceof WhatsAppTemplateModel &&
                       $model->template !== null &&
                       $model->template->name === 'test_template_2';
            }))
            ->willReturn(new WhatsAppTemplate(1, 1, null, 'PENDING', 'ext_456', '{}'));

        // Act
        $result = $this->repository->fetchById($whatsAppTemplateModel->id);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
    }
}
