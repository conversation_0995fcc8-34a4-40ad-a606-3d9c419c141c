<?php

namespace Tests\Unit\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\Inventory\Client;
use Exception;

class ChatBotServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ChatBotService $chatBotService;
    protected $processWebhookMessage;
    protected $findOrCreateClient;
    protected $findOrCreateConversation;
    protected $processFlowStep;
    protected $sendWhatsAppResponse;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for all dependencies
        $this->processWebhookMessage = $this->createMock(ProcessWebhookMessage::class);
        $this->findOrCreateClient = $this->createMock(FindOrCreateClient::class);
        $this->findOrCreateConversation = $this->createMock(FindOrCreateConversation::class);
        $this->processFlowStep = $this->createMock(ProcessFlowStep::class);
        $this->sendWhatsAppResponse = $this->createMock(SendWhatsAppResponse::class);

        $this->chatBotService = new ChatBotService(
            $this->processWebhookMessage,
            $this->findOrCreateClient,
            $this->findOrCreateConversation,
            $this->processFlowStep,
            $this->sendWhatsAppResponse
        );
    }

    public function test_process_webhook_success_flow()
    {
        // Arrange
        $webhookData = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'messages' => [
                                    [
                                        'from' => '5511999999999',
                                        'id' => 'msg_123',
                                        'text' => ['body' => 'Hello'],
                                        'type' => 'text'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'msg_123',
            'type' => 'text',
            'text' => ['body' => 'Hello']
        ];

        $client = $this->createMock(Client::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $stepResult = [
            'type' => 'message',
            'action' => 'send_message',
            'message' => 'Welcome!',
            'move_to_next' => true
        ];

        $responseResult = [
            'status' => 'sent',
            'message_id' => 'response_123'
        ];

        // Set up mock expectations
        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->with($webhookData)
            ->willReturn($messageData);

        $this->findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->with($messageData)
            ->willReturn($client);

        $this->findOrCreateConversation
            ->expects($this->once())
            ->method('perform')
            ->with($messageData, $client)
            ->willReturn($conversation);

        $this->processFlowStep
            ->expects($this->once())
            ->method('perform')
            ->with($conversation, $messageData)
            ->willReturn($stepResult);

        $this->sendWhatsAppResponse
            ->expects($this->once())
            ->method('perform')
            ->with($stepResult, $conversation)
            ->willReturn($responseResult);

        // Act
        $result = $this->chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals($stepResult, $result['step_result']);
        $this->assertEquals($responseResult, $result['response_result']);
    }

    public function test_process_webhook_handles_process_webhook_message_exception()
    {
        // Arrange
        $webhookData = ['invalid' => 'data'];
        
        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->with($webhookData)
            ->willThrowException(new Exception('Invalid webhook data'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid webhook data');
        
        $this->chatBotService->processWebhook($webhookData);
    }

    public function test_process_webhook_handles_find_or_create_client_exception()
    {
        // Arrange
        $webhookData = ['valid' => 'data'];
        $messageData = ['from' => '5511999999999'];

        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($messageData);

        $this->findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->with($messageData)
            ->willThrowException(new Exception('Client creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Client creation failed');
        
        $this->chatBotService->processWebhook($webhookData);
    }

    public function test_process_webhook_handles_find_or_create_conversation_exception()
    {
        // Arrange
        $webhookData = ['valid' => 'data'];
        $messageData = ['from' => '5511999999999'];
        $client = $this->createMock(Client::class);

        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($messageData);

        $this->findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->willReturn($client);

        $this->findOrCreateConversation
            ->expects($this->once())
            ->method('perform')
            ->with($messageData, $client)
            ->willThrowException(new Exception('Conversation creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Conversation creation failed');
        
        $this->chatBotService->processWebhook($webhookData);
    }

    public function test_process_webhook_handles_process_flow_step_exception()
    {
        // Arrange
        $webhookData = ['valid' => 'data'];
        $messageData = ['from' => '5511999999999'];
        $client = $this->createMock(Client::class);
        $conversation = $this->createMock(WhatsAppConversation::class);

        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($messageData);

        $this->findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->willReturn($client);

        $this->findOrCreateConversation
            ->expects($this->once())
            ->method('perform')
            ->willReturn($conversation);

        $this->processFlowStep
            ->expects($this->once())
            ->method('perform')
            ->with($conversation, $messageData)
            ->willThrowException(new Exception('Flow step processing failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Flow step processing failed');
        
        $this->chatBotService->processWebhook($webhookData);
    }

    public function test_process_webhook_handles_send_whatsapp_response_exception()
    {
        // Arrange
        $webhookData = ['valid' => 'data'];
        $messageData = ['from' => '5511999999999'];
        $client = $this->createMock(Client::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        $stepResult = ['type' => 'message'];

        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($messageData);

        $this->findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->willReturn($client);

        $this->findOrCreateConversation
            ->expects($this->once())
            ->method('perform')
            ->willReturn($conversation);

        $this->processFlowStep
            ->expects($this->once())
            ->method('perform')
            ->willReturn($stepResult);

        $this->sendWhatsAppResponse
            ->expects($this->once())
            ->method('perform')
            ->with($stepResult, $conversation)
            ->willThrowException(new Exception('Response sending failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Response sending failed');
        
        $this->chatBotService->processWebhook($webhookData);
    }

    public function test_process_webhook_returns_error_status_on_exception()
    {
        // Arrange
        $webhookData = ['invalid' => 'data'];
        
        $this->processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willThrowException(new Exception('Test exception'));

        // Act
        try {
            $this->chatBotService->processWebhook($webhookData);
            $this->fail('Expected exception was not thrown');
        } catch (Exception $e) {
            // Assert
            $this->assertEquals('Test exception', $e->getMessage());
        }
    }
}
