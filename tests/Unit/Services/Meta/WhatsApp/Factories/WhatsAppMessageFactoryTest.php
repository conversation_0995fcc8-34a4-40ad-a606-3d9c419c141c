<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Factories;

use Tests\Unit\Factories\BaseFactoryTest;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage as WhatsAppMessageModel;
use App\Domains\ChatBot\Message;
use App\Factories\ChatBot\MessageFactory;
use App\Models\Message as MessageModel;
use Carbon\Carbon;

class WhatsAppMessageFactoryTest extends BaseFactoryTest
{
    private WhatsAppMessageFactory $factory;
    private MessageFactory $messageFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->messageFactory = $this->createMock(MessageFactory::class);
        $this->factory = new WhatsAppMessageFactory($this->messageFactory);
    }

    public function test_build_from_model_with_valid_model()
    {
        // Arrange
        $messageModel = $this->createMock(MessageModel::class);
        $messageDomain = $this->createMock(Message::class);

        $model = new class {
            public $id = 1;
            public $message_id = 2;
            public $message;
            public $whatsapp_message_id = 'wamid.123456789';
            public $message_status = 'accepted';
            public $wa_id = '557991514957';
            public $input_phone = '+5579991514957';
            public $messaging_product = 'whatsapp';
            public $json = '{"test": "data"}';
            public $created_at;
            public $updated_at;
        };
        $model->message = $messageModel;
        $model->created_at = Carbon::parse('2023-01-01 12:00:00');
        $model->updated_at = Carbon::parse('2023-01-01 12:00:00');

        $this->messageFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($messageModel)
            ->willReturn($messageDomain);

        // Act
        $result = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppMessage::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(2, $result->message_id);
        $this->assertEquals($messageDomain, $result->message);
        $this->assertEquals('wamid.123456789', $result->whatsapp_message_id);
        $this->assertEquals('accepted', $result->message_status);
        $this->assertEquals('557991514957', $result->wa_id);
        $this->assertEquals('+5579991514957', $result->input_phone);
        $this->assertEquals('whatsapp', $result->messaging_product);
        $this->assertEquals('{"test": "data"}', $result->json);
    }

    public function test_build_from_model_with_null_model()
    {
        // Act
        $result = $this->factory->buildFromModel(null);

        // Assert
        $this->assertNull($result);
    }

    public function test_build_from_model_without_message_relation()
    {
        // Arrange
        $model = new class {
            public $id = 1;
            public $message_id = 2;
            public $message = null;
            public $whatsapp_message_id = 'wamid.123456789';
            public $message_status = 'accepted';
            public $wa_id = '557991514957';
            public $input_phone = '+5579991514957';
            public $messaging_product = 'whatsapp';
            public $json = '{"test": "data"}';
            public $created_at;
            public $updated_at;
        };
        $model->created_at = Carbon::parse('2023-01-01 12:00:00');
        $model->updated_at = Carbon::parse('2023-01-01 12:00:00');

        $this->messageFactory
            ->expects($this->never())
            ->method('buildFromModel');

        // Act
        $result = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(WhatsAppMessage::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(2, $result->message_id);
        $this->assertNull($result->message);
    }

    public function test_build_from_meta_api_response()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+5579991514957',
                    'wa_id' => '557991514957'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==',
                    'message_status' => 'accepted'
                ]
            ]
        ];

        // Act
        $result = $this->factory->buildFromMetaApiResponse($messageId, $whatsappResponse);

        // Assert
        $this->assertInstanceOf(WhatsAppMessage::class, $result);
        $this->assertNull($result->id); // Should be null before saving
        $this->assertEquals(123, $result->message_id);
        $this->assertNull($result->message); // Should be null initially
        $this->assertEquals('wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==', $result->whatsapp_message_id);
        $this->assertEquals('accepted', $result->message_status);
        $this->assertEquals('557991514957', $result->wa_id);
        $this->assertEquals('+5579991514957', $result->input_phone);
        $this->assertEquals('whatsapp', $result->messaging_product);
        $this->assertEquals(json_encode($whatsappResponse), $result->json);
        $this->assertNull($result->created_at);
        $this->assertNull($result->updated_at);
    }

    public function test_build_from_meta_api_response_with_missing_data()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [],
            'messages' => []
        ];

        // Act
        $result = $this->factory->buildFromMetaApiResponse($messageId, $whatsappResponse);

        // Assert
        $this->assertInstanceOf(WhatsAppMessage::class, $result);
        $this->assertEquals(123, $result->message_id);
        $this->assertNull($result->whatsapp_message_id);
        $this->assertNull($result->message_status);
        $this->assertNull($result->wa_id);
        $this->assertNull($result->input_phone);
        $this->assertEquals('whatsapp', $result->messaging_product);
        $this->assertEquals(json_encode($whatsappResponse), $result->json);
    }

    public function test_build_from_meta_api_response_with_default_messaging_product()
    {
        // Arrange
        $messageId = 123;
        $whatsappResponse = [
            'contacts' => [
                [
                    'input' => '+5579991514957',
                    'wa_id' => '557991514957'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.123456789',
                    'message_status' => 'accepted'
                ]
            ]
        ];

        // Act
        $result = $this->factory->buildFromMetaApiResponse($messageId, $whatsappResponse);

        // Assert
        $this->assertEquals('whatsapp', $result->messaging_product); // Should default to 'whatsapp'
    }

    public function test_build_from_model()
    {
        // Arrange
        $model = $this->createModelInstance();

        // Act
        $result = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf($this->getDomainClass(), $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals(2, $result->message_id);
    }

    protected function createFactoryInstance()
    {
        return new WhatsAppMessageFactory($this->messageFactory);
    }

    protected function getDomainClass(): string
    {
        return WhatsAppMessage::class;
    }

    protected function createModelInstance()
    {
        $model = new class {
            public $id = 1;
            public $message_id = 2;
            public $message = null;
            public $whatsapp_message_id = 'wamid.123456789';
            public $message_status = 'accepted';
            public $wa_id = '557991514957';
            public $input_phone = '+5579991514957';
            public $messaging_product = 'whatsapp';
            public $json = '{"test": "data"}';
            public $created_at;
            public $updated_at;
        };
        $model->created_at = Carbon::parse('2023-01-01 12:00:00');
        $model->updated_at = Carbon::parse('2023-01-01 12:00:00');

        return $model;
    }

    protected function getExpectedMethods(): array
    {
        return [
            'buildFromModel',
            'buildFromMetaApiResponse'
        ];
    }
}
