<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Factories;

use Tests\Unit\Factories\BaseFactoryTest;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate as WhatsAppTemplateModel;
use App\Domains\ChatBot\Template;
use App\Factories\ChatBot\TemplateFactory;
use App\Models\Template as TemplateModel;
use Carbon\Carbon;

class WhatsAppTemplateFactoryTest extends BaseFactoryTest
{
    protected WhatsAppTemplateFactory $factory;
    protected $mockTemplateFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockTemplateFactory = $this->createMock(TemplateFactory::class);
        $this->factory = new WhatsAppTemplateFactory($this->mockTemplateFactory);
    }

    public function test_build_from_model()
    {
        // Arrange
        $templateModel = TemplateModel::factory()->create([
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US'
        ]);

        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => $templateModel->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_123',
            'json' => '{"status": "APPROVED", "name": "test_template"}'
        ]);

        // Load the template relationship
        $whatsAppTemplateModel->load('template');

        $templateDomain = $this->createMock(Template::class);

        $this->mockTemplateFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($templateModel)
            ->willReturn($templateDomain);

        // Act
        $result = $this->factory->buildFromModel($whatsAppTemplateModel);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals($whatsAppTemplateModel->template_id, $result->template_id);
        $this->assertInstanceOf(Template::class, $result->template);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_123', $result->external_id);
        $this->assertEquals('{"status": "APPROVED", "name": "test_template"}', $result->json);
        $this->assertInstanceOf(Carbon::class, $result->created_at);
        $this->assertInstanceOf(Carbon::class, $result->updated_at);
    }

    public function test_build_from_model_with_null_template()
    {
        // Arrange
        $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
            'template_id' => 1,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_456',
            'json' => '{"status": "PENDING"}'
        ]);

        // Don't load template relationship, so it will be null
        $whatsAppTemplateModel->template = null;

        $this->mockTemplateFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->factory->buildFromModel($whatsAppTemplateModel);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($whatsAppTemplateModel->id, $result->id);
        $this->assertEquals($whatsAppTemplateModel->template_id, $result->template_id);
        $this->assertNull($result->template);
        $this->assertEquals('PENDING', $result->status);
        $this->assertEquals('whatsapp_456', $result->external_id);
    }

    public function test_build_from_template()
    {
        // Arrange
        $template = $this->createMock(Template::class);
        $template->id = 1;

        $status = 'APPROVED';
        $externalId = 'whatsapp_789';
        $json = '{"status": "APPROVED", "id": "whatsapp_789"}';

        // Act
        $result = $this->factory->buildFromTemplate($template, $status, $externalId, $json);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNull($result->id); // Should be null for new instances
        $this->assertEquals(1, $result->template_id);
        $this->assertInstanceOf(Template::class, $result->template);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_789', $result->external_id);
        $this->assertEquals('{"status": "APPROVED", "id": "whatsapp_789"}', $result->json);
        $this->assertNull($result->created_at); // Should be null for new instances
        $this->assertNull($result->updated_at); // Should be null for new instances
    }

    public function test_build_from_template_with_null_values()
    {
        // Arrange
        $template = $this->createMock(Template::class);
        $template->id = 2;

        // Act
        $result = $this->factory->buildFromTemplate($template, null, null, null);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNull($result->id);
        $this->assertEquals(2, $result->template_id);
        $this->assertInstanceOf(Template::class, $result->template);
        $this->assertNull($result->status);
        $this->assertNull($result->external_id);
        $this->assertNull($result->json);
        $this->assertNull($result->created_at);
        $this->assertNull($result->updated_at);
    }

    public function test_build_from_template_with_empty_strings()
    {
        // Arrange
        $template = $this->createMock(Template::class);
        $template->id = 3;

        // Act
        $result = $this->factory->buildFromTemplate($template, '', '', '');

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals(3, $result->template_id);
        $this->assertEquals('', $result->status);
        $this->assertEquals('', $result->external_id);
        $this->assertEquals('', $result->json);
    }

    public function test_build_from_template_with_complex_json()
    {
        // Arrange
        $template = $this->createMock(Template::class);
        $template->id = 4;

        $complexJson = json_encode([
            'status' => 'APPROVED',
            'id' => 'whatsapp_complex',
            'name' => 'complex_template',
            'components' => [
                ['type' => 'HEADER', 'format' => 'TEXT'],
                ['type' => 'BODY', 'text' => 'Hello {{1}}'],
                ['type' => 'FOOTER', 'text' => 'Thank you']
            ],
            'language' => 'en_US',
            'category' => 'UTILITY'
        ]);

        // Act
        $result = $this->factory->buildFromTemplate(
            $template,
            'APPROVED',
            'whatsapp_complex',
            $complexJson
        );

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals(4, $result->template_id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_complex', $result->external_id);
        $this->assertEquals($complexJson, $result->json);

        // Verify JSON can be decoded
        $decodedJson = json_decode($result->json, true);
        $this->assertIsArray($decodedJson);
        $this->assertEquals('APPROVED', $decodedJson['status']);
        $this->assertCount(3, $decodedJson['components']);
    }

    public function test_build_from_model_handles_different_status_values()
    {
        $statuses = ['PENDING', 'APPROVED', 'REJECTED', 'DISABLED', null];

        foreach ($statuses as $status) {
            // Arrange
            $whatsAppTemplateModel = WhatsAppTemplateModel::factory()->create([
                'template_id' => 1,
                'status' => $status,
                'external_id' => 'whatsapp_status_test',
                'json' => json_encode(['status' => $status])
            ]);

            $this->mockTemplateFactory
                ->method('buildFromModel')
                ->willReturn(null);

            // Act
            $result = $this->factory->buildFromModel($whatsAppTemplateModel);

            // Assert
            $this->assertInstanceOf(WhatsAppTemplate::class, $result);
            $this->assertEquals($status, $result->status);
        }
    }

    public function test_build_from_model_preserves_timestamps()
    {
        // Arrange
        $createdAt = Carbon::create(2023, 1, 15, 10, 30, 45);
        $updatedAt = Carbon::create(2023, 2, 20, 14, 15, 30);

        $whatsAppTemplateModel = new WhatsAppTemplateModel([
            'id' => 1,
            'template_id' => 1,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_timestamp_test',
            'json' => '{"status": "APPROVED"}'
        ]);
        $whatsAppTemplateModel->created_at = $createdAt;
        $whatsAppTemplateModel->updated_at = $updatedAt;
        $whatsAppTemplateModel->template = null;

        $this->mockTemplateFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->willReturn(null);

        // Act
        $result = $this->factory->buildFromModel($whatsAppTemplateModel);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($createdAt, $result->created_at);
        $this->assertEquals($updatedAt, $result->updated_at);
    }

    protected function createFactoryInstance()
    {
        return $this->factory;
    }

    protected function getDomainClass(): string
    {
        return WhatsAppTemplate::class;
    }

    protected function createModelInstance()
    {
        return WhatsAppTemplateModel::factory()->create();
    }
}
