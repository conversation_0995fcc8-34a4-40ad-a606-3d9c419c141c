<?php

namespace Tests\Unit\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\MessageService;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Repositories\MessageRepository;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;
use Carbon\Carbon;
use Exception;

class MessageServiceTest extends TestCase
{
    use RefreshDatabase;

    protected MessageService $messageService;
    protected $mockMessageRepository;
    protected $mockWhatsAppTemplateRepository;
    protected $mockGuzzleClient;
    protected PhoneNumber $phoneNumber;
    protected Message $message;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock dependencies
        $this->mockMessageRepository = $this->createMock(MessageRepository::class);
        $this->mockWhatsAppTemplateRepository = $this->createMock(WhatsAppTemplateRepository::class);
        $this->mockGuzzleClient = $this->createMock(GuzzleClient::class);

        // Create test phone number
        $this->phoneNumber = new PhoneNumber(
            1,
            1,
            1,
            null,
            null,
            '+5511999999999',
            'Test Phone',
            'Test Description',
            true,
            'whatsapp_phone_id_123',
            'test_access_token',
            Carbon::now(),
            Carbon::now()
        );

        // Create test client
        $this->client = $this->createMock(Client::class);
        $this->client->phone = '+5511888888888';

        // Create test message
        $this->message = $this->createMock(Message::class);
        $this->message->template_id = 1;
        $this->message->client = $this->client;

        // Create service instance
        $this->messageService = new MessageService(
            $this->mockMessageRepository,
            $this->mockWhatsAppTemplateRepository,
            $this->phoneNumber
        );

        // Replace the HTTP client with our mock using reflection
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $this->mockGuzzleClient);
    }

    public function test_send_message_success()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $whatsAppPayload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5511888888888',
            'type' => 'template',
            'template' => [
                'name' => 'test_template',
                'language' => ['code' => 'en_US']
            ]
        ];

        $apiResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                ['input' => '+5511888888888', 'wa_id' => '5511888888888']
            ],
            'messages' => [
                ['id' => 'wamid.123456789']
            ]
        ];

        // Set up repository mock
        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        // Set up message mock
        $this->message
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        // Set up HTTP client mock
        $httpResponse = new Response(200, [], json_encode($apiResponse));
        $this->mockGuzzleClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        // Act
        $result = $this->messageService->send($this->message);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('whatsapp', $result['messaging_product']);
        $this->assertArrayHasKey('contacts', $result);
        $this->assertArrayHasKey('messages', $result);
        $this->assertEquals('wamid.123456789', $result['messages'][0]['id']);
    }

    public function test_send_message_fails_when_template_not_found()
    {
        // Arrange
        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn(null);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Template is not published to WhatsApp');

        $this->messageService->send($this->message);
    }

    public function test_send_message_fails_when_template_has_no_external_id()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'PENDING',
            null, // No external_id
            '{"status": "PENDING"}'
        );

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Template is not published to WhatsApp');

        $this->messageService->send($this->message);
    }

    public function test_send_message_fails_when_client_is_null()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $messageWithoutClient = $this->createMock(Message::class);
        $messageWithoutClient->template_id = 1;
        $messageWithoutClient->client = null;

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Client has no phone number');

        $this->messageService->send($messageWithoutClient);
    }

    public function test_send_message_fails_when_client_has_no_phone()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $clientWithoutPhone = $this->createMock(Client::class);
        $clientWithoutPhone->phone = null;

        $messageWithoutPhone = $this->createMock(Message::class);
        $messageWithoutPhone->template_id = 1;
        $messageWithoutPhone->client = $clientWithoutPhone;

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Client has no phone number');

        $this->messageService->send($messageWithoutPhone);
    }

    public function test_send_message_handles_http_exception()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $whatsAppPayload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5511888888888',
            'type' => 'template'
        ];

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        $this->message
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $guzzleException = $this->createMock(GuzzleException::class);
        $this->mockGuzzleClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException($guzzleException);

        // Act & Assert
        $this->expectException(GuzzleException::class);

        $this->messageService->send($this->message);
    }

    public function test_send_message_handles_invalid_json_response()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $whatsAppPayload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5511888888888',
            'type' => 'template'
        ];

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        $this->message
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $httpResponse = new Response(200, [], 'invalid json');
        $this->mockGuzzleClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        // Act
        $result = $this->messageService->send($this->message);

        // Assert
        $this->assertNull($result);
    }

    public function test_send_message_with_error_response()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            null,
            'APPROVED',
            'whatsapp_template_123',
            '{"status": "APPROVED"}'
        );

        $whatsAppPayload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5511888888888',
            'type' => 'template'
        ];

        $errorResponse = [
            'error' => [
                'message' => 'Invalid phone number',
                'type' => 'OAuthException',
                'code' => 100
            ]
        ];

        $this->mockWhatsAppTemplateRepository
            ->expects($this->once())
            ->method('fetchByTemplateId')
            ->with(1)
            ->willReturn($whatsAppTemplate);

        $this->message
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $httpResponse = new Response(400, [], json_encode($errorResponse));
        $this->mockGuzzleClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        // Act
        $result = $this->messageService->send($this->message);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals('Invalid phone number', $result['error']['message']);
    }

    public function test_constructor_sets_endpoint()
    {
        // Act
        $service = new MessageService(
            $this->mockMessageRepository,
            $this->mockWhatsAppTemplateRepository,
            $this->phoneNumber
        );

        // Assert
        $reflection = new \ReflectionClass($service);
        $endpointProperty = $reflection->getProperty('endpoint');
        $endpointProperty->setAccessible(true);
        $endpoint = $endpointProperty->getValue($service);

        $this->assertEquals('messages', $endpoint);
    }
}
