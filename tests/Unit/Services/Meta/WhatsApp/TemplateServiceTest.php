<?php

namespace Tests\Unit\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\TemplateService;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\PhoneNumber;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;
use Carbon\Carbon;
use Exception;

class TemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TemplateService $templateService;
    protected $mockRepository;
    protected $mockFactory;
    protected $mockClient;
    protected PhoneNumber $phoneNumber;
    protected Template $template;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock dependencies
        $this->mockRepository = $this->createMock(WhatsAppTemplateRepository::class);
        $this->mockFactory = $this->createMock(WhatsAppTemplateFactory::class);
        $this->mockClient = $this->createMock(Client::class);

        // Create test phone number
        $this->phoneNumber = new PhoneNumber(
            1,
            1,
            1,
            null,
            null,
            '+5511999999999',
            'Test Phone',
            'Test Description',
            true,
            'whatsapp_phone_id_123',
            'test_access_token',
            Carbon::now(),
            Carbon::now()
        );

        // Create test template
        $this->template = $this->createMock(Template::class);

        // Create service instance
        $this->templateService = new TemplateService(
            $this->mockRepository,
            $this->mockFactory,
            $this->phoneNumber
        );

        // Replace the HTTP client with our mock using reflection
        $reflection = new \ReflectionClass($this->templateService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->templateService, $this->mockClient);
    }

    public function test_register_template_success()
    {
        // Arrange
        $whatsAppPayload = [
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'components' => []
        ];

        $apiResponse = [
            'id' => 'whatsapp_template_123',
            'status' => 'PENDING',
            'name' => 'test_template'
        ];

        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            1,
            $this->template,
            'PENDING',
            'whatsapp_template_123',
            json_encode($apiResponse)
        );

        $savedWhatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            $this->template,
            'PENDING',
            'whatsapp_template_123',
            json_encode($apiResponse)
        );

        // Set up template mock
        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(true);

        $this->template
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        // Set up HTTP client mock
        $httpResponse = new Response(200, [], json_encode($apiResponse));
        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        // Set up factory mock
        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromTemplate')
            ->with(
                $this->template,
                'PENDING',
                'whatsapp_template_123',
                json_encode($apiResponse)
            )
            ->willReturn($whatsAppTemplate);

        // Set up repository mock
        $this->mockRepository
            ->expects($this->once())
            ->method('save')
            ->with($whatsAppTemplate)
            ->willReturn($savedWhatsAppTemplate);

        // Act
        $result = $this->templateService->register($this->template);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('PENDING', $result->status);
        $this->assertEquals('whatsapp_template_123', $result->external_id);
    }

    public function test_register_template_fails_validation()
    {
        // Arrange
        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(false);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Template is not valid for WhatsApp');

        $this->templateService->register($this->template);
    }

    public function test_register_template_handles_http_exception()
    {
        // Arrange
        $whatsAppPayload = [
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'components' => []
        ];

        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(true);

        $this->template
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $guzzleException = $this->createMock(GuzzleException::class);
        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException($guzzleException);

        // Act & Assert
        $this->expectException(GuzzleException::class);

        $this->templateService->register($this->template);
    }

    public function test_register_template_handles_invalid_api_response()
    {
        // Arrange
        $whatsAppPayload = [
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'components' => []
        ];

        $invalidApiResponse = 'invalid json response';

        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(true);

        $this->template
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $httpResponse = new Response(200, [], $invalidApiResponse);
        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            1,
            $this->template,
            null,
            null,
            $invalidApiResponse
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromTemplate')
            ->with(
                $this->template,
                null,
                null,
                $invalidApiResponse
            )
            ->willReturn($whatsAppTemplate);

        $savedWhatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            $this->template,
            null,
            null,
            $invalidApiResponse
        );

        $this->mockRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedWhatsAppTemplate);

        // Act
        $result = $this->templateService->register($this->template);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNull($result->status);
        $this->assertNull($result->external_id);
    }

    public function test_register_template_with_missing_status_in_response()
    {
        // Arrange
        $whatsAppPayload = [
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'components' => []
        ];

        $apiResponse = [
            'id' => 'whatsapp_template_123',
            'name' => 'test_template'
            // Missing 'status' field
        ];

        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(true);

        $this->template
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $httpResponse = new Response(200, [], json_encode($apiResponse));
        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            1,
            $this->template,
            null, // status is null
            'whatsapp_template_123',
            json_encode($apiResponse)
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromTemplate')
            ->with(
                $this->template,
                null,
                'whatsapp_template_123',
                json_encode($apiResponse)
            )
            ->willReturn($whatsAppTemplate);

        $savedWhatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            $this->template,
            null,
            'whatsapp_template_123',
            json_encode($apiResponse)
        );

        $this->mockRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedWhatsAppTemplate);

        // Act
        $result = $this->templateService->register($this->template);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertNull($result->status);
        $this->assertEquals('whatsapp_template_123', $result->external_id);
    }

    public function test_register_template_with_missing_id_in_response()
    {
        // Arrange
        $whatsAppPayload = [
            'name' => 'test_template',
            'category' => 'UTILITY',
            'language' => 'en_US',
            'components' => []
        ];

        $apiResponse = [
            'status' => 'PENDING',
            'name' => 'test_template'
            // Missing 'id' field
        ];

        $this->template
            ->expects($this->once())
            ->method('validateForWhatsApp')
            ->willReturn(true);

        $this->template
            ->expects($this->once())
            ->method('toWhatsAppPayload')
            ->willReturn($whatsAppPayload);

        $httpResponse = new Response(200, [], json_encode($apiResponse));
        $this->mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($httpResponse);

        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            1,
            $this->template,
            'PENDING',
            null, // external_id is null
            json_encode($apiResponse)
        );

        $this->mockFactory
            ->expects($this->once())
            ->method('buildFromTemplate')
            ->with(
                $this->template,
                'PENDING',
                null,
                json_encode($apiResponse)
            )
            ->willReturn($whatsAppTemplate);

        $savedWhatsAppTemplate = new WhatsAppTemplate(
            1,
            1,
            $this->template,
            'PENDING',
            null,
            json_encode($apiResponse)
        );

        $this->mockRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedWhatsAppTemplate);

        // Act
        $result = $this->templateService->register($this->template);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals('PENDING', $result->status);
        $this->assertNull($result->external_id);
    }
}
