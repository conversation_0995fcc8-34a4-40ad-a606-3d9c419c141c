<?php

namespace Tests\Unit\Services\Telegram;

use App\Services\Telegram\UseCases\Flows\ReadDocText\SendDocumentToWebhook;
use App\Services\Telegram\Telegram;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\TestCase;
use Mockery;
use ReflectionClass;

class SendDocumentToWebhookTest extends TestCase
{
    private $mockTelegram;
    private $sendDocumentToWebhook;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockTelegram = Mockery::mock(Telegram::class);
        $this->mockTelegram->telegramChat = (object) [
            'organization_id' => 1,
            'user_id' => 1,
            'ocr_data' => '{"test": "data"}'
        ];
        
        $this->sendDocumentToWebhook = new SendDocumentToWebhook($this->mockTelegram);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_clean_json_data_with_callback_data_structure()
    {
        $dirtyData = '{"callback_data":"{\n   \"paciente\": \"<PERSON>\",\n   \"data_nascimento\": \"20\/09\/1973\",\n   \"idade\": \"51 anos\",\n   \"sexo\": \"Masculino\",\n   \"convenio\": \"Bradesco Saúde\"\n}"}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_clean_json_data_with_escaped_quotes()
    {
        $dirtyData = '"{\"paciente\":\"Carlos Jose Gomes da Silva\",\"data_nascimento\":\"20\\\/09\\\/1973\",\"convenio\":\"Bradesco Sa\\u00fade\"}"';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_clean_json_data_with_non_breaking_spaces()
    {
        $dirtyData = '{\n\u00a0\u00a0\u00a0 "paciente": "Carlos Jose Gomes da Silva",\n\u00a0\u00a0\u00a0 "convenio": "Bradesco Saúde"\n}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_clean_json_data_with_double_escaped_slashes()
    {
        $dirtyData = '{"data_nascimento":"20\\\/09\\\/1973","inicio_cirurgia":"26\\\/11\\\/2024 08:30"}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('26/11/2024 08:30', $decoded['inicio_cirurgia']);
    }

    public function test_clean_json_data_with_complex_nested_structure()
    {
        $dirtyData = '{"procedimentos":[{"codigo":"31009336","descricao":"Herniorrafia Inguinal"}],"equipe_cirurgica":{"cirurgiao_principal":"Rafael Moraes Tavares"}}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertIsArray($decoded['procedimentos']);
        $this->assertEquals('31009336', $decoded['procedimentos'][0]['codigo']);
        $this->assertIsArray($decoded['equipe_cirurgica']);
        $this->assertEquals('Rafael Moraes Tavares', $decoded['equipe_cirurgica']['cirurgiao_principal']);
    }

    public function test_clean_json_data_with_materials_array()
    {
        $dirtyData = '{"materiais":[{"item":"Materials: SF 500ml","quantidade":"01"},{"item":"OPME: Trocater 10mm descartável","quantidade":"02"}]}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertIsArray($decoded['materiais']);
        $this->assertCount(2, $decoded['materiais']);
        $this->assertEquals('Materials: SF 500ml', $decoded['materiais'][0]['item']);
        $this->assertEquals('OPME: Trocater 10mm descartável', $decoded['materiais'][1]['item']);
    }

    public function test_clean_json_data_with_empty_input()
    {
        $result = $this->callPrivateMethod('cleanJsonData', ['']);
        $this->assertEquals('{}', $result);
        
        $result = $this->callPrivateMethod('cleanJsonData', [null]);
        $this->assertEquals('{}', $result);
    }

    public function test_clean_json_data_with_array_input()
    {
        $arrayData = [
            'paciente' => 'Carlos Jose Gomes da Silva',
            'data_nascimento' => '20/09/1973',
            'convenio' => 'Bradesco Saúde'
        ];
        
        $result = $this->callPrivateMethod('cleanJsonData', [$arrayData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_clean_json_data_preserves_unicode_characters()
    {
        $dirtyData = '{"convenio":"Bradesco Saúde","setor":"HP-Centro Cirúrgico","diagnostico":"hérnia inguinal"}';
        
        $result = $this->callPrivateMethod('cleanJsonData', [$dirtyData]);
        $decoded = json_decode($result, true);
        
        $this->assertIsArray($decoded);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
        $this->assertEquals('HP-Centro Cirúrgico', $decoded['setor']);
        $this->assertEquals('hérnia inguinal', $decoded['diagnostico']);
    }

    public function test_clean_json_data_with_real_world_telegram_data()
    {
        // This is the actual problematic data from the logs
        $realWorldData = '"{\"callback_data\":\"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"convenio\\\": \\\"Bradesco Sa\\u00fade\\\"\\n}\"}"';

        $result = $this->callPrivateMethod('cleanJsonData', [$realWorldData]);
        $decoded = json_decode($result, true);

        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_normalize_telegram_format_with_raw_data()
    {
        // Telegram format with raw_data structure
        $telegramData = '{"raw_data":"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"convenio\\\": \\\"Bradesco Sa\\u00fade\\\"\\n}"}';

        $result = $this->callPrivateMethod('cleanJsonData', [$telegramData]);
        $decoded = json_decode($result, true);

        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_normalize_database_format()
    {
        // Database format with heavy escaping
        $databaseData = '"{\\\"paciente\\\":\\\"Carlos Jose Gomes da Silva\\\",\\\"data_nascimento\\\":\\\"20\\\\\\\/09\\\\\\\/1973\\\",\\\"convenio\\\":\\\"Bradesco Sa\\\\u00fade\\\"}"';

        $result = $this->callPrivateMethod('cleanJsonData', [$databaseData]);
        $decoded = json_decode($result, true);

        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data_nascimento']);
        $this->assertEquals('Bradesco Saúde', $decoded['convenio']);
    }

    public function test_normalize_telegram_format_complex_data()
    {
        // Complex Telegram data with nested structures
        $complexTelegramData = '{"raw_data":"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"procedimentos\\\": [\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"codigo\\\": \\\"31009336\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 }\\n\\u00a0\\u00a0\\u00a0 ]\\n}"}';

        $result = $this->callPrivateMethod('cleanJsonData', [$complexTelegramData]);
        $decoded = json_decode($result, true);

        $this->assertIsArray($decoded);
        $this->assertEquals('Carlos Jose Gomes da Silva', $decoded['paciente']);
        $this->assertIsArray($decoded['procedimentos']);
        $this->assertEquals('31009336', $decoded['procedimentos'][0]['codigo']);
    }

    public function test_detect_and_normalize_format_telegram()
    {
        $telegramData = '{"raw_data":"{\\"paciente\\": \\"Test\\"}"}';

        $result = $this->callPrivateMethod('detectAndNormalizeFormat', [$telegramData]);

        $this->assertIsString($result);
        $decoded = json_decode($result, true);
        $this->assertIsArray($decoded);
        $this->assertEquals('Test', $decoded['paciente']);
    }

    public function test_detect_and_normalize_format_database()
    {
        $databaseData = '"{\\\"paciente\\\":\\\"Test\\\",\\\"data\\\":\\\"20\\\\\\\/09\\\\\\\/1973\\\"}"';

        $result = $this->callPrivateMethod('detectAndNormalizeFormat', [$databaseData]);

        $this->assertIsString($result);
        $decoded = json_decode($result, true);
        $this->assertIsArray($decoded);
        $this->assertEquals('Test', $decoded['paciente']);
        $this->assertEquals('20/09/1973', $decoded['data']);
    }

    /**
     * Helper method to call private methods for testing
     */
    private function callPrivateMethod($methodName, $parameters = [])
    {
        $reflection = new ReflectionClass($this->sendDocumentToWebhook);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($this->sendDocumentToWebhook, $parameters);
    }
}
