# CustomProduct Module Test Suite Documentation

## Overview

This document provides a comprehensive overview of the test suite implemented for the CustomProduct module in the Inventory system. The test suite covers all layers of the application architecture with both unit and integration tests, following the same high-quality patterns established for the Client, Project, Product, Stock, Budget, Batch, Brand, Sale, Shop, StockEntry, StockExit, Item, and BudgetProduct modules.

## Test Structure

### 1. Domain Tests (`tests/Unit/Domains/Inventory/CustomProductTest.php`)

**Purpose**: Test the CustomProduct domain object and its methods in isolation.

**Coverage**:
- ✅ Domain instantiation with all properties (project_id, budget_id, quantity, value, description)
- ✅ Domain instantiation with minimal data
- ✅ `toArray()` method functionality with relationships
- ✅ `toStoreArray()` method (excludes id, timestamps)
- ✅ `toUpdateArray()` method (excludes id, timestamps)
- ✅ Date handling (null dates use Carbon::now())
- ✅ Quantity and value handling (zero, high, decimal values)
- ✅ Description handling (including long descriptions)
- ✅ Relationship handling (Project, Budget)

**Key Test Cases**:
- Custom products with complete data (all relationships)
- Minimal custom products (required fields only)
- Zero, high, and decimal quantity/value handling
- Array conversion methods with proper field exclusions
- Relationship handling with Project and Budget objects
- Long description handling

### 2. Factory Tests (`tests/Unit/Factories/Inventory/CustomProductFactoryTest.php`)

**Purpose**: Test the CustomProductFactory's ability to build domain objects from various sources.

**Coverage**:
- ✅ `buildFromModel()` - Convert Eloquent model to domain with relationships
- ✅ `buildFromStoreRequest()` - Build from HTTP store request
- ✅ `buildFromUpdateRequest()` - Build from HTTP update request
- ✅ Relationship loading control (with_project, with_budget parameters)
- ✅ Null handling for all methods
- ✅ Quantity and value variations
- ✅ Description variations (including long descriptions)
- ✅ Various quantities and values testing

**Key Test Cases**:
- Model to domain conversion with relationship loading
- Request to domain conversion with proper null handling
- Quantity and value handling across all methods
- Relationship loading control testing
- Long description handling

### 3. Repository Tests (`tests/Unit/Repositories/CustomProductRepositoryTest.php`)

**Purpose**: Test the CustomProductRepository's data access operations.

**Coverage**:
- ✅ `fetchAll()` with pagination
- ✅ `fetchFromOrganization()` with organization isolation
- ✅ `fetchFromCustom()` with project-specific filtering
- ✅ `fetchById()` with success and not found scenarios
- ✅ `store()` - Create new custom products
- ✅ `update()` - Modify existing custom products
- ✅ `delete()` - Hard delete custom products (no soft delete)
- ✅ Organization isolation via project and budget relationships
- ✅ Quantity and value handling (zero, high, decimal)
- ✅ Description handling (including long descriptions)
- ✅ Various quantities and values testing

**Key Test Cases**:
- Pagination with different limits
- Organization-based data isolation via relationships
- Project-specific custom product retrieval
- Hard delete behavior verification
- Quantity and value range handling
- Multiple custom products from same project
- Mixed project and budget organization filtering

### 4. UseCase Tests (`tests/Unit/UseCases/Inventory/CustomProduct/`)

**Purpose**: Test business logic and orchestration in UseCases.

#### Store UseCase (`StoreTest.php`)
- ✅ Successful custom product creation
- ✅ Different quantities and values handling
- ✅ Zero and high quantity handling
- ✅ Factory and repository exception handling
- ✅ Decimal values handling
- ✅ Long description handling

#### Update UseCase (`UpdateTest.php`)
- ✅ Successful custom product updates
- ✅ ID assignment from parameter
- ✅ Different quantities and values handling
- ✅ Zero quantity handling
- ✅ Factory and repository exception handling
- ✅ Decimal values and long description handling

#### Delete UseCase (`DeleteTest.php`)
- ✅ Successful deletion with hard delete
- ✅ Repository exception handling
- ✅ Different custom product types handling
- ✅ Zero quantity and high value handling
- ✅ Long description handling
- ✅ Various project and budget ID combinations

#### Get UseCase (`GetTest.php`)
- ✅ Retrieve custom product by ID
- ✅ Handle not found scenarios
- ✅ Different custom product types and value ranges
- ✅ Complete property verification
- ✅ Quantity and value variations
- ✅ Various project and budget ID combinations
- ✅ Long description handling

#### GetAll UseCase (`GetAllTest.php`)
- ✅ Paginated results with organization filtering
- ✅ Empty results handling
- ✅ Pagination support
- ✅ Error handling
- ✅ Various custom product types handling
- ✅ Multiple projects and budgets handling
- ✅ Long description handling

### 5. Integration/Feature Tests (`tests/Feature/Api/Inventory/CustomProductTest.php`)

**Purpose**: Test complete HTTP request-to-response flows through all application layers.

**Coverage**:
- ✅ Custom product creation with proper JSON structure
- ✅ Custom product retrieval with proper JSON structure
- ✅ Custom product updates with proper JSON structure
- ✅ Custom product deletion with hard delete verification
- ✅ Organization-based access control via relationships
- ✅ Pagination via HTTP
- ✅ Project-specific custom product retrieval via HTTP
- ✅ Validation error handling
- ✅ Data consistency across operations
- ✅ Hard delete behavior
- ✅ Quantity and value variations (zero, high, decimal)
- ✅ Organization isolation testing via relationships
- ✅ Multiple custom products from same project
- ✅ Long description handling
- ✅ Complete CRUD workflow testing
- ✅ Mixed project-budget relationship handling

**Key Test Cases**:
- Complete CRUD operations via API
- Authentication and authorization
- Input validation and error responses
- Data transformation and consistency
- Quantity and value management
- Project-specific custom product management
- Organization isolation verification via relationships
- Long description support
- Mixed relationship scenarios

## Test Execution

### Running All CustomProduct Tests
```bash
# Run all custom product-related tests
php artisan test tests/Unit/Domains/Inventory/CustomProductTest.php
php artisan test tests/Unit/Factories/Inventory/CustomProductFactoryTest.php
php artisan test tests/Unit/Repositories/CustomProductRepositoryTest.php
php artisan test tests/Unit/UseCases/Inventory/CustomProduct/
php artisan test tests/Feature/Api/Inventory/CustomProductTest.php

# Run all tests with coverage
php artisan test --coverage
```

### Test Categories
- **Unit Tests**: 10 test classes, ~180 test methods
- **Integration Tests**: 1 test class, ~25 test methods
- **Total Coverage**: ~205 test methods covering all CustomProduct module functionality

## Test Quality Features

### 1. Comprehensive Business Logic Testing
- **Custom Product Management**: Complete CRUD operations
- **Project Integration**: Custom products belong to specific projects
- **Budget Integration**: Custom products reference specific budgets
- **Organization Isolation**: Indirect organization-based access control via relationships
- **Data Validation**: Quantity, value, and description validation

### 2. Advanced Repository Testing
- **Project-Specific Queries**: Fetch custom products from specific projects
- **Hard Delete**: Proper hard delete behavior verification (no soft delete)
- **Numeric Handling**: Zero, high, and decimal quantity/value handling
- **Organization Filtering**: Organization-based data isolation via relationships
- **Relationship Loading**: Conditional relationship loading
- **Description Handling**: Support for long descriptions

### 3. UseCase Complexity
- **Relationship Validation**: Consistent across all operations
- **Error Handling**: Comprehensive exception scenarios
- **Data Transformation**: Request to domain conversion
- **Project Integration**: Project-specific custom product operations

### 4. Integration Test Completeness
- **Project Integration**: Complete project-custom product relationship workflows
- **Budget Integration**: Complete budget-custom product relationship workflows
- **Data Consistency**: Verification across multiple operations
- **Organization Security**: Indirect access control testing via relationships
- **Numeric Validation**: Quantity and value handling
- **Relationship Integration**: Complete relationship workflows
- **Description Support**: Long description handling

## Key Features of CustomProduct Module

### 1. CustomProduct Properties
- **Core Info**: Quantity (required), value (required), description (required)
- **Relationships**: Project (required), Budget (required)
- **Organization**: Indirect organization-based isolation via relationships
- **Project Integration**: Custom products belong to specific projects
- **Budget Integration**: Custom products reference specific budgets

### 2. Advanced Functionality
- **Project Management**: Custom products are grouped by projects
- **Budget Integration**: Custom products reference specific budgets
- **Relationship Management**: Complex relationship handling with conditional loading
- **Description Support**: Full support for long descriptions

### 3. Business Rules
- **Organization Isolation**: Custom products belong to organizations via project/budget relationships
- **Project Integration**: Custom products must belong to a project
- **Budget Integration**: Custom products must reference a budget
- **Hard Delete**: Custom products are hard deleted (no soft delete)
- **Required Relationships**: Project and Budget are required
- **Description Required**: Description is a required field

## Coverage Metrics

The test suite provides comprehensive coverage of:
- **Domain Logic**: 100% of CustomProduct domain methods
- **Factory Methods**: 100% of CustomProductFactory methods
- **Repository Operations**: 100% of CustomProductRepository methods
- **UseCase Logic**: 100% of all CustomProduct UseCases (5 total)
- **API Endpoints**: 100% of CustomProduct API routes
- **Error Scenarios**: Comprehensive error handling coverage
- **Business Rules**: All domain-specific rules
- **Project Integration**: Complete project-custom product relationship workflows
- **Budget Integration**: Complete budget-custom product relationship workflows

## Maintenance Guidelines

### Adding New Tests
1. Follow the established naming conventions
2. Use appropriate mocking for unit tests
3. Include both success and failure scenarios
4. Test edge cases including numeric variations and long descriptions
5. Maintain organization isolation via relationship testing
6. Test project and budget integration scenarios

### Project Integration Testing
- Always test project-custom product relationships
- Verify project-specific custom product retrieval
- Test project integration across operations
- Validate project-based filtering

### Budget Integration Testing
- Always test budget-custom product relationships
- Verify budget integration across operations
- Test budget-based filtering and relationships
- Validate budget consistency

### Relationship Testing
- Test Project and Budget relationships
- Verify conditional relationship loading
- Test relationship integrity across operations
- Validate relationship-based operations
- Test organization isolation via relationships

This comprehensive test suite ensures the CustomProduct module is robust, maintainable, and handles the complex business logic around custom product management, project integration, budget integration, and relationship management reliably for production use.
