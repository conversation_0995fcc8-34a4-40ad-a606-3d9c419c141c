<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot\Commands;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\UpdateClientData;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Repositories\ClientRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Domains\Client;
use App\Models\Organization;
use Mockery;

class UpdateClientDataTest extends TestCase
{
    use RefreshDatabase;

    protected UpdateClientData $updateClientData;
    protected $mockClientRepository;
    protected $mockDynamicInputService;
    protected Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();

        $this->mockClientRepository = Mockery::mock(ClientRepository::class);
        $this->mockDynamicInputService = Mockery::mock(DynamicInputService::class);

        $this->updateClientData = new UpdateClientData(
            $this->mockClientRepository,
            $this->mockDynamicInputService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_client_name_successfully()
    {
        $config = [
            'field' => 'name',
            'source' => 'last_interaction_input'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Old Name',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'João Silva', null, null, null, now(), null, null, 'text',
            ['text' => ['body' => 'João Silva']]
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->mockDynamicInputService
            ->shouldReceive('updateClientField')
            ->once()
            ->with($client, 'name', 'João Silva')
            ->andReturn(true);

        $result = $this->updateClientData->perform($config, $interaction, $conversation);

        $this->assertEquals('name', $result['field_updated']);
        $this->assertEquals('João Silva', $result['new_value']);
        $this->assertEquals(1, $result['client_id']);
        $this->assertEquals('last_interaction_input', $result['source']);
    }

    public function test_perform_updates_client_email_successfully()
    {
        $config = [
            'field' => 'email',
            'source' => 'last_interaction_input',
            'validation' => 'required|email'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Test Client',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            '<EMAIL>', null, null, null, now(), null, null, 'text',
            ['text' => ['body' => '<EMAIL>']]
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->mockDynamicInputService
            ->shouldReceive('updateClientField')
            ->once()
            ->with($client, 'email', '<EMAIL>')
            ->andReturn(true);

        $result = $this->updateClientData->perform($config, $interaction, $conversation);

        $this->assertEquals('email', $result['field_updated']);
        $this->assertEquals('<EMAIL>', $result['new_value']);
    }

    public function test_perform_fails_when_field_is_missing()
    {
        $config = [
            'source' => 'last_interaction_input'
            // Missing 'field'
        ];

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'test value', null, null, null, now(), null, null, 'text'
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Field is required for update_client command');

        $this->updateClientData->perform($config, $interaction, $conversation);
    }

    public function test_perform_fails_when_client_not_found()
    {
        $config = [
            'field' => 'name',
            'source' => 'last_interaction_input'
        ];

        $conversation = new WhatsAppConversation(1, $this->organization->id, 999, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 999, 1, 1, 1,
            'João Silva', null, null, null, now(), null, null, 'text'
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Client not found for conversation');

        $this->updateClientData->perform($config, $interaction, $conversation);
    }

    public function test_perform_fails_when_validation_fails()
    {
        $config = [
            'field' => 'email',
            'source' => 'last_interaction_input',
            'validation' => 'required|email'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Test Client',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'invalid-email', null, null, null, now(), null, null, 'text'
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->expectException(\Exception::class);

        $this->updateClientData->perform($config, $interaction, $conversation);
    }

    public function test_perform_fails_when_update_fails()
    {
        $config = [
            'field' => 'name',
            'source' => 'last_interaction_input'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Old Name',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'João Silva', null, null, null, now(), null, null, 'text'
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->mockDynamicInputService
            ->shouldReceive('updateClientField')
            ->once()
            ->with($client, 'name', 'João Silva')
            ->andReturn(false);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to update client field: name');

        $this->updateClientData->perform($config, $interaction, $conversation);
    }

    public function test_get_value_from_conversation_data()
    {
        $config = [
            'field' => 'name',
            'source' => 'collected_name'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Old Name',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversationData = [
            'collected_name' => 'João from conversation'
        ];

        $conversation = new WhatsAppConversation(
            1, $this->organization->id, 1, 1, 1, 1,
            json_encode($conversationData)
        );

        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'current input', null, null, null, now(), null, null, 'text'
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->mockDynamicInputService
            ->shouldReceive('updateClientField')
            ->once()
            ->with($client, 'name', 'João from conversation')
            ->andReturn(true);

        $result = $this->updateClientData->perform($config, $interaction, $conversation);

        $this->assertEquals('João from conversation', $result['new_value']);
        $this->assertEquals('collected_name', $result['source']);
    }

    public function test_perform_with_unknown_field_fails()
    {
        $config = [
            'field' => 'unknown_field',
            'source' => 'last_interaction_input'
        ];

        $client = new Client(
            1,
            $this->organization->id,
            'Test Client',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(
            1, $this->organization->id, null, 1, 1, 1, 1,
            'test value', null, null, null, now(), null, null, 'text'
        );

        $this->mockClientRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($client);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to update client field: unknown_field');

        $this->updateClientData->perform($config, $interaction, $conversation);
    }
}
