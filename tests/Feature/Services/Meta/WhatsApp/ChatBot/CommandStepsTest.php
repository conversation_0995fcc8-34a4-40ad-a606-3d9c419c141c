<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\UpdateClientData;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\CreateLead;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\SendNotification;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Flow;
use App\Domains\Inventory\Client;
use App\Enums\ChatBot\CommandType;
use App\Models\Organization;
use App\Models\User;

class CommandStepsTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $organization;
    protected User $user;
    protected Client $client;
    protected Flow $flow;
    protected WhatsAppConversation $conversation;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test organization and user
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);

        // Create test client
        $this->client = new Client(
            null,
            $this->organization->id,
            'Test Client',
            '+5511999999999',
            '<EMAIL>',
            'Developer',
            '1990-01-01',
            '12345678901',
            null,
            'Software Development',
            'Rua Teste, 123',
            '123',
            'Centro',
            '01234-567',
            'Apto 1',
            'Solteiro',
            'Cliente de teste'
        );

        // Create test flow
        $this->flow = new Flow(
            1,
            $this->organization->id,
            'Test Flow',
            'Test flow for command steps',
            3,
            null,
            true
        );

        // Create test conversation
        $this->conversation = new WhatsAppConversation(
            1,
            $this->organization->id,
            $this->client->id,
            $this->flow->id,
            1, // phone_number_id
            1, // current_step_id
            null, // json
            false // is_finished
        );
    }

    public function test_execute_command_with_update_client_type()
    {
        // Create command step configuration
        $stepConfig = [
            'command_type' => CommandType::UPDATE_CLIENT->value,
            'command_config' => [
                'field' => 'name',
                'source' => 'last_interaction_input',
                'validation' => 'required|string|min:2'
            ],
            'success_message' => 'Nome atualizado com sucesso!',
            'error_message' => 'Erro ao atualizar nome.'
        ];

        $step = new Step(
            1,
            $this->organization->id,
            $this->flow->id,
            'update_client_name',
            'command',
            1,
            2, // next_step
            null, // earlier_step
            false, // is_initial_step
            false, // is_ending_step
            false, // is_message
            false, // is_interactive
            true,  // is_command
            false, // is_input
            json_encode($stepConfig)
        );

        $interaction = new WhatsAppInteraction(
            1,
            $this->organization->id,
            null, // user_id
            $this->client->id,
            $this->flow->id,
            $step->id,
            $this->conversation->id,
            'João Silva', // message
            null, // answer
            null, // result
            null, // json
            now(), // created_at
            null, // updated_at
            null, // whatsapp_message_id
            'text', // whatsapp_message_type
            ['text' => ['body' => 'João Silva']] // whatsapp_raw_data
        );

        $executeCommand = app(ExecuteCommand::class);
        $result = $executeCommand->perform($step, $interaction, $this->conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::UPDATE_CLIENT->value, $result['command_type']);
        $this->assertEquals('Nome atualizado com sucesso!', $result['message']);
        $this->assertArrayHasKey('result', $result);
    }

    public function test_execute_command_with_create_lead_type()
    {
        $stepConfig = [
            'command_type' => CommandType::CREATE_LEAD->value,
            'command_config' => [
                'status' => 'new',
                'priority' => 'high',
                'description' => 'Lead criado via ChatBot',
                'field_mappings' => [
                    'service_type' => 'service_interest',
                    'budget' => 'budget_range'
                ]
            ],
            'success_message' => 'Lead criado com sucesso!',
            'error_message' => 'Erro ao criar lead.'
        ];

        $step = new Step(
            2,
            $this->organization->id,
            $this->flow->id,
            'create_lead',
            'command',
            2,
            3, // next_step
            1, // earlier_step
            false, // is_initial_step
            false, // is_ending_step
            false, // is_message
            false, // is_interactive
            true,  // is_command
            false, // is_input
            json_encode($stepConfig)
        );

        // Set conversation data
        $conversationData = [
            'service_interest' => 'Website Development',
            'budget_range' => '5000-10000'
        ];
        $this->conversation->json = json_encode($conversationData);

        $interaction = new WhatsAppInteraction(
            2,
            $this->organization->id,
            null, // user_id
            $this->client->id,
            $this->flow->id,
            $step->id,
            $this->conversation->id,
            'Quero contratar um site', // message
            null, // answer
            null, // result
            null, // json
            now(), // created_at
            null, // updated_at
            null, // whatsapp_message_id
            'text', // whatsapp_message_type
            ['text' => ['body' => 'Quero contratar um site']] // whatsapp_raw_data
        );

        $executeCommand = app(ExecuteCommand::class);
        $result = $executeCommand->perform($step, $interaction, $this->conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::CREATE_LEAD->value, $result['command_type']);
        $this->assertEquals('Lead criado com sucesso!', $result['message']);
        $this->assertArrayHasKey('result', $result);
        $this->assertArrayHasKey('lead_id', $result['result']);
    }

    public function test_execute_command_with_send_notification_type()
    {
        $stepConfig = [
            'command_type' => CommandType::SEND_NOTIFICATION->value,
            'command_config' => [
                'type' => 'internal',
                'message' => 'Cliente {{client.name}} completou o fluxo de interesse',
                'level' => 'info'
            ],
            'success_message' => 'Notificação enviada!',
            'error_message' => 'Erro ao enviar notificação.'
        ];

        $step = new Step(
            3,
            $this->organization->id,
            $this->flow->id,
            'send_notification',
            'command',
            3,
            null, // next_step (ending step)
            2, // earlier_step
            false, // is_initial_step
            true,  // is_ending_step
            false, // is_message
            false, // is_interactive
            true,  // is_command
            false, // is_input
            json_encode($stepConfig)
        );

        $interaction = new WhatsAppInteraction(
            3,
            $this->organization->id,
            $this->client->id,
            $this->flow->id,
            $step->id,
            $this->conversation->id,
            'Obrigado!', // input_text
            'text', // whatsapp_message_type
            null, // whatsapp_message_id
            null, // json
            null, // result
            now()
        );

        $executeCommand = app(ExecuteCommand::class);
        $result = $executeCommand->perform($step, $interaction, $this->conversation);

        $this->assertTrue($result['success']);
        $this->assertEquals(CommandType::SEND_NOTIFICATION->value, $result['command_type']);
        $this->assertEquals('Notificação enviada!', $result['message']);
        $this->assertArrayHasKey('result', $result);
    }

    public function test_execute_command_with_invalid_configuration()
    {
        $step = new Step(
            4,
            $this->organization->id,
            $this->flow->id,
            'invalid_command',
            'command',
            4,
            null,
            null,
            false, false, false, false, true, false,
            '{"invalid": "config"}' // Missing command_type
        );

        $interaction = new WhatsAppInteraction(
            4,
            $this->organization->id,
            $this->client->id,
            $this->flow->id,
            $step->id,
            $this->conversation->id,
            'test input',
            'text',
            null, null, null,
            now()
        );

        $executeCommand = app(ExecuteCommand::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid command configuration');

        $executeCommand->perform($step, $interaction, $this->conversation);
    }

    public function test_execute_command_with_unknown_command_type()
    {
        $stepConfig = [
            'command_type' => 'unknown_command',
            'command_config' => [],
            'success_message' => 'Success',
            'error_message' => 'Error'
        ];

        $step = new Step(
            5,
            $this->organization->id,
            $this->flow->id,
            'unknown_command',
            'command',
            5,
            null,
            null,
            false, false, false, false, true, false,
            json_encode($stepConfig)
        );

        $interaction = new WhatsAppInteraction(
            5,
            $this->organization->id,
            $this->client->id,
            $this->flow->id,
            $step->id,
            $this->conversation->id,
            'test input',
            'text',
            null, null, null,
            now()
        );

        $executeCommand = app(ExecuteCommand::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unknown command type');

        $executeCommand->perform($step, $interaction, $this->conversation);
    }
}
