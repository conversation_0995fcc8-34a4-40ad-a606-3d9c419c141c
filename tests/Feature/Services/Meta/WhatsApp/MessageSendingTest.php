<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\MessageService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ChatBotMessageService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Models\Organization;
use App\Models\User;
use App\Models\PhoneNumber as PhoneNumberModel;
use App\Models\Client as ClientModel;
use App\Models\Message as MessageModel;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;

class MessageSendingTest extends TestCase
{
    use RefreshDatabase;

    protected MessageService $messageService;
    protected ChatBotMessageService $chatBotMessageService;
    protected Organization $organization;
    protected User $user;
    protected PhoneNumber $phoneNumber;
    protected Client $client;
    protected WhatsAppConversation $conversation;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        
        $phoneNumberModel = PhoneNumberModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'whatsapp_phone_number_id' => 'test_phone_123',
            'whatsapp_access_token' => 'test_token_123'
        ]);

        $this->phoneNumber = new PhoneNumber(
            id: $phoneNumberModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            phone_number: $phoneNumberModel->phone_number,
            whatsapp_phone_number_id: $phoneNumberModel->whatsapp_phone_number_id,
            whatsapp_access_token: $phoneNumberModel->whatsapp_access_token
        );

        // Create client
        $clientModel = ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'name' => 'John Doe',
            'phone' => '+1234567890',
            'email' => '<EMAIL>'
        ]);

        $this->client = new Client(
            id: $clientModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: $clientModel->name,
            phone: $clientModel->phone,
            email: $clientModel->email
        );

        // Create conversation
        $this->conversation = new WhatsAppConversation(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            flow_id: 1,
            phone_number_id: $this->phoneNumber->id,
            current_step_id: 1,
            client: $this->client
        );

        // Create services
        $this->messageService = new MessageService($this->phoneNumber);
        $this->chatBotMessageService = new ChatBotMessageService($this->phoneNumber);
    }

    public function test_sends_text_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: '+1234567890',
            message: 'Hello, this is a test message!',
            type: 'text'
        );

        $mockResponse = new Response(200, [], json_encode([
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+1234567890',
                    'wa_id' => '1234567890'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.test123'
                ]
            ]
        ]));

        // Mock the HTTP client
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) &&
                           $options['json']['type'] === 'text' &&
                           $options['json']['text']['body'] === 'Hello, this is a test message!' &&
                           $options['json']['to'] === '+1234567890';
                })
            )
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act
        $response = $this->messageService->sendMessage($message);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals('whatsapp', $responseData['messaging_product']);
        $this->assertEquals('wamid.test123', $responseData['messages'][0]['id']);
    }

    public function test_sends_interactive_button_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: '+1234567890',
            message: 'Please choose an option:',
            type: 'interactive',
            json: json_encode([
                'buttons' => [
                    [
                        'id' => 'btn_1',
                        'title' => 'Option 1'
                    ],
                    [
                        'id' => 'btn_2',
                        'title' => 'Option 2'
                    ]
                ]
            ])
        );

        $mockResponse = new Response(200, [], json_encode([
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+1234567890',
                    'wa_id' => '1234567890'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.interactive123'
                ]
            ]
        ]));

        // Mock the HTTP client
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) &&
                           $options['json']['type'] === 'interactive' &&
                           isset($options['json']['interactive']['action']['buttons']) &&
                           count($options['json']['interactive']['action']['buttons']) === 2;
                })
            )
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act
        $response = $this->messageService->sendMessage($message);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals('wamid.interactive123', $responseData['messages'][0]['id']);
    }

    public function test_chatbot_message_service_substitutes_variables()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: '+1234567890',
            message: 'Hello {{client.name}}, your email is {{client.email}}',
            type: 'text'
        );

        $mockResponse = new Response(200, [], json_encode([
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.substitution123']]
        ]));

        // Mock the HTTP client
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) &&
                           $options['json']['text']['body'] === 'Hello John Doe, your <NAME_EMAIL>';
                })
            )
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->chatBotMessageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->chatBotMessageService, $mockClient);

        // Act
        $response = $this->chatBotMessageService->sendMessage($message, $this->conversation);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
    }

    public function test_handles_whatsapp_api_error_gracefully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: '+1234567890',
            message: 'Test message',
            type: 'text'
        );

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new GuzzleException('WhatsApp API Error: Invalid phone number'));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->expectExceptionMessage('WhatsApp API Error: Invalid phone number');
        
        $this->messageService->sendMessage($message);
    }

    public function test_sends_template_message_successfully()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: '+1234567890',
            message: 'Template message',
            type: 'template',
            json: json_encode([
                'template' => [
                    'name' => 'hello_world',
                    'language' => [
                        'code' => 'en_US'
                    ],
                    'components' => [
                        [
                            'type' => 'header',
                            'parameters' => [
                                [
                                    'type' => 'text',
                                    'text' => 'John Doe'
                                ]
                            ]
                        ]
                    ]
                ]
            ])
        );

        $mockResponse = new Response(200, [], json_encode([
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.template123']]
        ]));

        // Mock the HTTP client
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('messages'),
                $this->callback(function ($options) {
                    return isset($options['json']) &&
                           $options['json']['type'] === 'template' &&
                           $options['json']['template']['name'] === 'hello_world';
                })
            )
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act
        $response = $this->messageService->sendMessage($message);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals('wamid.template123', $responseData['messages'][0]['id']);
    }

    public function test_message_persistence_workflow()
    {
        // Arrange
        $messageModel = MessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'to' => '+1234567890',
            'message' => 'Persistent test message',
            'type' => 'text',
            'status' => 'pending'
        ]);

        $message = new Message(
            id: $messageModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: $this->client->id,
            phone_number_id: $this->phoneNumber->id,
            to: $messageModel->to,
            message: $messageModel->message,
            type: $messageModel->type,
            status: $messageModel->status
        );

        $mockResponse = new Response(200, [], json_encode([
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.persistent123']]
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act
        $response = $this->messageService->sendMessage($message);

        // Assert
        $this->assertInstanceOf(Response::class, $response);
        
        // Verify message exists in database
        $this->assertDatabaseHas('messages', [
            'id' => $messageModel->id,
            'to' => '+1234567890',
            'message' => 'Persistent test message',
            'type' => 'text'
        ]);
    }

    public function test_uses_correct_phone_number_configuration()
    {
        // Arrange
        $customPhoneNumberModel = PhoneNumberModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'whatsapp_phone_number_id' => 'custom_phone_456',
            'whatsapp_access_token' => 'custom_token_456'
        ]);

        $customPhoneNumber = new PhoneNumber(
            id: $customPhoneNumberModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            phone_number: $customPhoneNumberModel->phone_number,
            whatsapp_phone_number_id: $customPhoneNumberModel->whatsapp_phone_number_id,
            whatsapp_access_token: $customPhoneNumberModel->whatsapp_access_token
        );

        $customService = new MessageService($customPhoneNumber);

        // Use reflection to check internal properties
        $reflection = new \ReflectionClass($customService);
        $phoneNumberIdProperty = $reflection->getProperty('phoneNumberId');
        $phoneNumberIdProperty->setAccessible(true);
        $tokenProperty = $reflection->getProperty('token');
        $tokenProperty->setAccessible(true);

        // Assert
        $this->assertEquals('custom_phone_456', $phoneNumberIdProperty->getValue($customService));
        $this->assertEquals('custom_token_456', $tokenProperty->getValue($customService));
    }
}
