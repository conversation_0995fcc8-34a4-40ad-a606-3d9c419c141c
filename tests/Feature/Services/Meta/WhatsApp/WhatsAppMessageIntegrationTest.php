<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage\SaveWhatsAppMessage;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Factories\ChatBot\MessageFactory;
use App\Models\Message;
use App\Models\User;
use App\Models\Organization;

class WhatsAppMessageIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_save_whatsapp_message_integration()
    {
        // Arrange - Create required data
        $message = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);
        $messageId = $message->id;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+5579991514957',
                    'wa_id' => '557991514957'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==',
                    'message_status' => 'accepted'
                ]
            ]
        ];

        // Create the use case with real dependencies
        $messageFactory = app(MessageFactory::class);
        $whatsAppMessageFactory = new WhatsAppMessageFactory($messageFactory);
        $whatsAppMessageRepository = new WhatsAppMessageRepository($whatsAppMessageFactory);
        $saveWhatsAppMessage = new SaveWhatsAppMessage($whatsAppMessageRepository, $whatsAppMessageFactory);

        // Act
        $result = $saveWhatsAppMessage->perform($messageId, $whatsappResponse);

        // Assert
        $this->assertNotNull($result);
        $this->assertNotNull($result->id);
        $this->assertEquals($messageId, $result->message_id);
        $this->assertEquals('wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==', $result->whatsapp_message_id);
        $this->assertEquals('accepted', $result->message_status);
        $this->assertEquals('557991514957', $result->wa_id);
        $this->assertEquals('+5579991514957', $result->input_phone);
        $this->assertEquals('whatsapp', $result->messaging_product);
        $this->assertEquals(json_encode($whatsappResponse), $result->json);

        // Verify it was saved to database
        $this->assertDatabaseHas('whatsapp_messages', [
            'message_id' => $messageId,
            'whatsapp_message_id' => 'wamid.HBgMNTU3OTkxNTE0OTU3FQIAERgSREQ3MTc0MDA2MTIzNkJCNTM1AA==',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp'
        ]);
    }

    public function test_save_whatsapp_message_with_minimal_data()
    {
        // Arrange - Create required data
        $message = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);
        $messageId = $message->id;
        $whatsappResponse = [
            'messaging_product' => 'whatsapp'
        ];

        // Create the use case with real dependencies
        $messageFactory = app(MessageFactory::class);
        $whatsAppMessageFactory = new WhatsAppMessageFactory($messageFactory);
        $whatsAppMessageRepository = new WhatsAppMessageRepository($whatsAppMessageFactory);
        $saveWhatsAppMessage = new SaveWhatsAppMessage($whatsAppMessageRepository, $whatsAppMessageFactory);

        // Act
        $result = $saveWhatsAppMessage->perform($messageId, $whatsappResponse);

        // Assert
        $this->assertNotNull($result);
        $this->assertNotNull($result->id);
        $this->assertEquals($messageId, $result->message_id);
        $this->assertNull($result->whatsapp_message_id);
        $this->assertNull($result->message_status);
        $this->assertNull($result->wa_id);
        $this->assertNull($result->input_phone);
        $this->assertEquals('whatsapp', $result->messaging_product);
        $this->assertEquals(json_encode($whatsappResponse), $result->json);

        // Verify it was saved to database
        $this->assertDatabaseHas('whatsapp_messages', [
            'message_id' => $messageId,
            'messaging_product' => 'whatsapp'
        ]);
    }
}
