<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Message;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry\ProcessWebhookEntry;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppWebhookEntryRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppWebhookEntryFactory;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Factories\ChatBot\MessageFactory;

class WhatsAppWebhookIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_process_webhook_entry_integration()
    {
        // Arrange - Create required data
        $message = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage = WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.HBgMNTU5...',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.HBgMNTU5...',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999',
                                        'conversation' => [
                                            'id' => 'conversation_id',
                                            'origin' => ['type' => 'user_initiated']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Create the use case with real dependencies
        $messageFactory = app(MessageFactory::class);
        $whatsAppMessageFactory = new WhatsAppMessageFactory($messageFactory);
        $whatsAppMessageRepository = new WhatsAppMessageRepository($whatsAppMessageFactory);
        $whatsAppWebhookEntryFactory = new WhatsAppWebhookEntryFactory($whatsAppMessageFactory);
        $whatsAppWebhookEntryRepository = new WhatsAppWebhookEntryRepository($whatsAppWebhookEntryFactory);
        
        $processWebhookEntry = new ProcessWebhookEntry(
            $whatsAppWebhookEntryRepository,
            $whatsAppWebhookEntryFactory,
            $whatsAppMessageRepository
        );

        // Act
        $result = $processWebhookEntry->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['processed_entries']);
        $this->assertCount(1, $result['entries']);

        $processedEntry = $result['entries'][0];
        $this->assertEquals($whatsappMessage->id, $processedEntry->whatsapp_message_id);
        $this->assertEquals('wamid.HBgMNTU5...', $processedEntry->external_wam_id);
        $this->assertEquals('delivered', $processedEntry->status);
        $this->assertEquals('1659463500', $processedEntry->timestamp);
        $this->assertEquals('559999999999', $processedEntry->recipient_id);
        $this->assertEquals('conversation_id', $processedEntry->conversation_id);
        $this->assertEquals('user_initiated', $processedEntry->conversation_origin_type);

        // Verify it was saved to database
        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage->id,
            'external_wam_id' => 'wamid.HBgMNTU5...',
            'status' => 'delivered',
            'timestamp' => '1659463500',
            'recipient_id' => '559999999999',
            'conversation_id' => 'conversation_id',
            'conversation_origin_type' => 'user_initiated'
        ]);
    }

    public function test_process_webhook_entry_with_nonexistent_message()
    {
        // Arrange
        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.NONEXISTENT',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Create the use case with real dependencies
        $messageFactory = app(MessageFactory::class);
        $whatsAppMessageFactory = new WhatsAppMessageFactory($messageFactory);
        $whatsAppMessageRepository = new WhatsAppMessageRepository($whatsAppMessageFactory);
        $whatsAppWebhookEntryFactory = new WhatsAppWebhookEntryFactory($whatsAppMessageFactory);
        $whatsAppWebhookEntryRepository = new WhatsAppWebhookEntryRepository($whatsAppWebhookEntryFactory);
        
        $processWebhookEntry = new ProcessWebhookEntry(
            $whatsAppWebhookEntryRepository,
            $whatsAppWebhookEntryFactory,
            $whatsAppMessageRepository
        );

        // Act
        $result = $processWebhookEntry->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['processed_entries']);
        $this->assertEmpty($result['entries']);

        // Verify nothing was saved to database
        $this->assertDatabaseMissing('whatsapp_webhook_entries', [
            'external_wam_id' => 'wamid.NONEXISTENT'
        ]);
    }

    public function test_process_webhook_entry_with_multiple_statuses()
    {
        // Arrange - Create required data
        $message1 = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message 1',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $message2 = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message 2',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage1 = WhatsAppMessage::create([
            'message_id' => $message1->id,
            'whatsapp_message_id' => 'wamid.FIRST',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data1"}'
        ]);

        $whatsappMessage2 = WhatsAppMessage::create([
            'message_id' => $message2->id,
            'whatsapp_message_id' => 'wamid.SECOND',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data2"}'
        ]);

        $webhookEntry = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.FIRST',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ],
                                    [
                                        'id' => 'wamid.SECOND',
                                        'status' => 'read',
                                        'timestamp' => '1659463600',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Create the use case with real dependencies
        $messageFactory = app(MessageFactory::class);
        $whatsAppMessageFactory = new WhatsAppMessageFactory($messageFactory);
        $whatsAppMessageRepository = new WhatsAppMessageRepository($whatsAppMessageFactory);
        $whatsAppWebhookEntryFactory = new WhatsAppWebhookEntryFactory($whatsAppMessageFactory);
        $whatsAppWebhookEntryRepository = new WhatsAppWebhookEntryRepository($whatsAppWebhookEntryFactory);
        
        $processWebhookEntry = new ProcessWebhookEntry(
            $whatsAppWebhookEntryRepository,
            $whatsAppWebhookEntryFactory,
            $whatsAppMessageRepository
        );

        // Act
        $result = $processWebhookEntry->perform($webhookEntry);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['processed_entries']);
        $this->assertCount(2, $result['entries']);

        // Verify both entries were saved to database
        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage1->id,
            'external_wam_id' => 'wamid.FIRST',
            'status' => 'delivered'
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage2->id,
            'external_wam_id' => 'wamid.SECOND',
            'status' => 'read'
        ]);
    }
}
