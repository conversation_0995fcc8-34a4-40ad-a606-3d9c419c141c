<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\TemplateService;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate as WhatsAppTemplateModel;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\PhoneNumber;
use App\Models\Organization;
use App\Models\User;
use App\Models\Template as TemplateModel;
use App\Models\PhoneNumber as PhoneNumberModel;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;

class TemplateManagementTest extends TestCase
{
    use RefreshDatabase;

    protected TemplateService $templateService;
    protected Organization $organization;
    protected User $user;
    protected PhoneNumber $phoneNumber;
    protected Template $template;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        
        $phoneNumberModel = PhoneNumberModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'whatsapp_phone_number_id' => 'test_phone_123',
            'whatsapp_access_token' => 'test_token_123'
        ]);

        $this->phoneNumber = new PhoneNumber(
            id: $phoneNumberModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            phone_number: $phoneNumberModel->phone_number,
            whatsapp_phone_number_id: $phoneNumberModel->whatsapp_phone_number_id,
            whatsapp_access_token: $phoneNumberModel->whatsapp_access_token
        );

        // Create template
        $templateModel = TemplateModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'name' => 'Test Template',
            'description' => 'Test Description',
            'language' => 'en',
            'category' => 'MARKETING',
            'json' => json_encode([
                [
                    'type' => 'HEADER',
                    'format' => 'TEXT',
                    'text' => 'Hello {{1}}'
                ],
                [
                    'type' => 'BODY',
                    'text' => 'This is a test message for {{1}}'
                ]
            ])
        ]);

        $this->template = new Template(
            id: $templateModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: $templateModel->name,
            description: $templateModel->description,
            language: $templateModel->language,
            category: $templateModel->category,
            json: $templateModel->json
        );

        // Create service
        $this->templateService = new TemplateService($this->phoneNumber);
    }

    public function test_registers_template_with_whatsapp_successfully()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'id' => 'whatsapp_template_123',
            'status' => 'PENDING',
            'category' => 'MARKETING',
            'language' => 'en',
            'name' => 'test_template'
        ]));

        // Mock the HTTP client
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->with(
                $this->stringContains('message_templates'),
                $this->callback(function ($options) {
                    return isset($options['json']) &&
                           isset($options['headers']['Authorization']) &&
                           $options['json']['name'] === 'test_template';
                })
            )
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->templateService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->templateService, $mockClient);

        // Act
        $result = $this->templateService->register($this->template);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($this->template->id, $result->template_id);
        $this->assertEquals('PENDING', $result->status);
        $this->assertEquals('whatsapp_template_123', $result->external_id);
        $this->assertNotNull($result->json);

        // Verify it was saved to database
        $this->assertDatabaseHas('whatsapp_templates', [
            'template_id' => $this->template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_template_123'
        ]);
    }

    public function test_throws_exception_for_invalid_template()
    {
        // Arrange
        $invalidTemplate = new Template(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: '', // Invalid: empty name
            description: 'Test Description',
            language: 'en',
            category: 'MARKETING',
            json: json_encode([])
        );

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Template is not valid for WhatsApp');
        
        $this->templateService->register($invalidTemplate);
    }

    public function test_handles_whatsapp_api_error_gracefully()
    {
        // Arrange
        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new GuzzleException('WhatsApp API Error'));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->templateService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->templateService, $mockClient);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->expectExceptionMessage('WhatsApp API Error');
        
        $this->templateService->register($this->template);
    }

    public function test_fetches_existing_whatsapp_template_by_template_id()
    {
        // Arrange
        $whatsappTemplateModel = WhatsAppTemplateModel::create([
            'template_id' => $this->template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_123',
            'json' => json_encode(['test' => 'data'])
        ]);

        $repository = new WhatsAppTemplateRepository(new WhatsAppTemplateFactory());

        // Act
        $result = $repository->fetchByTemplateId($this->template->id);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals($this->template->id, $result->template_id);
        $this->assertEquals('APPROVED', $result->status);
        $this->assertEquals('whatsapp_123', $result->external_id);
    }

    public function test_returns_null_when_whatsapp_template_not_found()
    {
        // Arrange
        $repository = new WhatsAppTemplateRepository(new WhatsAppTemplateFactory());

        // Act
        $result = $repository->fetchByTemplateId(999);

        // Assert
        $this->assertNull($result);
    }

    public function test_updates_existing_whatsapp_template()
    {
        // Arrange
        $whatsappTemplateModel = WhatsAppTemplateModel::create([
            'template_id' => $this->template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_123',
            'json' => json_encode(['test' => 'data'])
        ]);

        $repository = new WhatsAppTemplateRepository(new WhatsAppTemplateFactory());
        $whatsappTemplate = $repository->fetchById($whatsappTemplateModel->id);
        
        // Update the template
        $whatsappTemplate->status = 'APPROVED';
        $whatsappTemplate->json = json_encode(['updated' => 'data']);

        // Act
        $result = $repository->update($whatsappTemplate);

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $result);
        $this->assertEquals('APPROVED', $result->status);
        
        // Verify database was updated
        $this->assertDatabaseHas('whatsapp_templates', [
            'id' => $whatsappTemplateModel->id,
            'status' => 'APPROVED',
            'json' => json_encode(['updated' => 'data'])
        ]);
    }

    public function test_deletes_whatsapp_template()
    {
        // Arrange
        $whatsappTemplateModel = WhatsAppTemplateModel::create([
            'template_id' => $this->template->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_123',
            'json' => json_encode(['test' => 'data'])
        ]);

        $repository = new WhatsAppTemplateRepository(new WhatsAppTemplateFactory());
        $whatsappTemplate = $repository->fetchById($whatsappTemplateModel->id);

        // Act
        $result = $repository->delete($whatsappTemplate);

        // Assert
        $this->assertTrue($result);
        
        // Verify soft delete
        $this->assertSoftDeleted('whatsapp_templates', [
            'id' => $whatsappTemplateModel->id
        ]);
    }

    public function test_template_factory_builds_from_template_correctly()
    {
        // Arrange
        $factory = new WhatsAppTemplateFactory();

        // Act
        $whatsappTemplate = $factory->buildFromTemplate(
            $this->template,
            'APPROVED',
            'whatsapp_external_123',
            json_encode(['api_response' => 'data'])
        );

        // Assert
        $this->assertInstanceOf(WhatsAppTemplate::class, $whatsappTemplate);
        $this->assertEquals($this->template->id, $whatsappTemplate->template_id);
        $this->assertEquals('APPROVED', $whatsappTemplate->status);
        $this->assertEquals('whatsapp_external_123', $whatsappTemplate->external_id);
        $this->assertEquals(json_encode(['api_response' => 'data']), $whatsappTemplate->json);
        $this->assertEquals($this->template, $whatsappTemplate->template);
    }

    public function test_complete_template_registration_workflow()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'id' => 'whatsapp_workflow_123',
            'status' => 'PENDING',
            'category' => 'MARKETING',
            'language' => 'en',
            'name' => 'test_template'
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willReturn($mockResponse);

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->templateService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->templateService, $mockClient);

        // Act - Register template
        $whatsappTemplate = $this->templateService->register($this->template);

        // Assert - Verify complete workflow
        $this->assertInstanceOf(WhatsAppTemplate::class, $whatsappTemplate);
        $this->assertNotNull($whatsappTemplate->id);
        $this->assertEquals($this->template->id, $whatsappTemplate->template_id);
        $this->assertEquals('PENDING', $whatsappTemplate->status);
        $this->assertEquals('whatsapp_workflow_123', $whatsappTemplate->external_id);

        // Verify database persistence
        $this->assertDatabaseHas('whatsapp_templates', [
            'template_id' => $this->template->id,
            'status' => 'PENDING',
            'external_id' => 'whatsapp_workflow_123'
        ]);

        // Verify we can fetch it back
        $repository = new WhatsAppTemplateRepository(new WhatsAppTemplateFactory());
        $fetchedTemplate = $repository->fetchByTemplateId($this->template->id);
        
        $this->assertNotNull($fetchedTemplate);
        $this->assertEquals($whatsappTemplate->id, $fetchedTemplate->id);
        $this->assertEquals('PENDING', $fetchedTemplate->status);
    }
}
