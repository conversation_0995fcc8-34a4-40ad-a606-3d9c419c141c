<?php

namespace Tests\Feature\Services\ASAAS\Architecture;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Organization;
use App\Models\Client;
use App\Models\Sale;
use App\Models\Subscription;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasSale;

class RelationshipsTest extends TestCase
{
    use RefreshDatabase;

    public function test_organization_has_asaas_relationship()
    {
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganization::factory()->create([
            'organization_id' => $organization->id
        ]);

        // Test hasOne relationship
        $this->assertInstanceOf(AsaasOrganization::class, $organization->asaas);
        $this->assertEquals($asaasOrganization->id, $organization->asaas->id);

        // Test belongsTo relationship
        $this->assertInstanceOf(Organization::class, $asaasOrganization->organization);
        $this->assertEquals($organization->id, $asaasOrganization->organization->id);
    }

    public function test_organization_asaas_relationship_is_optional()
    {
        $organization = Organization::factory()->create();

        // Organization without ASAAS integration should return null
        $this->assertNull($organization->asaas);
        $this->assertFalse($organization->hasAsaasIntegration());
    }

    public function test_client_has_asaas_relationship()
    {
        $client = Client::factory()->create();
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $client->organization_id,
        ]);

        // Test hasOne relationship
        $this->assertInstanceOf(AsaasClient::class, $client->asaas);
        $this->assertEquals($asaasClient->id, $client->asaas->id);

        // Test belongsTo relationship
        $this->assertInstanceOf(Client::class, $asaasClient->client);
        $this->assertEquals($client->id, $asaasClient->client->id);
    }

    public function test_client_asaas_relationship_is_optional()
    {
        $client = Client::factory()->create();

        // Client without ASAAS integration should return null
        $this->assertNull($client->asaas);
        $this->assertFalse($client->hasAsaasIntegration());
    }

    public function test_sale_has_asaas_relationship()
    {
        $sale = Sale::factory()->create();
        $asaasSale = AsaasSale::factory()->create([
            'sale_id' => $sale->id,
            'organization_id' => $sale->organization_id,
            'client_id' => $sale->client_id,
        ]);

        // Test hasOne relationship
        $this->assertInstanceOf(AsaasSale::class, $sale->asaas);
        $this->assertEquals($asaasSale->id, $sale->asaas->id);

        // Test belongsTo relationship
        $this->assertInstanceOf(Sale::class, $asaasSale->sale);
        $this->assertEquals($sale->id, $asaasSale->sale->id);
    }

    public function test_sale_asaas_relationship_is_optional()
    {
        $sale = Sale::factory()->create();

        // Sale without ASAAS payment should return null
        $this->assertNull($sale->asaas);
        $this->assertFalse($sale->hasAsaasPayment());
    }

    public function test_asaas_client_belongs_to_organization()
    {
        $organization = Organization::factory()->create();
        $client = Client::factory()->create(['organization_id' => $organization->id]);
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organization->id,
        ]);

        $this->assertInstanceOf(Organization::class, $asaasClient->organization);
        $this->assertEquals($organization->id, $asaasClient->organization->id);
    }

    public function test_asaas_sale_belongs_to_organization_and_client()
    {
        $organization = Organization::factory()->create();
        $client = Client::factory()->create(['organization_id' => $organization->id]);
        $sale = Sale::factory()->create([
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);
        $asaasSale = AsaasSale::factory()->create([
            'sale_id' => $sale->id,
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);

        $this->assertInstanceOf(Organization::class, $asaasSale->organization);
        $this->assertEquals($organization->id, $asaasSale->organization->id);

        $this->assertInstanceOf(Client::class, $asaasSale->client);
        $this->assertEquals($client->id, $asaasSale->client->id);
    }

    public function test_organization_convenience_methods_work()
    {
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganization::factory()->active()->create([
            'organization_id' => $organization->id,
            'asaas_account_id' => 'acc_123',
            'asaas_api_key' => 'key_456',
        ]);

        // Create an actual subscription for the organization
        $subscription = Subscription::factory()->active()->create([
            'organization_id' => $organization->id,
            'status' => 'ACTIVE',
        ]);

        $this->assertTrue($organization->hasAsaasIntegration());
        $this->assertTrue($organization->canAccessSystem());
        $this->assertEquals('acc_123', $organization->getAsaasAccountId());
        $this->assertEquals('key_456', $organization->getAsaasApiKey());
        $this->assertEquals('ACTIVE', $organization->getSubscriptionStatus());
    }

    public function test_client_convenience_methods_work()
    {
        $client = Client::factory()->create();
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $client->organization_id,
            'asaas_customer_id' => 'cus_123',
        ]);

        $this->assertTrue($client->hasAsaasIntegration());
        $this->assertEquals('cus_123', $client->getAsaasCustomerId());
    }

    public function test_sale_convenience_methods_work()
    {
        $sale = Sale::factory()->create();
        $asaasSale = AsaasSale::factory()->paid()->create([
            'sale_id' => $sale->id,
            'organization_id' => $sale->organization_id,
            'client_id' => $sale->client_id,
            'asaas_payment_id' => 'pay_123',
            'status' => 'RECEIVED', // Set status to RECEIVED so it's considered paid
            'payment_date' => now(),
        ]);

        $this->assertTrue($sale->hasAsaasPayment());
        $this->assertEquals('pay_123', $sale->getAsaasPaymentId());
        $this->assertTrue($sale->isAsaasPaid());
        $this->assertFalse($sale->isAsaasPending());
        $this->assertFalse($sale->isAsaasOverdue());
    }

    public function test_eager_loading_works_correctly()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganization::factory()->create([
            'organization_id' => $organization->id
        ]);

        $client = Client::factory()->create(['organization_id' => $organization->id]);
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organization->id,
        ]);

        $sale = Sale::factory()->create([
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);
        $asaasSale = AsaasSale::factory()->create([
            'sale_id' => $sale->id,
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);

        // Test eager loading
        $organizationWithAsaas = Organization::with('asaas')->find($organization->id);
        $clientWithAsaas = Client::with('asaas')->find($client->id);
        $saleWithAsaas = Sale::with('asaas')->find($sale->id);

        $this->assertInstanceOf(AsaasOrganization::class, $organizationWithAsaas->asaas);
        $this->assertInstanceOf(AsaasClient::class, $clientWithAsaas->asaas);
        $this->assertInstanceOf(AsaasSale::class, $saleWithAsaas->asaas);
    }

    public function test_cascade_delete_works()
    {
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganization::factory()->create([
            'organization_id' => $organization->id
        ]);

        $client = Client::factory()->create(['organization_id' => $organization->id]);
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organization->id,
        ]);

        $sale = Sale::factory()->create([
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);
        $asaasSale = AsaasSale::factory()->create([
            'sale_id' => $sale->id,
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);

        // Delete main models (force delete to trigger cascade)
        $organization->forceDelete();
        $client->forceDelete();
        $sale->forceDelete();

        // ASAAS models should be deleted due to cascade
        $this->assertDatabaseMissing('asaas_organizations', ['id' => $asaasOrganization->id]);
        $this->assertDatabaseMissing('asaas_clients', ['id' => $asaasClient->id]);
        $this->assertDatabaseMissing('asaas_sales', ['id' => $asaasSale->id]);
    }

    public function test_unique_constraints_work()
    {
        $organization = Organization::factory()->create();
        $client = Client::factory()->create();
        $sale = Sale::factory()->create();

        // Create first ASAAS records
        AsaasOrganization::factory()->create(['organization_id' => $organization->id]);
        AsaasClient::factory()->create(['client_id' => $client->id]);
        AsaasSale::factory()->create(['sale_id' => $sale->id]);

        // Try to create duplicates - should fail
        $this->expectException(\Illuminate\Database\QueryException::class);
        AsaasOrganization::factory()->create(['organization_id' => $organization->id]);
    }

    public function test_complex_query_with_relationships()
    {
        // Create organization with active ASAAS subscription
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganization::factory()->active()->create([
            'organization_id' => $organization->id
        ]);

        // Create client with ASAAS integration
        $client = Client::factory()->create(['organization_id' => $organization->id]);
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organization->id,
        ]);

        // Create paid sale
        $sale = Sale::factory()->create([
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);
        $asaasSale = AsaasSale::factory()->paid()->create([
            'sale_id' => $sale->id,
            'organization_id' => $organization->id,
            'client_id' => $client->id,
        ]);

        // Query organizations with active ASAAS integration and paid sales
        $result = Organization::whereHas('asaas', function ($query) {
            $query->where('is_active', true);
        })->with(['asaas'])->first();

        $this->assertNotNull($result);
        $this->assertEquals($organization->id, $result->id);
        $this->assertTrue($result->canAccessSystem());
    }
}
