<?php

namespace Tests\Feature\Services\ASAAS\Organizations;

use App\Enums\AsaasEnvironment;
use App\Factories\OrganizationFactory;
use App\Models\Organization;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\DeleteAsaasOrganization;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\FindAsaasOrganization;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\StoreAsaasOrganization;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\UpdateAsaasOrganization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AsaasOrganizationCrudTest extends TestCase
{
    use RefreshDatabase;

    private StoreAsaasOrganization $storeAsaasOrganization;
    private UpdateAsaasOrganization $updateAsaasOrganization;
    private DeleteAsaasOrganization $deleteAsaasOrganization;
    private FindAsaasOrganization $findAsaasOrganization;
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;
    private OrganizationFactory $organizationFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->asaasOrganizationRepository = app(AsaasOrganizationRepository::class);
        $this->asaasOrganizationFactory = app(AsaasOrganizationFactory::class);
        $this->organizationFactory = app(OrganizationFactory::class);

        $this->storeAsaasOrganization = new StoreAsaasOrganization(
            $this->asaasOrganizationRepository,
            $this->asaasOrganizationFactory
        );

        $this->updateAsaasOrganization = new UpdateAsaasOrganization(
            $this->asaasOrganizationRepository,
            $this->asaasOrganizationFactory
        );

        $this->deleteAsaasOrganization = new DeleteAsaasOrganization(
            $this->asaasOrganizationRepository
        );

        $this->findAsaasOrganization = new FindAsaasOrganization(
            $this->asaasOrganizationRepository
        );
    }

    public function test_can_store_asaas_organization_from_data()
    {
        // Create organization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        // Prepare data
        $data = [
            'asaas_account_id' => 'acc_123456789',
            'asaas_api_key' => 'api_key_123456789',
            'asaas_wallet_id' => 'wallet_123456789',
            'asaas_environment' => AsaasEnvironment::SANDBOX->value,
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'login_email' => '<EMAIL>',
            'cpf_cnpj' => '***********',
            'phone' => '***********',
            'mobile_phone' => '***********',
            'address' => 'Test Address',
            'address_number' => '123',
            'postal_code' => '01310-100',
            'company_type' => 'MEI',
            'income_value' => 5000.00,
            'site' => 'https://example.com',
        ];

        // Store AsaasOrganization
        $asaasOrganization = $this->storeAsaasOrganization->perform($data, $organizationDomain);

        // Assertions
        $this->assertNotNull($asaasOrganization->id);
        $this->assertEquals($organization->id, $asaasOrganization->organization_id);
        $this->assertEquals('acc_123456789', $asaasOrganization->asaas_account_id);
        $this->assertEquals('api_key_123456789', $asaasOrganization->asaas_api_key);
        $this->assertEquals('wallet_123456789', $asaasOrganization->asaas_wallet_id);
        $this->assertEquals(AsaasEnvironment::SANDBOX, $asaasOrganization->asaas_environment);
        $this->assertEquals('Test Organization', $asaasOrganization->name);
        $this->assertEquals('<EMAIL>', $asaasOrganization->email);
        $this->assertEquals('<EMAIL>', $asaasOrganization->login_email);
        $this->assertTrue($asaasOrganization->is_active);
    }

    public function test_can_store_asaas_organization_from_asaas_response()
    {
        // Create organization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        // Mock ASAAS response
        $asaasResponse = [
            'object' => 'account',
            'id' => '4f468235-cec3-482f-b3d0-348af4c7194',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'loginEmail' => '<EMAIL>',
            'phone' => null,
            'mobilePhone' => null,
            'address' => 'Rua Fernando Orlandi',
            'addressNumber' => '544',
            'complement' => null,
            'province' => 'Jardim Pedra Branca',
            'postalCode' => '14079-452',
            'cpfCnpj' => '**************',
            'birthDate' => '1995-04-12',
            'personType' => 'JURIDICA',
            'companyType' => 'MEI',
            'city' => 15478,
            'state' => 'SP',
            'country' => 'Brasil',
            'tradingName' => null,
            'site' => 'https://www.example.com',
            'walletId' => 'c0c1688f-636b-42c0-b6ee-7339182276b7',
            'accountNumber' => [
                'agency' => '0001',
                'account' => '3514',
                'accountDigit' => '3'
            ],
            'commercialInfoExpiration' => [
                'isExpired' => false,
                'scheduledDate' => '2025-05-05 00:00:00'
            ],
            'apiKey' => '3d425760b01ea73feab3357de2a50c42fc8bc055861f4631e3be906700210874'
        ];

        // Store AsaasOrganization from ASAAS response
        $asaasOrganization = $this->storeAsaasOrganization->performFromAsaasResponse(
            $asaasResponse,
            $organizationDomain,
            AsaasEnvironment::PRODUCTION
        );

        // Assertions
        $this->assertNotNull($asaasOrganization->id);
        $this->assertEquals($organization->id, $asaasOrganization->organization_id);
        $this->assertEquals('4f468235-cec3-482f-b3d0-348af4c7194', $asaasOrganization->asaas_account_id);
        $this->assertEquals('3d425760b01ea73feab3357de2a50c42fc8bc055861f4631e3be906700210874', $asaasOrganization->asaas_api_key);
        $this->assertEquals('c0c1688f-636b-42c0-b6ee-7339182276b7', $asaasOrganization->asaas_wallet_id);
        $this->assertEquals(AsaasEnvironment::PRODUCTION, $asaasOrganization->asaas_environment);
        $this->assertEquals('John Doe', $asaasOrganization->name);
        $this->assertEquals('<EMAIL>', $asaasOrganization->email);
        $this->assertEquals('<EMAIL>', $asaasOrganization->login_email);
        $this->assertEquals('**************', $asaasOrganization->cpf_cnpj);
        $this->assertEquals('1995-04-12', $asaasOrganization->birth_date);
        $this->assertEquals('JURIDICA', $asaasOrganization->person_type);
        $this->assertEquals('MEI', $asaasOrganization->company_type);
        $this->assertEquals(15478, $asaasOrganization->city);
        $this->assertEquals('SP', $asaasOrganization->state);
        $this->assertEquals('Brasil', $asaasOrganization->country);
        $this->assertEquals('https://www.example.com', $asaasOrganization->site);
        $this->assertEquals(['agency' => '0001', 'account' => '3514', 'accountDigit' => '3'], $asaasOrganization->account_number);
        $this->assertEquals(['isExpired' => false, 'scheduledDate' => '2025-05-05 00:00:00'], $asaasOrganization->commercial_info_expiration);
        $this->assertTrue($asaasOrganization->is_active);
        $this->assertNotNull($asaasOrganization->last_sync_at);
    }

    public function test_can_update_asaas_organization()
    {
        // Create organization and AsaasOrganization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        $data = [
            'asaas_account_id' => 'acc_123456789',
            'asaas_api_key' => 'api_key_123456789',
            'asaas_environment' => AsaasEnvironment::SANDBOX->value,
            'name' => 'Original Name',
            'email' => '<EMAIL>',
        ];

        $asaasOrganization = $this->storeAsaasOrganization->perform($data, $organizationDomain);

        // Update data
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '***********',
            'site' => 'https://updated.com',
        ];

        // Update AsaasOrganization
        $updatedAsaasOrganization = $this->updateAsaasOrganization->perform($asaasOrganization->id, $updateData);

        // Assertions
        $this->assertEquals('Updated Name', $updatedAsaasOrganization->name);
        $this->assertEquals('<EMAIL>', $updatedAsaasOrganization->email);
        $this->assertEquals('***********', $updatedAsaasOrganization->phone);
        $this->assertEquals('https://updated.com', $updatedAsaasOrganization->site);
        $this->assertEquals('acc_123456789', $updatedAsaasOrganization->asaas_account_id); // Should remain unchanged
    }

    public function test_can_find_asaas_organization()
    {
        // Create organization and AsaasOrganization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        $data = [
            'asaas_account_id' => 'acc_123456789',
            'asaas_api_key' => 'api_key_123456789',
            'asaas_environment' => AsaasEnvironment::SANDBOX->value,
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ];

        $storedAsaasOrganization = $this->storeAsaasOrganization->perform($data, $organizationDomain);

        // Find by ID
        $foundById = $this->findAsaasOrganization->findById($storedAsaasOrganization->id);
        $this->assertNotNull($foundById);
        $this->assertEquals($storedAsaasOrganization->id, $foundById->id);

        // Find by organization ID
        $foundByOrgId = $this->findAsaasOrganization->findByOrganizationId($organization->id);
        $this->assertNotNull($foundByOrgId);
        $this->assertEquals($storedAsaasOrganization->id, $foundByOrgId->id);

        // Find by ASAAS account ID
        $foundByAsaasId = $this->findAsaasOrganization->findByAsaasAccountId('acc_123456789');
        $this->assertNotNull($foundByAsaasId);
        $this->assertEquals($storedAsaasOrganization->id, $foundByAsaasId->id);
    }

    public function test_can_delete_asaas_organization()
    {
        // Create organization and AsaasOrganization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        $data = [
            'asaas_account_id' => 'acc_123456789',
            'asaas_api_key' => 'api_key_123456789',
            'asaas_environment' => AsaasEnvironment::SANDBOX->value,
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ];

        $asaasOrganization = $this->storeAsaasOrganization->perform($data, $organizationDomain);

        // Delete AsaasOrganization
        $deleted = $this->deleteAsaasOrganization->perform($asaasOrganization->id);
        $this->assertTrue($deleted);

        // Verify it's deleted (soft delete)
        $foundAfterDelete = $this->findAsaasOrganization->findById($asaasOrganization->id);
        $this->assertNull($foundAfterDelete);
    }

    public function test_cannot_store_duplicate_asaas_organization_for_same_organization()
    {
        // Create organization
        $organization = Organization::factory()->create();
        $organizationDomain = $this->organizationFactory->buildFromModel($organization);

        $data = [
            'asaas_account_id' => 'acc_123456789',
            'asaas_api_key' => 'api_key_123456789',
            'asaas_environment' => AsaasEnvironment::SANDBOX->value,
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ];

        // Store first AsaasOrganization
        $this->storeAsaasOrganization->perform($data, $organizationDomain);

        // Try to store another AsaasOrganization for the same organization
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Organization already has an ASAAS integration');

        $this->storeAsaasOrganization->perform($data, $organizationDomain);
    }
}
