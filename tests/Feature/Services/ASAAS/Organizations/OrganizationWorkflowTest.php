<?php

namespace Tests\Feature\Services\ASAAS\Organizations;

use App\Enums\AsaasEnvironment;
use App\Enums\SubscriptionStatus;
use App\Factories\OrganizationFactory;
use App\Models\Organization;
use App\Models\User;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CheckSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\GetBillingDetails;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrganizationWorkflowTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private OrganizationFactory $organizationFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->organizationFactory = app(OrganizationFactory::class);
    }

    public function test_complete_organization_workflow()
    {
        // 1. Initially organization has no ASAAS integration
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $isAllowedUseCase = app(IsAllowedToUseSystem::class);
        $accessResult = $isAllowedUseCase->perform($organizationDomain);

        $this->assertFalse($accessResult['allowed']);
        $this->assertEquals('no_asaas_integration', $accessResult['reason']);

        // 2. Create ASAAS subaccount (simulated)
        $asaasOrganization = AsaasOrganization::factory()->create([
            'organization_id' => $this->organization->id,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'key_789',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'subscription_status' => SubscriptionStatus::INACTIVE,
            'is_courtesy' => true,
            'courtesy_expires_at' => now()->addDays(30),
        ]);

        // 3. Reload organization with ASAAS data
        $this->organization->load('asaas');
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        // 4. Now organization should have courtesy access
        $accessResult = $isAllowedUseCase->perform($organizationDomain);

        $this->assertTrue($accessResult['allowed']);
        $this->assertEquals('courtesy_access', $accessResult['reason']);
        $this->assertStringContains('courtesy access', $accessResult['message']);

        // 5. Check billing details
        $getBillingUseCase = app(GetBillingDetails::class);
        $billingResult = $getBillingUseCase->perform($organizationDomain);

        $this->assertTrue($billingResult['success']);
        $this->assertTrue($billingResult['access_status']['allowed']);
        $this->assertArrayHasKey('subscription_details', $billingResult);

        // 6. Simulate courtesy expiration
        $asaasOrganization->update([
            'is_courtesy' => false,
            'courtesy_expires_at' => now()->subDays(1),
        ]);

        $this->organization->refresh();
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        // 7. Now organization should not have access
        $accessResult = $isAllowedUseCase->perform($organizationDomain);

        $this->assertFalse($accessResult['allowed']);
        $this->assertEquals('subscription_inactive', $accessResult['reason']);

        // 8. Activate subscription
        $asaasOrganization->update([
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 99.90,
            'subscription_expires_at' => now()->addMonth(),
            'subscription_started_at' => now(),
        ]);

        $this->organization->refresh();
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        // 9. Now organization should have access again
        $accessResult = $isAllowedUseCase->perform($organizationDomain);

        $this->assertTrue($accessResult['allowed']);
        $this->assertEquals('active_subscription', $accessResult['reason']);
        $this->assertStringContains('active subscription', $accessResult['message']);
    }

    public function test_organization_subscription_lifecycle()
    {
        // Create organization with ASAAS integration
        $asaasOrganization = AsaasOrganization::factory()->create([
            'organization_id' => $this->organization->id,
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 99.90,
            'subscription_expires_at' => now()->addMonth(),
        ]);

        $this->organization->load('asaas');
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $isAllowedUseCase = app(IsAllowedToUseSystem::class);

        // Test active subscription
        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertTrue($result['allowed']);
        $this->assertEquals('active_subscription', $result['reason']);

        // Test expired subscription
        $asaasOrganization->update([
            'subscription_expires_at' => now()->subDays(1)
        ]);
        $this->organization->refresh();
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertFalse($result['allowed']);
        $this->assertEquals('subscription_expired', $result['reason']);

        // Test overdue subscription
        $asaasOrganization->update([
            'subscription_status' => SubscriptionStatus::OVERDUE,
            'subscription_expires_at' => now()->addMonth(),
        ]);
        $this->organization->refresh();
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertFalse($result['allowed']);
        $this->assertEquals('subscription_overdue', $result['reason']);
    }

    public function test_organization_states_affect_access()
    {
        // Create organization with ASAAS integration
        AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
        ]);

        $isAllowedUseCase = app(IsAllowedToUseSystem::class);

        // Test inactive organization
        $this->organization->update(['is_active' => false]);
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_inactive', $result['reason']);

        // Test suspended organization
        $this->organization->update([
            'is_active' => true,
            'is_suspended' => true
        ]);
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_suspended', $result['reason']);
    }

    public function test_billing_details_include_complete_information()
    {
        // Create organization with ASAAS integration
        AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
            'subscription_value' => 99.90,
            'monthly_revenue' => 1500.00,
            'current_balance' => 500.00,
        ]);

        $this->organization->load('asaas');
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $getBillingUseCase = app(GetBillingDetails::class);
        $result = $getBillingUseCase->perform($organizationDomain);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('access_status', $result);
        $this->assertArrayHasKey('subscription_details', $result);
        $this->assertArrayHasKey('billing_summary', $result);

        $this->assertEquals(99.90, $result['subscription_details']['value']);
        $this->assertEquals('R$ 99,90', $result['subscription_details']['formatted_value']);
        $this->assertTrue($result['access_status']['allowed']);
    }

    public function test_can_check_subscription_status()
    {
        // Create organization with ASAAS integration
        AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
        ]);

        $this->organization->load('asaas');
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $checkStatusUseCase = app(CheckSubscriptionStatus::class);
        $result = $checkStatusUseCase->perform($organizationDomain);

        // Since we're not mocking ASAAS API, this might fail
        // but we can verify the use case exists and handles the organization
        $this->assertIsArray($result);
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_organization_convenience_methods()
    {
        // Test organization without ASAAS
        $this->assertFalse($this->organization->hasAsaasIntegration());
        $this->assertFalse($this->organization->canAccessSystem());
        $this->assertNull($this->organization->getAsaasAccountId());
        $this->assertNull($this->organization->getAsaasApiKey());

        // Create ASAAS integration
        AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
            'asaas_account_id' => 'acc_123',
            'asaas_api_key' => 'key_456',
        ]);

        $this->organization->refresh();

        $this->assertTrue($this->organization->hasAsaasIntegration());
        $this->assertTrue($this->organization->canAccessSystem());
        $this->assertEquals('acc_123', $this->organization->getAsaasAccountId());
        $this->assertEquals('key_456', $this->organization->getAsaasApiKey());
        $this->assertEquals('active', $this->organization->getSubscriptionStatus());
    }

    public function test_organization_factory_builds_correct_domain()
    {
        AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
        ]);

        $this->organization->load('asaas');
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $this->assertNotNull($organizationDomain);
        $this->assertEquals($this->organization->id, $organizationDomain->id);
        $this->assertEquals($this->organization->name, $organizationDomain->name);
        $this->assertTrue($organizationDomain->hasAsaasIntegration());
        $this->assertTrue($organizationDomain->canAccessSystem());
    }

    public function test_next_action_suggestions()
    {
        $isAllowedUseCase = app(IsAllowedToUseSystem::class);

        // Test inactive organization
        $this->organization->update(['is_active' => false]);
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertEquals('activate_organization', $result['next_action']);

        // Test suspended organization
        $this->organization->update([
            'is_active' => true,
            'is_suspended' => true
        ]);
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertEquals('unsuspend_organization', $result['next_action']);

        // Test no ASAAS integration
        $this->organization->update(['is_suspended' => false]);
        $organizationDomain = $this->organizationFactory->buildFromModel($this->organization);

        $result = $isAllowedUseCase->perform($organizationDomain);
        $this->assertEquals('create_asaas_subaccount', $result['next_action']);
    }
}
