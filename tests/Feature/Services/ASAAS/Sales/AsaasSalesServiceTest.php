<?php

namespace Tests\Feature\Services\ASAAS\Sales;

use App\Models\Client;
use App\Models\Organization;
use App\Models\Sale;
use App\Models\User;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment;
use App\Services\ASAAS\UseCases\Deprecated\Sales\SyncPaymentStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AsaasSalesServiceTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $organization;
    protected Client $client;
    protected Sale $sale;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpf' => '12345678901',
        ]);

        $this->sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00,
        ]);
    }

    public function test_can_instantiate_create_customer_use_case()
    {
        $createCustomer = app(CreateCustomer::class);
        $this->assertInstanceOf(CreateCustomer::class, $createCustomer);
    }

    public function test_can_instantiate_create_payment_use_case()
    {
        $createPayment = app(CreatePayment::class);
        $this->assertInstanceOf(CreatePayment::class, $createPayment);
    }

    public function test_can_instantiate_sync_payment_status_use_case()
    {
        $syncPaymentStatus = app(SyncPaymentStatus::class);
        $this->assertInstanceOf(SyncPaymentStatus::class, $syncPaymentStatus);
    }

    public function test_can_check_if_client_can_create_customer()
    {
        $createCustomer = app(CreateCustomer::class);

        // Client without ASAAS customer and without organization ASAAS integration should not be able to create
        $this->assertFalse($createCustomer->canCreateCustomer($this->client));

        // Client with missing email should not be able to create
        $incompleteClient = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Client',
            'email' => null,
            'cpf' => '12345678901',
        ]);
        $this->assertFalse($createCustomer->canCreateCustomer($incompleteClient));
    }

    public function test_can_check_if_sale_can_create_payment()
    {
        $createPayment = app(CreatePayment::class);

        // Sale without ASAAS payment should be able to create
        $this->assertFalse($createPayment->canCreatePayment($this->sale));

        // Sale with ASAAS payment should not be able to create again
        \App\Services\ASAAS\Models\AsaasSale::factory()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
            'asaas_payment_id' => 'pay_123'
        ]);
        $this->assertFalse($createPayment->canCreatePayment($this->sale));
    }

    public function test_client_has_correct_document_attribute()
    {
        // Test with CPF
        $this->assertEquals('12345678901', $this->client->document);

        // Test with CNPJ
        $clientWithCnpj = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'cpf' => null,
            'cnpj' => '12345678000195',
        ]);
        $this->assertEquals('12345678000195', $clientWithCnpj->document);
    }

    public function test_client_has_correct_mobile_phone_attribute()
    {
        $this->client->update(['phone' => '11999999999']);
        $this->assertEquals('11999999999', $this->client->mobile_phone);
    }

    public function test_client_has_correct_birth_date_attribute()
    {
        $this->client->update(['birthdate' => '1990-01-01']);
        $this->assertEquals('1990-01-01', $this->client->birth_date->format('Y-m-d'));
    }

    public function test_client_has_correct_postal_code_attribute()
    {
        $this->client->update(['cep' => '01310100']);
        $this->assertEquals('01310100', $this->client->postal_code);
    }

    public function test_client_has_correct_address_number_attribute()
    {
        $this->client->update(['number' => '123']);
        $this->assertEquals('123', $this->client->address_number);
    }

    public function test_client_has_correct_province_attribute()
    {
        $this->client->update(['neighborhood' => 'Centro']);
        $this->assertEquals('Centro', $this->client->province);
    }

    public function test_client_asaas_integration_methods()
    {
        // Initially no ASAAS integration
        $this->assertFalse($this->client->hasAsaasIntegration());
        $this->assertFalse($this->client->needsAsaasSync()); // No sync needed if no ASAAS integration

        // After creating ASAAS client
        \App\Services\ASAAS\Models\AsaasClient::factory()->create([
            'client_id' => $this->client->id,
            'organization_id' => $this->client->organization_id,
            'asaas_customer_id' => 'cus_123'
        ]);
        $this->assertTrue($this->client->fresh()->hasAsaasIntegration());

        // After marking as synced
        $this->client->fresh()->markAsSynced();
        $this->assertFalse($this->client->fresh()->needsAsaasSync());
    }

    public function test_sale_value_attribute()
    {
        $this->assertEquals(100.00, $this->sale->value);
    }

    public function test_sale_asaas_payment_methods()
    {
        // Initially no ASAAS payment
        $this->assertFalse($this->sale->hasAsaasPayment());
        $this->assertFalse($this->sale->needsAsaasSync());

        // After creating ASAAS payment
        \App\Services\ASAAS\Models\AsaasSale::factory()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
            'asaas_payment_id' => 'pay_123'
        ]);
        $this->assertTrue($this->sale->fresh()->hasAsaasPayment());
        $this->assertTrue($this->sale->fresh()->needsAsaasSync());

        // After marking as synced
        $this->sale->markAsSynced();
        $this->assertFalse($this->sale->needsAsaasSync());
    }

    public function test_sale_payment_status_methods()
    {
        // Initially no payment status
        $this->assertFalse($this->sale->isPaid());
        $this->assertFalse($this->sale->isPending());
        $this->assertFalse($this->sale->isOverdue());

        // Test with different payment statuses using ASAAS sale
        $asaasSale = \App\Services\ASAAS\Models\AsaasSale::factory()->pending()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
        ]);
        $this->assertTrue($this->sale->fresh()->isPending());

        $asaasSale->update(['payment_status' => \App\Enums\PaymentStatus::RECEIVED]);
        $this->assertTrue($this->sale->fresh()->isPaid());

        $asaasSale->update(['payment_status' => \App\Enums\PaymentStatus::OVERDUE]);
        $this->assertTrue($this->sale->fresh()->isOverdue());
    }

    public function test_sale_formatted_value_attribute()
    {
        $this->assertEquals('R$ 100,00', $this->sale->formatted_value);
    }

    public function test_sale_payment_status_label_attribute()
    {
        $this->assertEquals('Não definido', $this->sale->payment_status_label);

        \App\Services\ASAAS\Models\AsaasSale::factory()->pending()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
        ]);
        $this->assertEquals('Pendente', $this->sale->fresh()->payment_status_label);
    }

    public function test_sale_billing_type_label_attribute()
    {
        $this->assertEquals('Não definido', $this->sale->billing_type_label);

        \App\Services\ASAAS\Models\AsaasSale::factory()->boleto()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
        ]);
        $this->assertEquals('Boleto', $this->sale->fresh()->billing_type_label);

        $asaasSale = $this->sale->fresh()->asaas;
        $asaasSale->update(['billing_type' => 'CREDIT_CARD']);
        $this->assertEquals('Cartão de Crédito', $this->sale->fresh()->billing_type_label);

        $asaasSale->update(['billing_type' => 'PIX']);
        $this->assertEquals('PIX', $this->sale->fresh()->billing_type_label);
    }

    public function test_sale_payment_summary()
    {
        \App\Services\ASAAS\Models\AsaasSale::factory()->pending()->boleto()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
            'asaas_payment_id' => 'pay_123',
            'due_date' => '2025-08-01',
        ]);

        $summary = $this->sale->getPaymentSummary();

        $this->assertArrayHasKey('status', $summary);
        $this->assertArrayHasKey('value', $summary);
        $this->assertArrayHasKey('formatted_value', $summary);
        $this->assertArrayHasKey('has_asaas_payment', $summary);

        $this->assertEquals('pending', $summary['status']);
        $this->assertEquals(100.00, $summary['value']);
        $this->assertEquals('R$ 100,00', $summary['formatted_value']);
        $this->assertTrue($summary['has_asaas_payment']);
    }

    public function test_sync_payment_status_without_asaas_payment_id()
    {
        $syncPaymentStatus = app(SyncPaymentStatus::class);

        $result = $syncPaymentStatus->perform($this->sale);

        $this->assertFalse($result['success']);
        $this->assertEquals('no_payment_id', $result['status']);
        $this->assertStringContainsString('does not have ASAAS payment ID', $result['message']);
    }

    public function test_sync_payment_status_without_api_key()
    {
        $syncPaymentStatus = app(SyncPaymentStatus::class);

        \App\Services\ASAAS\Models\AsaasSale::factory()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->sale->organization_id,
            'client_id' => $this->sale->client_id,
            'asaas_payment_id' => 'pay_123'
        ]);

        $result = $syncPaymentStatus->perform($this->sale);

        $this->assertFalse($result['success']);
        $this->assertEquals('no_api_key', $result['status']);
        $this->assertStringContainsString('does not have ASAAS API key', $result['message']);
    }

    public function test_sync_payment_status_get_sync_summary()
    {
        $syncPaymentStatus = app(SyncPaymentStatus::class);

        $summary = $syncPaymentStatus->getSyncSummary($this->organization->id);

        $this->assertArrayHasKey('organization_id', $summary);
        $this->assertArrayHasKey('total_sales_with_asaas', $summary);
        $this->assertArrayHasKey('synced_last_24h', $summary);
        $this->assertArrayHasKey('needs_sync', $summary);
        $this->assertArrayHasKey('with_errors', $summary);
        $this->assertArrayHasKey('sync_percentage', $summary);

        $this->assertEquals($this->organization->id, $summary['organization_id']);
        $this->assertEquals(0, $summary['total_sales_with_asaas']);
        $this->assertEquals(0, $summary['synced_last_24h']);
        $this->assertEquals(0, $summary['needs_sync']);
        $this->assertEquals(0, $summary['with_errors']);
        $this->assertEquals(0, $summary['sync_percentage']);
    }
}
