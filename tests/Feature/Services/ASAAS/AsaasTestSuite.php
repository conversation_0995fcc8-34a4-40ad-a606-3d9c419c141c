<?php

namespace Tests\Feature\Services\ASAAS;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * ASAAS Test Suite Runner
 *
 * This class provides a convenient way to run all ASAAS-related tests
 * and verify the complete integration works correctly.
 */
class AsaasTestSuite extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that all ASAAS components can be instantiated
     */
    public function test_all_asaas_components_can_be_instantiated()
    {
        // Test Service
        $asaasService = app(\App\Services\ASAAS\AsaasService::class);
        $this->assertInstanceOf(\App\Services\ASAAS\AsaasService::class, $asaasService);

        // Test Factories
        $clientFactory = app(\App\Services\ASAAS\Factories\AsaasClientFactory::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Factories\AsaasClientFactory::class, $clientFactory);

        $organizationFactory = app(\App\Services\ASAAS\Factories\AsaasOrganizationFactory::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Factories\AsaasOrganizationFactory::class, $organizationFactory);

        $saleFactory = app(\App\Services\ASAAS\Factories\AsaasSaleFactory::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Factories\AsaasSaleFactory::class, $saleFactory);

        // Test Repositories
        $clientRepository = app(\App\Services\ASAAS\Repositories\AsaasClientRepository::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Repositories\AsaasClientRepository::class, $clientRepository);

        $organizationRepository = app(\App\Services\ASAAS\Repositories\AsaasOrganizationRepository::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Repositories\AsaasOrganizationRepository::class, $organizationRepository);

        $saleRepository = app(\App\Services\ASAAS\Repositories\AsaasSaleRepository::class);
        $this->assertInstanceOf(\App\Services\ASAAS\Repositories\AsaasSaleRepository::class, $saleRepository);

        // Test Use Cases
        $createCustomer = app(\App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer::class);
        $this->assertInstanceOf(\App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer::class, $createCustomer);

        $isAllowedToUseSystem = app(\App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem::class);
        $this->assertInstanceOf(\App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem::class, $isAllowedToUseSystem);

        $createSubaccount = app(\App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount::class);
        $this->assertInstanceOf(\App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount::class, $createSubaccount);

        $createPayment = app(\App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment::class);
        $this->assertInstanceOf(\App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment::class, $createPayment);

        $syncPaymentStatus = app(\App\Services\ASAAS\UseCases\Deprecated\Sales\SyncPaymentStatus::class);
        $this->assertInstanceOf(\App\Services\ASAAS\UseCases\Deprecated\Sales\SyncPaymentStatus::class, $syncPaymentStatus);
    }

    /**
     * Test that all ASAAS models can be created
     */
    public function test_all_asaas_models_can_be_created()
    {
        $asaasClient = \App\Services\ASAAS\Models\AsaasClient::factory()->create();
        $this->assertInstanceOf(\App\Services\ASAAS\Models\AsaasClient::class, $asaasClient);

        $asaasOrganization = \App\Services\ASAAS\Models\AsaasOrganization::factory()->create();
        $this->assertInstanceOf(\App\Services\ASAAS\Models\AsaasOrganization::class, $asaasOrganization);

        $asaasSale = \App\Services\ASAAS\Models\AsaasSale::factory()->create();
        $this->assertInstanceOf(\App\Services\ASAAS\Models\AsaasSale::class, $asaasSale);

        $asaasLog = \App\Services\ASAAS\Models\AsaasLog::factory()->create();
        $this->assertInstanceOf(\App\Services\ASAAS\Models\AsaasLog::class, $asaasLog);
    }

    /**
     * Test that all ASAAS domains can be created
     */
    public function test_all_asaas_domains_can_be_created()
    {
        // Test AsaasClient domain
        $asaasClient = new \App\Services\ASAAS\Domains\AsaasClient(
            id: 1,
            organization_id: 1,
            client_id: 1,
            asaas_customer_id: 'cus_123',
            asaas_synced_at: now(),
            asaas_sync_errors: null,
            created_at: now(),
            updated_at: now(),
            client: null
        );
        $this->assertInstanceOf(\App\Services\ASAAS\Domains\AsaasClient::class, $asaasClient);

        // Test AsaasOrganization domain
        $asaasOrganization = new \App\Services\ASAAS\Domains\AsaasOrganization(
            id: 1,
            organization_id: 1,
            asaas_account_id: 'acc_123',
            asaas_api_key: 'key_456',
            asaas_environment: \App\Enums\AsaasEnvironment::SANDBOX,
            subscription_status: \App\Enums\SubscriptionStatus::ACTIVE,
            subscription_expires_at: now()->addMonth(),
            is_courtesy: false,
            courtesy_expires_at: null,
            created_at: now(),
            updated_at: now(),
            organization: null
        );
        $this->assertInstanceOf(\App\Services\ASAAS\Domains\AsaasOrganization::class, $asaasOrganization);

        // Test AsaasSale domain
        $asaasSale = new \App\Services\ASAAS\Domains\AsaasSale(
            id: 1,
            organization_id: 1,
            client_id: 1,
            sale_id: 1,
            asaas_payment_id: 'pay_123',
            payment_status: \App\Enums\PaymentStatus::PENDING,
            billing_type: 'BOLETO',
            due_date: now()->addDays(7),
            payment_date: null,
            net_value: 100.00,
            original_value: 100.00,
            created_at: now(),
            updated_at: now(),
            sale: null
        );
        $this->assertInstanceOf(\App\Services\ASAAS\Domains\AsaasSale::class, $asaasSale);
    }

    /**
     * Test that ASAAS configuration is properly loaded
     */
    public function test_asaas_configuration_is_loaded()
    {
        $this->assertNotNull(config('asaas'));
        $this->assertNotNull(config('asaas.sandbox_token'));
        $this->assertNotNull(config('asaas.production_token'));
        $this->assertNotNull(config('asaas.sandbox_url'));
        $this->assertNotNull(config('asaas.production_url'));
    }

    /**
     * Test that ASAAS enums work correctly
     */
    public function test_asaas_enums_work_correctly()
    {
        // Test AsaasEnvironment enum
        $sandbox = \App\Enums\AsaasEnvironment::SANDBOX;
        $production = \App\Enums\AsaasEnvironment::PRODUCTION;

        $this->assertEquals('sandbox', $sandbox->value);
        $this->assertEquals('production', $production->value);
        $this->assertNotEquals($sandbox->getBaseUrl(), $production->getBaseUrl());

        // Test SubscriptionStatus enum
        $active = \App\Enums\SubscriptionStatus::ACTIVE;
        $inactive = \App\Enums\SubscriptionStatus::INACTIVE;
        $overdue = \App\Enums\SubscriptionStatus::OVERDUE;

        $this->assertEquals('active', $active->value);
        $this->assertEquals('inactive', $inactive->value);
        $this->assertEquals('overdue', $overdue->value);

        // Test PaymentStatus enum
        $pending = \App\Enums\PaymentStatus::PENDING;
        $received = \App\Enums\PaymentStatus::RECEIVED;

        $this->assertEquals('pending', $pending->value);
        $this->assertEquals('received', $received->value);
    }

    /**
     * Test that ASAAS exceptions work correctly
     */
    public function test_asaas_exceptions_work_correctly()
    {
        $exception = new \App\Services\ASAAS\Exceptions\AsaasException('Test error', 400);

        $this->assertInstanceOf(\App\Services\ASAAS\Exceptions\AsaasException::class, $exception);
        $this->assertEquals('Test error', $exception->getMessage());
        $this->assertEquals(400, $exception->getCode());
    }

    /**
     * Test that ASAAS database tables exist
     */
    public function test_asaas_database_tables_exist()
    {
        $this->assertTrue(\Schema::hasTable('asaas_clients'));
        $this->assertTrue(\Schema::hasTable('asaas_organizations'));
        $this->assertTrue(\Schema::hasTable('asaas_sales'));
        $this->assertTrue(\Schema::hasTable('asaas_logs'));
        $this->assertTrue(\Schema::hasTable('asaas_subscriptions'));
    }

    /**
     * Test that ASAAS database columns exist
     */
    public function test_asaas_database_columns_exist()
    {
        // Test asaas_clients table
        $this->assertTrue(\Schema::hasColumns('asaas_clients', [
            'id', 'organization_id', 'client_id', 'asaas_customer_id',
            'asaas_synced_at', 'asaas_sync_errors', 'created_at', 'updated_at'
        ]));

        // Test asaas_organizations table
        $this->assertTrue(\Schema::hasColumns('asaas_organizations', [
            'id', 'organization_id', 'asaas_account_id', 'asaas_api_key',
            'asaas_environment', 'subscription_status', 'subscription_expires_at',
            'is_courtesy', 'courtesy_expires_at', 'created_at', 'updated_at'
        ]));

        // Test asaas_sales table
        $this->assertTrue(\Schema::hasColumns('asaas_sales', [
            'id', 'organization_id', 'client_id', 'sale_id', 'asaas_payment_id',
            'payment_status', 'billing_type', 'due_date', 'payment_date',
            'net_value', 'original_value', 'created_at', 'updated_at'
        ]));

        // Test asaas_logs table
        $this->assertTrue(\Schema::hasColumns('asaas_logs', [
            'id', 'organization_id', 'user_id', 'method', 'endpoint',
            'request_data', 'response_data', 'response_status', 'is_error',
            'created_at', 'updated_at'
        ]));
    }

    /**
     * Test that ASAAS commands are registered
     */
    public function test_asaas_commands_are_registered()
    {
        $commands = \Artisan::all();

        $this->assertArrayHasKey('asaas:sync-payments', $commands);
        $this->assertArrayHasKey('asaas:process-logs', $commands);
    }

    /**
     * Test that ASAAS service provider is loaded
     */
    public function test_asaas_service_provider_is_loaded()
    {
        $providers = app()->getLoadedProviders();

        // Check if ASAAS-related services are bound
        $this->assertTrue(app()->bound(\App\Services\ASAAS\AsaasService::class));
        $this->assertTrue(app()->bound(\App\Services\ASAAS\Factories\AsaasClientFactory::class));
        $this->assertTrue(app()->bound(\App\Services\ASAAS\Repositories\AsaasClientRepository::class));
    }
}
