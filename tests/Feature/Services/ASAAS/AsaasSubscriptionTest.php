<?php

namespace Tests\Feature\Services\ASAAS;

use App\Models\Subscription;
use App\Services\ASAAS\Domains\AsaasSubscription;
use App\Services\ASAAS\Factories\AsaasSubscriptionFactory;
use App\Services\ASAAS\Models\AsaasSubscription as AsaasSubscriptionModel;
use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AsaasSubscriptionTest extends TestCase
{
    use RefreshDatabase;

    private AsaasSubscriptionFactory $factory;
    private AsaasSubscriptionRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new AsaasSubscriptionFactory();
        $this->repository = new AsaasSubscriptionRepository($this->factory);
    }

    /** @test */
    public function it_can_create_asaas_subscription_model()
    {
        $subscription = Subscription::factory()->create();

        $asaasSubscription = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->create();

        $this->assertDatabaseHas('asaas_subscriptions', [
            'id' => $asaasSubscription->id,
            'subscription_id' => $subscription->id,
        ]);
    }

    /** @test */
    public function it_can_build_domain_from_model()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->active()
            ->synced()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(AsaasSubscription::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->subscription_id, $domain->subscription_id);
        $this->assertEquals($model->asaas_subscription_id, $domain->asaas_subscription_id);
        $this->assertTrue($domain->isActive());
        $this->assertTrue($domain->isSynced());
    }

    /** @test */
    public function it_can_create_active_asaas_subscription()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->active()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->isActive());
        $this->assertEquals('ACTIVE', $domain->status);
    }

    /** @test */
    public function it_can_create_synced_asaas_subscription()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->synced()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->isSynced());
        $this->assertEquals('synced', $domain->sync_status);
        $this->assertNotNull($domain->asaas_synced_at);
        $this->assertNull($domain->asaas_sync_errors);
    }

    /** @test */
    public function it_can_create_asaas_subscription_with_errors()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->withErrors()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->hasError());
        $this->assertEquals('error', $domain->sync_status);
        $this->assertNotNull($domain->asaas_sync_errors);
        $this->assertTrue($domain->hasErrors());
    }

    /** @test */
    public function it_can_create_asaas_subscription_needing_sync()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->needsSync()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->needsSync());
    }

    /** @test */
    public function it_can_find_asaas_subscription_by_subscription_id()
    {
        $subscription = Subscription::factory()->create();
        $model = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->create();

        $found = $this->repository->findBySubscriptionId($subscription->id);

        $this->assertInstanceOf(AsaasSubscription::class, $found);
        $this->assertEquals($model->id, $found->id);
        $this->assertEquals($subscription->id, $found->subscription_id);
    }

    /** @test */
    public function it_can_find_asaas_subscription_by_asaas_subscription_id()
    {
        $model = AsaasSubscriptionModel::factory()->create();

        $found = $this->repository->findByAsaasSubscriptionId($model->asaas_subscription_id);

        $this->assertInstanceOf(AsaasSubscription::class, $found);
        $this->assertEquals($model->id, $found->id);
        $this->assertEquals($model->asaas_subscription_id, $found->asaas_subscription_id);
    }

    /** @test */
    public function it_can_find_asaas_subscriptions_needing_sync()
    {
        // Clean up any existing records first
        AsaasSubscriptionModel::truncate();

        AsaasSubscriptionModel::factory()->needsSync()->count(3)->create();
        AsaasSubscriptionModel::factory()->synced()->count(2)->create();

        $needingSync = $this->repository->findNeedingSync();

        $this->assertCount(3, $needingSync);
        $this->assertTrue($needingSync->every(fn($sub) => $sub->needsSync()));
    }

    /** @test */
    public function it_can_find_asaas_subscriptions_with_errors()
    {
        AsaasSubscriptionModel::factory()->withErrors()->count(2)->create();
        AsaasSubscriptionModel::factory()->synced()->count(3)->create();

        $withErrors = $this->repository->findWithErrors();

        $this->assertCount(2, $withErrors);
        $this->assertTrue($withErrors->every(fn($sub) => $sub->hasError()));
    }

    /** @test */
    public function it_can_create_asaas_subscription_through_repository()
    {
        $subscription = Subscription::factory()->create();

        $domain = new AsaasSubscription(
            id: null,
            subscription_id: $subscription->id,
            asaas_customer_id: 'cus_test123',
            asaas_subscription_id: 'sub_test123',
            asaas_date_created: now(),
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            billing_type: 'BOLETO',
            cycle: 'MONTHLY',
            value: 99.90,
            next_due_date: now()->addMonth(),
            end_date: null,
            description: 'Test subscription',
            status: 'ACTIVE',
            max_payments: null,
            external_reference: 'test_ref',
            payment_link: null,
            checkout_session: null,
            discount_value: null,
            discount_type: null,
            discount_due_date_limit_days: null,
            fine_value: null,
            fine_type: null,
            interest_value: null,
            credit_card_number: null,
            credit_card_brand: null,
            credit_card_token: null,
            split_data: null,
            deleted: false,
        );

        $saved = $this->repository->save($domain);

        $this->assertInstanceOf(AsaasSubscription::class, $saved);
        $this->assertNotNull($saved->id);
        $this->assertEquals($subscription->id, $saved->subscription_id);
        $this->assertEquals('cus_test123', $saved->asaas_customer_id);
        $this->assertEquals('sub_test123', $saved->asaas_subscription_id);

        $this->assertDatabaseHas('asaas_subscriptions', [
            'subscription_id' => $subscription->id,
            'asaas_customer_id' => 'cus_test123',
            'asaas_subscription_id' => 'sub_test123',
            'status' => 'ACTIVE',
            'value' => 99.90,
        ]);
    }

    /** @test */
    public function it_validates_asaas_subscription_business_logic()
    {
        // Active subscription
        $activeModel = AsaasSubscriptionModel::factory()->active()->create();
        $activeDomain = $this->factory->buildFromModel($activeModel);
        $this->assertTrue($activeDomain->isActive());
        $this->assertFalse($activeDomain->isExpired());
        $this->assertFalse($activeDomain->isCancelled());

        // Expired subscription
        $expiredModel = AsaasSubscriptionModel::factory()->expired()->create();
        $expiredDomain = $this->factory->buildFromModel($expiredModel);
        $this->assertTrue($expiredDomain->isExpired());
        $this->assertFalse($expiredDomain->isActive());

        // Cancelled subscription
        $cancelledModel = AsaasSubscriptionModel::factory()->cancelled()->create();
        $cancelledDomain = $this->factory->buildFromModel($cancelledModel);
        $this->assertTrue($cancelledDomain->isCancelled());
        $this->assertFalse($cancelledDomain->isActive());
    }

    /** @test */
    public function it_can_generate_asaas_payload()
    {
        $model = AsaasSubscriptionModel::factory()
            ->withDiscount()
            ->withFineAndInterest()
            ->create();

        $domain = $this->factory->buildFromModel($model);
        $payload = $domain->toAsaasPayload();

        $this->assertArrayHasKey('customer', $payload);
        $this->assertArrayHasKey('billingType', $payload);
        $this->assertArrayHasKey('value', $payload);
        $this->assertArrayHasKey('cycle', $payload);
        $this->assertEquals($domain->asaas_customer_id, $payload['customer']);
        $this->assertEquals($domain->billing_type, $payload['billingType']);
        $this->assertEquals($domain->value, $payload['value']);
        $this->assertEquals($domain->cycle, $payload['cycle']);

        if ($domain->hasDiscount()) {
            $this->assertArrayHasKey('discount', $payload);
            $this->assertArrayHasKey('value', $payload['discount']);
            $this->assertArrayHasKey('type', $payload['discount']);
        }

        if ($domain->hasFine()) {
            $this->assertArrayHasKey('fine', $payload);
            $this->assertArrayHasKey('value', $payload['fine']);
        }

        if ($domain->hasInterest()) {
            $this->assertArrayHasKey('interest', $payload);
            $this->assertArrayHasKey('value', $payload['interest']);
        }
    }

    /** @test */
    public function subscription_model_has_asaas_subscription_relationship()
    {
        $subscription = Subscription::factory()->create();
        $asaasSubscription = AsaasSubscriptionModel::factory()
            ->forSubscription($subscription)
            ->create();

        $subscription->refresh();

        $this->assertInstanceOf(AsaasSubscriptionModel::class, $subscription->asaasSubscription);
        $this->assertEquals($asaasSubscription->id, $subscription->asaasSubscription->id);
        $this->assertTrue($subscription->hasAsaasIntegration());
    }

    /** @test */
    public function it_can_get_repository_statistics()
    {
        // Clean up any existing records first
        AsaasSubscriptionModel::truncate();

        // Create records and track their IDs to ensure we're counting correctly
        $activeRecords = AsaasSubscriptionModel::factory()->active()->count(3)->create();
        $expiredRecords = AsaasSubscriptionModel::factory()->expired()->count(2)->create();
        $cancelledRecords = AsaasSubscriptionModel::factory()->cancelled()->count(1)->create();
        $errorRecords = AsaasSubscriptionModel::factory()->withErrors()->count(2)->create();
        $syncRecords = AsaasSubscriptionModel::factory()->needsSync()->count(1)->create();

        // Verify the records were created with correct statuses
        $this->assertEquals(3, $activeRecords->count());
        $this->assertEquals(2, $expiredRecords->count());
        $this->assertEquals(1, $cancelledRecords->count());
        $this->assertEquals(2, $errorRecords->where('sync_status', 'error')->count());
        $this->assertEquals(1, $syncRecords->count());

        $stats = $this->repository->getStatistics();

        $this->assertArrayHasKey('total', $stats);
        $this->assertArrayHasKey('active', $stats);
        $this->assertArrayHasKey('expired', $stats);
        $this->assertArrayHasKey('cancelled', $stats);
        $this->assertArrayHasKey('errors', $stats);
        $this->assertArrayHasKey('needs_sync', $stats);



        $this->assertEquals(9, $stats['total']); // 3+2+1+2+1
        $this->assertEquals(4, $stats['active']); // 3 from active() + 1 from needsSync()
        $this->assertEquals(2, $stats['expired']);
        $this->assertEquals(1, $stats['cancelled']);
        $this->assertEquals(2, $stats['errors']); // 2 from withErrors() (counted by sync_status)
    }
}
