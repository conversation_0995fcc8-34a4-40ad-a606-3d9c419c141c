<?php

namespace Tests\Feature\Services\ASAAS\UseCases\Customer;

use App\Domains\Inventory\Client as ClientDomain;
use App\Domains\Organization;
use App\Models\Client;
use App\Models\Organization as OrganizationModel;
use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Models\AsaasClient as AsaasClientModel;
use App\Services\ASAAS\Models\AsaasOrganization as AsaasOrganizationModel;
use App\Services\ASAAS\Repositories\AsaasClientRepository;

use App\Services\ASAAS\UseCases\Customer\CreateCustomerClient;
use App\UseCases\Organization\Get as GetOrganization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateCustomerClientTest extends TestCase
{
    use RefreshDatabase;

    private CreateCustomerClient $useCase;
    private CustomerService $customerService;
    private AsaasClientFactory $asaasClientFactory;
    private AsaasClientRepository $asaasClientRepository;

    private GetOrganization $getOrganization;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock external dependencies
        $this->customerService = Mockery::mock(CustomerService::class);
        $this->asaasClientFactory = app()->make(AsaasClientFactory::class);
        $this->asaasClientRepository = app()->make(AsaasClientRepository::class);

        $this->getOrganization = app()->make(GetOrganization::class);

        $this->useCase = new CreateCustomerClient(
            $this->asaasClientFactory,
            $this->asaasClientRepository,

            $this->getOrganization
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Create test data in database
     */
    protected function createTestData()
    {
        // Create organization with ASAAS integration
        $organizationModel = OrganizationModel::factory()->create([
            'subscription_status' => 'active'
        ]);
        $asaasOrganization = AsaasOrganizationModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'asaas_account_id' => 'acc_test123',
            'asaas_api_key' => 'test_api_key',
        ]);

        // Create client
        $clientModel = Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpf' => '***********',
        ]);

        // Convert to domain object
        $client = new ClientDomain(
            id: $clientModel->id,
            organization_id: $clientModel->organization_id,
            name: $clientModel->name,
            phone: $clientModel->phone,
            email: $clientModel->email,
            profession: $clientModel->profession,
            birthdate: $clientModel->birthdate,
            cpf: $clientModel->cpf,
            cnpj: $clientModel->cnpj,
            service: $clientModel->service,
            address: $clientModel->address,
            number: $clientModel->number,
            neighborhood: $clientModel->neighborhood,
            cep: $clientModel->cep,
            complement: $clientModel->complement,
            civil_state: $clientModel->civil_state,
            description: $clientModel->description,
            created_at: $clientModel->created_at,
            updated_at: $clientModel->updated_at
        );

        return [$organizationModel, $asaasOrganization, $client];
    }

    public function test_throws_exception_when_client_already_has_asaas_integration()
    {
        [$organizationModel, $asaasOrganization, $client] = $this->createTestData();

        // Create existing AsaasClient for this client
        AsaasClientModel::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organizationModel->id,
            'asaas_customer_id' => 'existing_customer_123',
        ]);

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Client already has an ASAAS customer integration');

        $this->useCase->perform($client);
    }

    public function test_throws_exception_when_organization_has_no_api_key()
    {
        [$organizationModel, $asaasOrganization, $client] = $this->createTestData();

        // Remove API key from organization
        $asaasOrganization->update(['asaas_api_key' => null]);

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Organization does not have ASAAS API key configured');

        $this->useCase->perform($client);
    }

    public function test_creates_customer_successfully()
    {
        [$organizationModel, $asaasOrganization, $client] = $this->createTestData();

        // Mock ASAAS API response
        $asaasResponse = [
            'id' => 'cus_123456',
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpfCnpj' => '***********',
        ];

        // Mock the customer service create method
        $this->customerService
            ->shouldReceive('create')
            ->once()
            ->with(Mockery::type('array'))
            ->andReturn($asaasResponse);

        // Replace the customer service in the use case
        $reflection = new \ReflectionClass($this->useCase);
        $property = $reflection->getProperty('customerService');
        $property->setAccessible(true);
        $property->setValue($this->useCase, $this->customerService);

        $result = $this->useCase->perform($client);

        // Verify the result
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('Customer created successfully', $result['message']);
        $this->assertEquals('cus_123456', $result['asaas_customer_id']);
        $this->assertArrayHasKey('customer_data', $result);

        // Verify the AsaasClient was saved to database
        $this->assertDatabaseHas('asaas_clients', [
            'client_id' => $client->id,
            'asaas_customer_id' => 'cus_123456',
        ]);

        // Verify the log was created
        $this->assertDatabaseHas('asaas_logs', [
            'organization_id' => $organizationModel->id,
            'endpoint' => 'customers',
            'method' => 'POST',
            'status' => 'success',
        ]);
    }

    public function test_client_to_asaas_customer_payload_method()
    {
        [$organizationModel, $asaasOrganization, $client] = $this->createTestData();

        $payload = $client->toAsaasCustomerPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('Test Client', $payload['name']);
        $this->assertEquals('<EMAIL>', $payload['email']);
        $this->assertEquals('***********', $payload['cpfCnpj']);
        $this->assertEquals((string) $client->id, $payload['externalReference']);
        $this->assertFalse($payload['notificationDisabled']);
        $this->assertFalse($payload['foreignCustomer']);
    }
}
