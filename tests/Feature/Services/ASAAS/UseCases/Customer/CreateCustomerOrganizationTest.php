<?php

namespace Tests\Feature\Services\ASAAS\UseCases\Customer;

use App\Domains\Organization;
use App\Models\Organization as OrganizationModel;
use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Services\ASAAS\Models\AsaasOrganizationCustomer as AsaasOrganizationCustomerModel;
use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;

use App\Services\ASAAS\UseCases\Customer\CreateCustomerOrganization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateCustomerOrganizationTest extends TestCase
{
    use RefreshDatabase;

    private CreateCustomerOrganization $useCase;
    private CustomerService $customerService;
    private AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory;
    private AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository;


    protected function setUp(): void
    {
        parent::setUp();

        // Mock external dependencies
        $this->customerService = Mockery::mock(CustomerService::class);
        $this->asaasOrganizationCustomerFactory = app()->make(AsaasOrganizationCustomerFactory::class);
        $this->asaasOrganizationCustomerRepository = app()->make(AsaasOrganizationCustomerRepository::class);


        $this->useCase = new CreateCustomerOrganization(
            $this->asaasOrganizationCustomerFactory,
            $this->asaasOrganizationCustomerRepository,

        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Create test data in database
     */
    protected function createTestData()
    {
        // Create organization
        $organizationModel = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'cpf_cnpj' => '12345678901234',
            'subscription_status' => 'active'
        ]);

        // Convert to domain object
        $organization = new Organization(
            id: $organizationModel->id,
            name: $organizationModel->name,
            description: $organizationModel->description,
            is_active: $organizationModel->is_active,
            is_suspended: $organizationModel->is_suspended,
            default_flow_id: $organizationModel->default_flow_id,
            email: $organizationModel->email,
            cpf_cnpj: $organizationModel->cpf_cnpj,
            created_at: $organizationModel->created_at,
            updated_at: $organizationModel->updated_at
        );

        return [$organizationModel, $organization];
    }

    public function test_throws_exception_when_organization_already_has_asaas_customer_integration()
    {
        [$organizationModel, $organization] = $this->createTestData();

        // Create existing AsaasOrganizationCustomer for this organization
        AsaasOrganizationCustomerModel::factory()->create([
            'organization_id' => $organization->id,
            'asaas_customer_id' => 'existing_customer_123',
        ]);

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Organization already has an ASAAS customer integration');

        $this->useCase->perform($organization);
    }

    public function test_creates_customer_successfully()
    {
        [$organizationModel, $organization] = $this->createTestData();

        // Mock ASAAS API response
        $asaasResponse = [
            'id' => 'cus_org_123456',
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'cpfCnpj' => '12345678901234',
        ];

        // Mock the customer service create method
        $this->customerService
            ->shouldReceive('create')
            ->once()
            ->with(Mockery::type('array'))
            ->andReturn($asaasResponse);

        // Replace the customer service in the use case
        $reflection = new \ReflectionClass($this->useCase);
        $property = $reflection->getProperty('customerService');
        $property->setAccessible(true);
        $property->setValue($this->useCase, $this->customerService);

        $result = $this->useCase->perform($organization);

        // Verify the result
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('Customer created successfully', $result['message']);
        $this->assertEquals('cus_org_123456', $result['asaas_customer_id']);
        $this->assertArrayHasKey('customer_data', $result);

        // Verify the AsaasOrganizationCustomer was saved to database
        $this->assertDatabaseHas('asaas_organization_customers', [
            'organization_id' => $organization->id,
            'asaas_customer_id' => 'cus_org_123456',
        ]);

        // Verify the log was created
        $this->assertDatabaseHas('asaas_logs', [
            'organization_id' => $organizationModel->id,
            'endpoint' => 'customers',
            'method' => 'POST',
            'status' => 'success',
        ]);
    }

    public function test_handles_asaas_service_exception()
    {
        [$organizationModel, $organization] = $this->createTestData();

        // Mock the customer service to throw an exception
        $this->customerService
            ->shouldReceive('create')
            ->once()
            ->with(Mockery::type('array'))
            ->andThrow(new \Exception('ASAAS API error'));

        // Replace the customer service in the use case
        $reflection = new \ReflectionClass($this->useCase);
        $property = $reflection->getProperty('customerService');
        $property->setAccessible(true);
        $property->setValue($this->useCase, $this->customerService);

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Failed to create customer: ASAAS API error');

        $this->useCase->perform($organization);

        // Verify error log was created
        $this->assertDatabaseHas('asaas_logs', [
            'organization_id' => $organizationModel->id,
            'endpoint' => 'customers',
            'method' => 'POST',
            'status' => 'error',
        ]);
    }

    public function test_organization_to_asaas_customer_payload_method()
    {
        [$organizationModel, $organization] = $this->createTestData();

        $payload = $organization->toAsaasCustomerPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('Test Organization', $payload['name']);
        $this->assertEquals('<EMAIL>', $payload['email']);
        $this->assertEquals('12345678901234', $payload['cpfCnpj']);
        $this->assertEquals((string) $organization->id, $payload['externalReference']);
        $this->assertFalse($payload['notificationDisabled']);
        $this->assertFalse($payload['foreignCustomer']);
    }
}
