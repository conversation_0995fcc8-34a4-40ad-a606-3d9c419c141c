<?php

namespace Tests\Feature\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\UseCases\Send;
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\Domains\WelcomeEmail;
use App\Services\Resend\Exceptions\ResendException;
use App\Domains\User;
use App\Domains\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class SendEmailTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('resend.api_key', 'test-api-key');
        Config::set('resend.api_url', 'https://api.resend.com');
        Config::set('resend.sandbox_mode', true);

        $this->organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null,
            Carbon::now(),
            Carbon::now()
        );

        $this->user = new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $this->organization
        );
    }

    public function test_send_password_reset_email_successfully()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'id' => 'password-reset-email-id',
                'from' => '<EMAIL>',
                'to' => ['<EMAIL>'],
                'created_at' => '2024-01-01T00:00:00Z'
            ], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-reset-token', 60);

        $result = $sendUseCase->perform($email);

        $this->assertIsArray($result);
        $this->assertEquals('password-reset-email-id', $result['id']);

        Http::assertSent(function ($request) {
            $data = $request->data();
            return $request->url() === 'https://api.resend.com/emails' &&
                   $request->hasHeader('Authorization', 'Bearer test-api-key') &&
                   $data['subject'] === 'Redefinir sua senha - ' . config('app.name') &&
                   $data['to'][0]['email'] === '<EMAIL>' && // sandbox mode
                   isset($data['html']) &&
                   $data['tags']['type'] === 'auth';
        });
    }

    public function test_send_welcome_email_successfully()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'id' => 'welcome-email-id',
                'from' => '<EMAIL>',
                'to' => ['<EMAIL>'],
                'created_at' => '2024-01-01T00:00:00Z'
            ], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new WelcomeEmail($this->user, $this->organization);

        $result = $sendUseCase->perform($email);

        $this->assertIsArray($result);
        $this->assertEquals('welcome-email-id', $result['id']);

        Http::assertSent(function ($request) {
            $data = $request->data();
            return $data['subject'] === 'Bem-vindo ao ' . config('app.name') . '!' &&
                   $data['tags']['type'] === 'onboarding' &&
                   isset($data['html']);
        });
    }

    public function test_send_email_handles_api_authentication_error()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Invalid API key'
            ], 401)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend authentication failed');

        $sendUseCase->perform($email);
    }

    public function test_send_email_handles_bad_request_error()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response([
                'message' => 'Invalid email format'
            ], 400)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $this->expectException(ResendException::class);
        $this->expectExceptionMessage('Resend API bad request: Invalid email format');

        $sendUseCase->perform($email);
    }

    public function test_send_email_validates_template_rendering()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $result = $sendUseCase->perform($email);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $html = $data['html'];

            // Verify template was rendered with correct data
            return strpos($html, 'John Doe') !== false &&
                   strpos($html, 'Redefinir Senha') !== false &&
                   strpos($html, 'password/reset/test-token') !== false &&
                   strpos($html, 'john%40example.com') !== false; // URL encoded email
        });
    }

    public function test_send_email_includes_correct_tags()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);

        // Test different email types have correct tags
        $emails = [
            ['email' => new PasswordResetEmail($this->user, 'token'), 'expected_type' => 'auth', 'expected_priority' => 'high'],
            ['email' => new WelcomeEmail($this->user, $this->organization), 'expected_type' => 'onboarding', 'expected_priority' => 'medium']
        ];

        foreach ($emails as $emailData) {
            $sendUseCase->perform($emailData['email']);

            Http::assertSent(function ($request) use ($emailData) {
                $data = $request->data();
                return $data['tags']['type'] === $emailData['expected_type'] &&
                       $data['tags']['priority'] === $emailData['expected_priority'];
            });
        }
    }

    public function test_send_email_with_sandbox_mode_disabled()
    {
        Config::set('resend.sandbox_mode', false);

        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $sendUseCase->perform($email);

        Http::assertSent(function ($request) {
            $data = $request->data();
            // In non-sandbox mode, should use actual recipient email
            return $data['to'][0]['email'] === '<EMAIL>';
        });
    }

    public function test_send_email_retries_on_temporary_failure()
    {
        // First request fails, second succeeds
        Http::fake([
            'https://api.resend.com/emails' => Http::sequence()
                ->push(['message' => 'Temporary error'], 503)
                ->push(['id' => 'success-after-retry'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $result = $sendUseCase->perform($email);

        $this->assertEquals('success-after-retry', $result['id']);

        // Should have made 2 requests (initial + 1 retry)
        Http::assertSentCount(2);
    }

    public function test_send_email_logs_are_created()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'test-token');

        $result = $sendUseCase->perform($email);

        // Verify logs were created (this would require checking the database in a real test)
        $this->assertIsArray($result);
        $this->assertEquals('test-id', $result['id']);
    }
}
