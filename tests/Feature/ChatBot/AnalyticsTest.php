<?php

namespace Tests\Feature\ChatBot;

use App\Models\Campaign;
use App\Models\Message;
use App\Models\CampaignAnalytics;
use App\Models\MessageEngagementEvent;
use App\Models\User;
use App\Models\Organization;
use App\Enums\EngagementEventType;
use App\Enums\CampaignStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AnalyticsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;
    private Message $message;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);
        $this->message = Message::factory()->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_get_campaign_analytics()
    {
        CampaignAnalytics::factory()->create([
            'campaign_id' => $this->campaign->id,
            'total_messages' => 100,
            'delivered_count' => 95,
            'read_count' => 80,
            'response_count' => 15
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/analytics/campaign/{$this->campaign->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaign_id',
                         'analytics' => [
                             'delivery_rate',
                             'read_rate',
                             'response_rate',
                             'performance_grade'
                         ],
                         'insights',
                         'comparison'
                     ]
                 ]);
    }

    public function test_can_force_recalculate_analytics()
    {
        $response = $this->actingAs($this->user)
                         ->getJson("/api/analytics/campaign/{$this->campaign->id}?force_recalculate=true");

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('campaign_analytics', [
            'campaign_id' => $this->campaign->id
        ]);
    }

    public function test_can_get_analytics_dashboard()
    {
        CampaignAnalytics::factory()->count(3)->create();

        $response = $this->actingAs($this->user)
                         ->getJson('/api/analytics/dashboard');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'summary' => [
                             'total_campaigns',
                             'total_messages',
                             'avg_delivery_rate'
                         ],
                         'top_campaigns',
                         'trends',
                         'recommendations'
                     ]
                 ]);
    }

    public function test_can_record_engagement_event()
    {
        $eventData = [
            'message_id' => $this->message->id,
            'event_type' => 'delivered',
            'metadata' => ['test' => 'data'],
            'user_phone' => '+1234567890'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/engagement/record', $eventData);

        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'data' => [
                         'event_id',
                         'message_id',
                         'event_type',
                         'is_positive_engagement'
                     ]
                 ]);

        $this->assertDatabaseHas('message_engagement_events', [
            'message_id' => $this->message->id,
            'event_type' => 'delivered'
        ]);
    }

    public function test_can_record_bulk_engagement_events()
    {
        $message2 = Message::factory()->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id
        ]);

        $events = [
            [
                'message_id' => $this->message->id,
                'event_type' => 'delivered'
            ],
            [
                'message_id' => $message2->id,
                'event_type' => 'read'
            ]
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/engagement/bulk', ['events' => $events]);

        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'data' => [
                         'total_events',
                         'success_count',
                         'results'
                     ]
                 ]);

        $this->assertDatabaseCount('message_engagement_events', 2);
    }

    public function test_can_get_message_engagement_summary()
    {
        MessageEngagementEvent::factory()->create([
            'message_id' => $this->message->id,
            'campaign_id' => $this->campaign->id,
            'event_type' => EngagementEventType::DELIVERED
        ]);

        MessageEngagementEvent::factory()->create([
            'message_id' => $this->message->id,
            'campaign_id' => $this->campaign->id,
            'event_type' => EngagementEventType::READ
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/analytics/message/{$this->message->id}/engagement");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'message_id',
                         'total_events',
                         'event_types',
                         'timeline',
                         'engagement_score'
                     ]
                 ]);
    }

    public function test_can_get_multiple_campaign_analytics()
    {
        $campaign2 = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        CampaignAnalytics::factory()->create(['campaign_id' => $this->campaign->id]);
        CampaignAnalytics::factory()->create(['campaign_id' => $campaign2->id]);

        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/campaigns/multiple', [
                             'campaign_ids' => [$this->campaign->id, $campaign2->id]
                         ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaigns',
                         'total_requested',
                         'successful'
                     ]
                 ]);
    }

    public function test_can_compare_campaign_performance()
    {
        $campaign2 = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        CampaignAnalytics::factory()->create([
            'campaign_id' => $this->campaign->id,
            'delivery_rate' => 95.0
        ]);
        CampaignAnalytics::factory()->create([
            'campaign_id' => $campaign2->id,
            'delivery_rate' => 85.0
        ]);

        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/campaigns/compare', [
                             'campaign_ids' => [$this->campaign->id, $campaign2->id]
                         ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaigns',
                         'comparison_metrics' => [
                             'best_delivery_rate',
                             'worst_delivery_rate',
                             'avg_delivery_rate'
                         ]
                     ]
                 ]);
    }

    public function test_can_trigger_analytics_calculation()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/trigger-calculation');

        $response->assertStatus(200)
                 ->assertJsonFragment(['job_dispatched' => true]);
    }

    public function test_engagement_event_validation()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/analytics/engagement/record', [
                             'message_id' => 'invalid',
                             'event_type' => 'invalid_type'
                         ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['message_id', 'event_type']);
    }

    public function test_cannot_access_other_organization_analytics()
    {
        $otherOrg = Organization::factory()->create();
        $otherCampaign = Campaign::factory()->create([
            'organization_id' => $otherOrg->id
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/analytics/campaign/{$otherCampaign->id}");

        $response->assertStatus(422);
    }
}
