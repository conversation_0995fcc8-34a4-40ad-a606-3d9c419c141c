<?php

namespace Tests\Feature\ChatBot;

use App\Models\Category;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_list_categories()
    {
        Category::factory()->count(3)->create();

        $response = $this->actingAs($this->user)
                         ->getJson('/api/categories');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'categories' => [
                             '*' => ['id', 'name', 'type', 'description']
                         ]
                     ]
                 ]);
    }

    public function test_can_create_category()
    {
        $categoryData = [
            'name' => 'Test Category',
            'type' => 'campaign',
            'description' => 'Test description'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/api/categories', $categoryData);

        $response->assertStatus(201)
                 ->assertJsonFragment([
                     'name' => 'Test Category',
                     'type' => 'campaign'
                 ]);

        $this->assertDatabaseHas('categories', $categoryData);
    }

    public function test_cannot_create_category_with_duplicate_name()
    {
        Category::factory()->create(['name' => 'Existing Category']);

        $response = $this->actingAs($this->user)
                         ->postJson('/api/categories', [
                             'name' => 'Existing Category',
                             'type' => 'campaign'
                         ]);

        $response->assertStatus(422);
    }

    public function test_can_update_category()
    {
        $category = Category::factory()->create();

        $updateData = [
            'name' => 'Updated Category',
            'description' => 'Updated description'
        ];

        $response = $this->actingAs($this->user)
                         ->putJson("/api/categories/{$category->id}", $updateData);

        $response->assertStatus(200)
                 ->assertJsonFragment(['name' => 'Updated Category']);

        $this->assertDatabaseHas('categories', [
            'id' => $category->id,
            'name' => 'Updated Category'
        ]);
    }

    public function test_can_delete_category()
    {
        $category = Category::factory()->create();

        $response = $this->actingAs($this->user)
                         ->deleteJson("/api/categories/{$category->id}");

        $response->assertStatus(200);
        $this->assertDatabaseMissing('categories', ['id' => $category->id]);
    }

    public function test_category_validation_rules()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/categories', []);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['name', 'type']);
    }

    public function test_category_type_validation()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/categories', [
                             'name' => 'Test Category',
                             'type' => 'invalid_type'
                         ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['type']);
    }
}
