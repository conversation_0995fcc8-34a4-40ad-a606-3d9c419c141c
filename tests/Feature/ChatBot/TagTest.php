<?php

namespace Tests\Feature\ChatBot;

use App\Models\Tag;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TagTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_list_tags()
    {
        Tag::factory()->count(5)->create();

        $response = $this->actingAs($this->user)
                         ->getJson('/api/tags');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'tags' => [
                             '*' => ['id', 'name', 'usage_count']
                         ]
                     ]
                 ]);
    }

    public function test_can_get_most_used_tags()
    {
        Tag::factory()->create(['name' => 'Popular Tag', 'usage_count' => 10]);
        Tag::factory()->create(['name' => 'Less Popular', 'usage_count' => 5]);
        Tag::factory()->create(['name' => 'Rarely Used', 'usage_count' => 1]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/tags/most-used?limit=2');

        $response->assertStatus(200);
        
        $tags = $response->json('data.tags');
        $this->assertCount(2, $tags);
        $this->assertEquals('Popular Tag', $tags[0]['name']);
        $this->assertEquals('Less Popular', $tags[1]['name']);
    }

    public function test_can_get_tag_suggestions()
    {
        Tag::factory()->create(['name' => 'marketing']);
        Tag::factory()->create(['name' => 'market-research']);
        Tag::factory()->create(['name' => 'sales']);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/tags/suggestions?query=mark');

        $response->assertStatus(200);
        
        $suggestions = $response->json('data.suggestions');
        $this->assertCount(2, $suggestions);
        
        $names = array_column($suggestions, 'name');
        $this->assertContains('marketing', $names);
        $this->assertContains('market-research', $names);
        $this->assertNotContains('sales', $names);
    }

    public function test_tag_suggestions_are_case_insensitive()
    {
        Tag::factory()->create(['name' => 'Marketing']);
        Tag::factory()->create(['name' => 'MARKET-RESEARCH']);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/tags/suggestions?query=MARK');

        $response->assertStatus(200);
        
        $suggestions = $response->json('data.suggestions');
        $this->assertCount(2, $suggestions);
    }

    public function test_empty_query_returns_most_used_tags()
    {
        Tag::factory()->create(['name' => 'Popular', 'usage_count' => 10]);
        Tag::factory()->create(['name' => 'Less Popular', 'usage_count' => 5]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/tags/suggestions?query=');

        $response->assertStatus(200);
        
        $suggestions = $response->json('data.suggestions');
        $this->assertGreaterThan(0, count($suggestions));
        $this->assertEquals('Popular', $suggestions[0]['name']);
    }
}
