<?php

namespace Tests\Feature\ChatBot;

use App\Models\Campaign;
use App\Models\Message;
use App\Models\WhatsAppSyncLog;
use App\Models\User;
use App\Models\Organization;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WhatsAppSyncTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;
    private Message $message;
    private WhatsAppMessage $whatsappMessage;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->message = Message::factory()->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id
        ]);
        $this->whatsappMessage = WhatsAppMessage::factory()->create([
            'message_id' => $this->message->id,
            'needs_status_check' => true
        ]);
    }

    public function test_can_sync_message_status()
    {
        $response = $this->actingAs($this->user)
                         ->postJson("/api/whatsapp/sync/message/{$this->message->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'message_id',
                         'sync_status',
                         'messages_synced',
                         'sync_log_id'
                     ]
                 ]);
    }

    public function test_can_sync_campaign_messages()
    {
        $response = $this->actingAs($this->user)
                         ->postJson("/api/whatsapp/sync/campaign/{$this->campaign->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaign_id',
                         'sync_status',
                         'messages_synced',
                         'messages_updated'
                     ]
                 ]);
    }

    public function test_can_get_sync_logs()
    {
        WhatsAppSyncLog::factory()->create([
            'sync_type' => SyncType::MESSAGE,
            'entity_id' => $this->message->id,
            'status' => SyncStatus::SUCCESS
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/whatsapp/sync/logs');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'logs',
                         'statistics',
                         'total_logs'
                     ]
                 ]);
    }

    public function test_can_filter_sync_logs_by_status()
    {
        WhatsAppSyncLog::factory()->create([
            'status' => SyncStatus::SUCCESS,
            'synced_at' => now()
        ]);
        WhatsAppSyncLog::factory()->create([
            'status' => SyncStatus::FAILED,
            'synced_at' => now()
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/whatsapp/sync/logs?status=failed');

        $response->assertStatus(200);
        
        $logs = $response->json('data.logs');
        foreach ($logs as $log) {
            $this->assertEquals('failed', $log['status']);
        }
    }

    public function test_can_get_entity_logs()
    {
        WhatsAppSyncLog::factory()->create([
            'sync_type' => SyncType::MESSAGE,
            'entity_id' => $this->message->id,
            'status' => SyncStatus::SUCCESS
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/whatsapp/sync/entity-logs?sync_type=message&entity_id={$this->message->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'entity_type',
                         'entity_id',
                         'logs',
                         'sync_summary'
                     ]
                 ]);
    }

    public function test_can_get_sync_trends()
    {
        WhatsAppSyncLog::factory()->count(5)->create([
            'synced_at' => now()->subDays(1)
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/whatsapp/sync/trends?days=7');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'trends',
                         'period_statistics',
                         'summary'
                     ]
                 ]);
    }

    public function test_can_get_status_overview()
    {
        WhatsAppSyncLog::factory()->create([
            'status' => SyncStatus::SUCCESS,
            'synced_at' => now()
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/whatsapp/sync/status-overview');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'recent_activity',
                         'statistics',
                         'health_indicators',
                         'recommendations'
                     ]
                 ]);
    }

    public function test_can_trigger_proactive_sync()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/whatsapp/sync/trigger-proactive');

        $response->assertStatus(200)
                 ->assertJsonFragment(['job_dispatched' => true]);
    }

    public function test_cannot_sync_other_organization_message()
    {
        $otherOrg = Organization::factory()->create();
        $otherMessage = Message::factory()->create([
            'organization_id' => $otherOrg->id
        ]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/whatsapp/sync/message/{$otherMessage->id}");

        $response->assertStatus(422);
    }
}
