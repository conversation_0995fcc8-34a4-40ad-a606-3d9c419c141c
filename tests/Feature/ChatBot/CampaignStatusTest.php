<?php

namespace Tests\Feature\ChatBot;

use App\Models\Campaign;
use App\Models\CampaignStatusHistory;
use App\Models\User;
use App\Models\Organization;
use App\Enums\CampaignStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CampaignStatusTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::DRAFT
        ]);
    }

    public function test_can_cancel_campaign()
    {
        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/{$this->campaign->id}/cancel", [
                             'reason' => 'Test cancellation'
                         ]);

        $response->assertStatus(200)
                 ->assertJsonFragment(['message' => 'Campaign cancelled successfully']);

        $this->campaign->refresh();
        $this->assertEquals(CampaignStatus::CANCELLED, $this->campaign->status);
        $this->assertNotNull($this->campaign->cancelled_at);
    }

    public function test_cannot_cancel_completed_campaign()
    {
        $this->campaign->update(['status' => CampaignStatus::COMPLETED]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/{$this->campaign->id}/cancel");

        $response->assertStatus(422);
    }

    public function test_can_get_status_history()
    {
        // Create some status history
        CampaignStatusHistory::factory()->create([
            'campaign_id' => $this->campaign->id,
            'old_status' => CampaignStatus::DRAFT,
            'new_status' => CampaignStatus::SCHEDULED,
            'user_id' => $this->user->id
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$this->campaign->id}/status-history");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaign_id',
                         'current_status',
                         'history' => [
                             '*' => ['old_status', 'new_status', 'created_at']
                         ]
                     ]
                 ]);
    }

    public function test_can_get_status_timeline()
    {
        // Create status history entries
        CampaignStatusHistory::factory()->create([
            'campaign_id' => $this->campaign->id,
            'old_status' => null,
            'new_status' => CampaignStatus::DRAFT,
            'user_id' => $this->user->id
        ]);

        CampaignStatusHistory::factory()->create([
            'campaign_id' => $this->campaign->id,
            'old_status' => CampaignStatus::DRAFT,
            'new_status' => CampaignStatus::SCHEDULED,
            'user_id' => $this->user->id
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$this->campaign->id}/status-timeline");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'timeline' => [
                             '*' => ['change_direction']
                         ]
                     ]
                 ]);
    }

    public function test_cannot_access_other_organization_campaign()
    {
        $otherOrg = Organization::factory()->create();
        $otherCampaign = Campaign::factory()->create([
            'organization_id' => $otherOrg->id
        ]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/{$otherCampaign->id}/cancel");

        $response->assertStatus(422);
    }
}
