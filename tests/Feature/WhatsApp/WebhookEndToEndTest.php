<?php

namespace Tests\Feature\WhatsApp;

use Tests\TestCase;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\WhatsAppWebhookLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookEndToEndTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_webhook_flow_with_organization_token_and_signature()
    {
        $secret = 'test_webhook_secret_e2e';
        $orgToken = 'org_webhook_token_e2e';

        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $orgToken,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'e2e_phone_123',
            'phone_number' => '+5511999999999',
            'is_active' => true,
        ]);

        // Test verification flow
        $verifyResponse = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $orgToken,
            'hub_challenge' => '**********',
        ]));

        $verifyResponse->assertStatus(200);
        $this->assertEquals(**********, $verifyResponse->json());

        // Test webhook message flow
        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_e2e',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'e2e_phone_123',
                                    'display_phone_number' => '+55 11 99999-9999'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'e2e_msg_123',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Hello E2E Test']
                                    ]
                                ],
                                'contacts' => [
                                    [
                                        'profile' => ['name' => 'E2E User'],
                                        'wa_id' => '+*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $webhookResponse = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $webhookResponse->assertStatus(200);
        $webhookResponse->assertJsonStructure([
            'status',
            'processed',
            'results'
        ]);

        // Verify logs were created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => 'e2e_phone_123',
            'event_type' => 'message',
            'processing_status' => 'pending',
        ]);

        // Verify organization was identified correctly
        $messageLog = WhatsAppWebhookLog::where('event_type', 'message')->first();
        $this->assertEquals($organization->id, $messageLog->organization_id);
        $this->assertEquals('e2e_phone_123', $messageLog->phone_number_id);
    }

    public function test_complete_webhook_flow_with_global_token_fallback()
    {
        $secret = 'test_webhook_secret_global';
        $globalToken = 'global_webhook_token_e2e';

        config([
            'whatsapp.webhook_secret' => $secret,
            'whatsapp.webhook_verify_token' => $globalToken
        ]);

        // Test verification with global token
        $verifyResponse = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $globalToken,
            'hub_challenge' => '9876543210',
        ]));

        $verifyResponse->assertStatus(200);
        $this->assertEquals(9876543210, $verifyResponse->json());

        // Verify global token log
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $log = WhatsAppWebhookLog::where('event_type', 'verification')->first();
        $this->assertEquals('global', $log->webhook_payload['type']);
    }

    public function test_webhook_security_flow_invalid_signature()
    {
        $secret = 'test_webhook_secret_security';
        config(['whatsapp.webhook_secret' => $secret]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => []
        ];

        $payload = json_encode($webhookPayload);
        $invalidSignature = 'sha256=invalid_signature_for_security_test';

        $webhookResponse = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $invalidSignature
        ]);

        $webhookResponse->assertStatus(403);
        $webhookResponse->assertJson(['error' => 'Forbidden']);

        // Verify security log
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'security',
            'processing_status' => 'failed',
            'error_message' => 'Invalid signature',
        ]);
    }

    public function test_webhook_flow_with_inactive_organization()
    {
        $secret = 'test_webhook_secret_inactive';
        $orgToken = 'inactive_org_token';

        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $orgToken,
            'is_active' => false, // Inactive organization
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'inactive_phone_123',
            'is_active' => true,
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_inactive',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'inactive_phone_123'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'inactive_msg_123',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message to inactive org']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $webhookResponse = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $webhookResponse->assertStatus(200);

        // Should process but not find organization (phone number lookup should fail)
        $response = $webhookResponse->json();
        $this->assertEquals('success', $response['status']);
        $this->assertEquals(0, $response['processed']); // No messages processed
    }

    public function test_webhook_flow_with_status_updates()
    {
        $secret = 'test_webhook_secret_status';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'status_phone_123',
            'is_active' => true,
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_status',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'status_phone_123'
                                ],
                                'statuses' => [
                                    [
                                        'id' => 'status_msg_123',
                                        'status' => 'delivered',
                                        'timestamp' => '**********',
                                        'recipient_id' => '+*************'
                                    ],
                                    [
                                        'id' => 'status_msg_124',
                                        'status' => 'read',
                                        'timestamp' => '1234567891',
                                        'recipient_id' => '+*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $webhookResponse = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $webhookResponse->assertStatus(200);

        // Verify status log
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => 'status_phone_123',
            'event_type' => 'status',
            'processing_status' => 'pending',
        ]);
    }
}
