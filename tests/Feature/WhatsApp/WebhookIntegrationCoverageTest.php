<?php

namespace Tests\Feature\WhatsApp;

use Tests\TestCase;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\WhatsAppWebhookLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookIntegrationCoverageTest extends TestCase
{
    use RefreshDatabase;

    public function test_webhook_with_multiple_entries_and_changes()
    {
        $secret = 'test_webhook_secret_multi';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization1 = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $organization2 = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber1 = PhoneNumber::factory()->create([
            'organization_id' => $organization1->id,
            'whatsapp_phone_number_id' => 'multi_phone_1',
            'is_active' => true,
        ]);

        $phoneNumber2 = PhoneNumber::factory()->create([
            'organization_id' => $organization2->id,
            'whatsapp_phone_number_id' => 'multi_phone_2',
            'is_active' => true,
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_1',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'multi_phone_1'],
                                'messages' => [
                                    [
                                        'id' => 'msg_1_1',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message 1 to org 1']
                                    ]
                                ]
                            ]
                        ],
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'multi_phone_1'],
                                'statuses' => [
                                    [
                                        'id' => 'status_1_1',
                                        'status' => 'delivered',
                                        'timestamp' => '**********',
                                        'recipient_id' => '+*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'id' => 'business_2',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'multi_phone_2'],
                                'messages' => [
                                    [
                                        'id' => 'msg_2_1',
                                        'from' => '+5511222222222',
                                        'timestamp' => '1234567892',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message 1 to org 2']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals('success', $responseData['status']);
        $this->assertEquals(3, $responseData['processed']); // 2 messages + 1 status

        // Verify logs for both organizations
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization1->id,
            'phone_number_id' => 'multi_phone_1',
            'event_type' => 'message',
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization1->id,
            'phone_number_id' => 'multi_phone_1',
            'event_type' => 'status',
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization2->id,
            'phone_number_id' => 'multi_phone_2',
            'event_type' => 'message',
        ]);
    }

    public function test_webhook_with_unknown_phone_number_id()
    {
        $secret = 'test_webhook_secret_unknown';
        config(['whatsapp.webhook_secret' => $secret]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_unknown',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'unknown_phone_123'],
                                'messages' => [
                                    [
                                        'id' => 'unknown_msg_1',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message to unknown phone']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals('success', $responseData['status']);
        $this->assertEquals(0, $responseData['processed']); // No messages processed
        $this->assertEmpty($responseData['results']);
    }

    public function test_webhook_with_mixed_valid_and_invalid_changes()
    {
        $secret = 'test_webhook_secret_mixed';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'mixed_phone_123',
            'is_active' => true,
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_mixed',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'mixed_phone_123'],
                                'messages' => [
                                    [
                                        'id' => 'valid_msg_1',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Valid message']
                                    ]
                                ]
                            ]
                        ],
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'unknown_phone_456'],
                                'messages' => [
                                    [
                                        'id' => 'invalid_msg_1',
                                        'from' => '+5511555555555',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message to unknown phone']
                                    ]
                                ]
                            ]
                        ],
                        [
                            'field' => 'other_field',
                            'value' => ['some' => 'data']
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals('success', $responseData['status']);
        $this->assertEquals(1, $responseData['processed']); // Only valid message processed
        $this->assertCount(1, $responseData['results']);
    }

    public function test_webhook_with_suspended_organization()
    {
        $secret = 'test_webhook_secret_suspended';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => true, // Suspended organization
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'suspended_phone_123',
            'is_active' => true,
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_suspended',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'suspended_phone_123'],
                                'messages' => [
                                    [
                                        'id' => 'suspended_msg_1',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Message to suspended org']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        $this->assertEquals('success', $responseData['status']);
        $this->assertEquals(0, $responseData['processed']); // No messages processed
    }

    public function test_webhook_logging_comprehensive_coverage()
    {
        $secret = 'test_webhook_secret_logging';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'logging_phone_123',
            'is_active' => true,
        ]);

        // Test 1: Valid webhook with message
        $messagePayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_logging',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'logging_phone_123'],
                                'messages' => [
                                    [
                                        'id' => 'logging_msg_1',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Logging test message']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Send message webhook
        $payload = json_encode($messagePayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $messagePayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);

        // Test 2: Valid webhook with status
        $statusPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_logging',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => ['phone_number_id' => 'logging_phone_123'],
                                'statuses' => [
                                    [
                                        'id' => 'logging_status_1',
                                        'status' => 'delivered',
                                        'timestamp' => '**********',
                                        'recipient_id' => '+*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Send status webhook
        $statusPayloadJson = json_encode($statusPayload);
        $statusSignature = 'sha256=' . hash_hmac('sha256', $statusPayloadJson, $secret);

        $statusResponse = $this->postJson('/api/whatsapp/webhook', $statusPayload, [
            'X-Hub-Signature-256' => $statusSignature
        ]);

        $statusResponse->assertStatus(200);

        // Verify comprehensive logging
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => 'logging_phone_123',
            'event_type' => 'message',
            'processing_status' => 'pending',
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => 'logging_phone_123',
            'event_type' => 'status',
            'processing_status' => 'pending',
        ]);

        // Verify log content
        $messageLog = WhatsAppWebhookLog::where('event_type', 'message')->first();
        $this->assertArrayHasKey('metadata', $messageLog->webhook_payload);
        $this->assertArrayHasKey('messages', $messageLog->webhook_payload);

        $statusLog = WhatsAppWebhookLog::where('event_type', 'status')->first();
        $this->assertArrayHasKey('metadata', $statusLog->webhook_payload);
        $this->assertArrayHasKey('statuses', $statusLog->webhook_payload);
    }

    public function test_webhook_error_handling_and_logging()
    {
        $secret = 'test_webhook_secret_error';
        config(['whatsapp.webhook_secret' => $secret]);

        // Test invalid payload structure
        $invalidPayload = [
            'invalid' => 'structure'
        ];

        $payload = json_encode($invalidPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', $invalidPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(400);
        $response->assertJson(['error' => 'Invalid webhook data']);

        // Verify error logging
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'other',
            'processing_status' => 'failed',
            'error_message' => 'Invalid webhook data',
        ]);
    }
}
