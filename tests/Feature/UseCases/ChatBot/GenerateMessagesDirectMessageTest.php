<?php

namespace Tests\Feature\UseCases\ChatBot;

use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Domains\Inventory\Client;
use App\Domains\Inventory\Organization;
use App\Domains\Inventory\User;
use App\Factories\ChatBot\CampaignFactory;
use App\Factories\ChatBot\ComponentFactory;
use App\Factories\ChatBot\MessageFactory;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Factories\ChatBot\TemplateFactory;
use App\Factories\Inventory\ClientFactory;
use App\Factories\Inventory\OrganizationFactory;
use App\Factories\Inventory\UserFactory;
use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use App\UseCases\ChatBot\Campaign\GenerateMessages;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GenerateMessagesDirectMessageTest extends TestCase
{
    use RefreshDatabase;

    private GenerateMessages $useCase;
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->campaignRepository = app(CampaignRepository::class);
        $this->messageRepository = app(MessageRepository::class);
        $this->messageFactory = app(MessageFactory::class);
        $this->useCase = new GenerateMessages(
            $this->campaignRepository,
            $this->messageRepository,
            $this->messageFactory
        );
    }

    public function test_generate_messages_with_direct_message_parameter()
    {
        // Create test data
        $organization = $this->createOrganization();
        $user = $this->createUser($organization);
        $phoneNumber = $this->createPhoneNumber($organization);
        $template = $this->createTemplate($organization, $phoneNumber);
        $client = $this->createClient($organization);
        
        // Create campaign with is_direct_message = false
        $campaign = $this->createCampaign($organization, $user, $template, $phoneNumber, false);
        
        // Add client to campaign
        $campaign->clients = [$client];
        $this->campaignRepository->save($campaign, $organization->id);
        $this->campaignRepository->addClients($campaign->id, [$client->id]);

        // Act as the user
        $this->actingAs(\App\Models\User::factory()->create([
            'organization_id' => $organization->id
        ]));

        // Generate messages with is_direct_message = true
        $messages = $this->useCase->perform($campaign->id, false, true);

        // Assertions
        $this->assertCount(1, $messages);
        $this->assertTrue($messages[0]->is_direct_message);
    }

    public function test_generate_messages_with_campaign_direct_message_flag()
    {
        // Create test data
        $organization = $this->createOrganization();
        $user = $this->createUser($organization);
        $phoneNumber = $this->createPhoneNumber($organization);
        $template = $this->createTemplate($organization, $phoneNumber);
        $client = $this->createClient($organization);
        
        // Create campaign with is_direct_message = true
        $campaign = $this->createCampaign($organization, $user, $template, $phoneNumber, true);
        
        // Add client to campaign
        $campaign->clients = [$client];
        $this->campaignRepository->save($campaign, $organization->id);
        $this->campaignRepository->addClients($campaign->id, [$client->id]);

        // Act as the user
        $this->actingAs(\App\Models\User::factory()->create([
            'organization_id' => $organization->id
        ]));

        // Generate messages without direct message parameter
        $messages = $this->useCase->perform($campaign->id, false, false);

        // Assertions
        $this->assertCount(1, $messages);
        $this->assertTrue($messages[0]->is_direct_message);
    }

    public function test_message_to_whatsapp_direct_payload()
    {
        // Create test data
        $organization = $this->createOrganization();
        $phoneNumber = $this->createPhoneNumber($organization);
        $template = $this->createTemplateWithComponents($organization, $phoneNumber);
        $client = $this->createClient($organization);
        
        // Create message with direct message flag
        $message = new Message(
            1,
            $organization->id,
            1,
            $template->id,
            $client->id,
            'Test message',
            null,
            false,
            false,
            false,
            true, // is_direct_message = true
            null,
            null,
            null,
            null,
            $client,
            $template,
            null
        );

        // Test the direct payload generation
        $payload = $message->toWhatsAppTemplatePayload();

        // Assertions
        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($client->internationalPhone(), $payload['to']);
        $this->assertEquals('interactive', $payload['type']);
        $this->assertArrayHasKey('interactive', $payload);
        $this->assertEquals('button', $payload['interactive']['type']);
        $this->assertArrayHasKey('body', $payload['interactive']);
    }

    private function createOrganization(): Organization
    {
        $factory = app(OrganizationFactory::class);
        $organization = $factory->buildFromArray([
            'name' => 'Test Organization',
            'email' => '<EMAIL>'
        ]);
        
        $model = \App\Models\Organization::create($organization->toStoreArray());
        $organization->id = $model->id;
        
        return $organization;
    }

    private function createUser(Organization $organization): User
    {
        $factory = app(UserFactory::class);
        $user = $factory->buildFromArray([
            'organization_id' => $organization->id,
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
        
        $model = \App\Models\User::create($user->toStoreArray());
        $user->id = $model->id;
        
        return $user;
    }

    private function createPhoneNumber(Organization $organization): PhoneNumber
    {
        $factory = app(PhoneNumberFactory::class);
        $phoneNumber = $factory->buildFromArray([
            'organization_id' => $organization->id,
            'phone' => '+5511999999999',
            'whatsapp_phone_number_id' => 'test_phone_id',
            'whatsapp_access_token' => 'test_token'
        ]);
        
        $model = \App\Models\PhoneNumber::create($phoneNumber->toStoreArray());
        $phoneNumber->id = $model->id;
        
        return $phoneNumber;
    }

    private function createTemplate(Organization $organization, PhoneNumber $phoneNumber): Template
    {
        $factory = app(TemplateFactory::class);
        $template = $factory->buildFromArray([
            'organization_id' => $organization->id,
            'phone_number_id' => $phoneNumber->id,
            'name' => 'test_template',
            'language' => 'pt_BR',
            'category' => 'MARKETING'
        ]);
        
        $model = \App\Models\Template::create($template->toStoreArray());
        $template->id = $model->id;
        $template->phone_number = $phoneNumber;
        
        return $template;
    }

    private function createTemplateWithComponents(Organization $organization, PhoneNumber $phoneNumber): Template
    {
        $template = $this->createTemplate($organization, $phoneNumber);
        
        // Create components
        $componentFactory = app(ComponentFactory::class);
        
        $bodyComponent = $componentFactory->buildFromArray([
            'template_id' => $template->id,
            'type' => 'BODY',
            'text' => 'Hello {{client.name}}! This is a test message.',
            'index' => 0
        ]);
        
        $model = \App\Models\Component::create($bodyComponent->toStoreArray());
        $bodyComponent->id = $model->id;
        
        $template->components = [$bodyComponent];
        
        return $template;
    }

    private function createClient(Organization $organization): Client
    {
        $factory = app(ClientFactory::class);
        $client = $factory->buildFromArray([
            'organization_id' => $organization->id,
            'name' => 'Test Client',
            'phone' => '+5511888888888'
        ]);
        
        $model = \App\Models\Client::create($client->toStoreArray());
        $client->id = $model->id;
        
        return $client;
    }

    private function createCampaign(Organization $organization, User $user, Template $template, PhoneNumber $phoneNumber, bool $isDirectMessage): Campaign
    {
        $factory = app(CampaignFactory::class);
        $campaign = $factory->buildFromArray([
            'organization_id' => $organization->id,
            'user_id' => $user->id,
            'template_id' => $template->id,
            'phone_number_id' => $phoneNumber->id,
            'name' => 'Test Campaign',
            'description' => 'Test Description',
            'is_direct_message' => $isDirectMessage
        ]);
        
        $model = \App\Models\Campaign::create($campaign->toStoreArray());
        $campaign->id = $model->id;
        $campaign->template = $template;
        $campaign->phone_number = $phoneNumber;
        
        return $campaign;
    }
}
