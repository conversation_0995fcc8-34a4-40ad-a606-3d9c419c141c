<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\CustomProduct;
use App\Models\Organization;
use App\Models\User;
use App\Models\Project;
use App\Models\Budget;
use App\Models\Client;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CustomProductTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Project $project;
    private Budget $budget;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $this->budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_custom_product()
    {
        $customProductData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test custom product'
        ];

        $response = $this->postJson('/api/custom-products', $customProductData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'budget_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->project->id, $responseData['project_id']);
        $this->assertEquals($this->budget->id, $responseData['budget_id']);
        $this->assertEquals(10, $responseData['quantity']);
        $this->assertEquals(150.50, $responseData['value']);
        $this->assertEquals('Test custom product', $responseData['description']);

        $this->assertDatabaseHas('custom_products', [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test custom product'
        ]);
    }

    public function test_can_get_custom_product_by_id()
    {
        $customProduct = CustomProduct::factory()->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Test custom product'
        ]);

        $response = $this->getJson("/api/custom-products/{$customProduct->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'budget_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($customProduct->id, $responseData['id']);
        $this->assertEquals(5, $responseData['quantity']);
        $this->assertEquals(75.25, $responseData['value']);
        $this->assertEquals('Test custom product', $responseData['description']);
    }

    public function test_can_update_custom_product()
    {
        $customProduct = CustomProduct::factory()->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Original description'
        ]);

        $updateData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/custom-products/{$customProduct->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'budget_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['quantity']);
        $this->assertEquals(225.75, $responseData['value']);
        $this->assertEquals('Updated description', $responseData['description']);

        $this->assertDatabaseHas('custom_products', [
            'id' => $customProduct->id,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Updated description'
        ]);
    }

    public function test_can_delete_custom_product()
    {
        $customProduct = CustomProduct::factory()->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id
        ]);

        $response = $this->deleteJson("/api/custom-products/{$customProduct->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('custom_products', [
            'id' => $customProduct->id
        ]);
    }

    public function test_cannot_get_custom_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherProject = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        
        $customProduct = CustomProduct::factory()->create([
            'project_id' => $otherProject->id
        ]);

        $response = $this->getJson("/api/custom-products/{$customProduct->id}");

        $response->assertStatus(404);
    }

    public function test_can_get_all_custom_products_with_pagination()
    {
        CustomProduct::factory()->count(15)->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id
        ]);

        $response = $this->getJson('/api/custom-products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
    }

    public function test_can_get_custom_products_from_specific_project()
    {
        $project1 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);
        $project2 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        CustomProduct::factory()->count(3)->create(['project_id' => $project1->id]);
        CustomProduct::factory()->count(2)->create(['project_id' => $project2->id]);

        $response = $this->getJson("/api/projects/{$project1->id}/custom-products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        foreach ($responseData as $customProduct) {
            $this->assertEquals($project1->id, $customProduct['project_id']);
        }
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/custom-products', [
            'project_id' => '', // Empty project_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['project_id', 'quantity']);
    }

    public function test_custom_product_with_zero_quantity()
    {
        $customProductData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 0,
            'value' => 0.0,
            'description' => 'Zero quantity custom product'
        ];

        $response = $this->postJson('/api/custom-products', $customProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_custom_product_with_high_quantity()
    {
        $customProductData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 999999,
            'value' => 99999999.99,
            'description' => 'High quantity custom product'
        ];

        $response = $this->postJson('/api/custom-products', $customProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }

    public function test_custom_product_with_decimal_values()
    {
        $customProductData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 33,
            'value' => 123.456789,
            'description' => 'Decimal value custom product'
        ];

        $response = $this->postJson('/api/custom-products', $customProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(33, $responseData['quantity']);
        $this->assertEquals(123.456789, $responseData['value']);
    }

    public function test_custom_products_organization_isolation()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create custom products for both organizations
        CustomProduct::factory()->count(3)->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id
        ]);

        $otherProject = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherBudget = Budget::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        CustomProduct::factory()->count(2)->create([
            'project_id' => $otherProject->id,
            'budget_id' => $otherBudget->id
        ]);

        // Current user should only see their organization's custom products
        $response = $this->getJson('/api/custom-products');
        $responseData = $response->json('data');
        $this->assertEquals(3, $responseData['count']);

        // Switch to other user
        Sanctum::actingAs($otherUser);

        // Other user should only see their organization's custom products
        $response = $this->getJson('/api/custom-products');
        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);
    }

    public function test_custom_products_from_project_with_multiple_products()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        CustomProduct::factory()->create([
            'project_id' => $project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'First custom product'
        ]);
        CustomProduct::factory()->create([
            'project_id' => $project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Second custom product'
        ]);

        $response = $this->getJson("/api/projects/{$project->id}/custom-products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);

        // Verify different custom products
        $descriptions = collect($responseData)->pluck('description')->toArray();
        $this->assertContains('First custom product', $descriptions);
        $this->assertContains('Second custom product', $descriptions);
    }

    public function test_custom_products_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            $customProductData = [
                'project_id' => $this->project->id,
                'budget_id' => $this->budget->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value'],
                'description' => "Custom product with quantity {$testCase['quantity']}"
            ];

            $response = $this->postJson('/api/custom-products', $customProductData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($testCase['quantity'], $responseData['quantity']);
            $this->assertEquals($testCase['value'], $responseData['value']);

            $this->assertDatabaseHas('custom_products', [
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }
    }

    public function test_custom_product_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description for a custom product. ', 10);

        $customProductData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 1,
            'value' => 100.00,
            'description' => $longDescription
        ];

        $response = $this->postJson('/api/custom-products', $customProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($longDescription, $responseData['description']);

        $this->assertDatabaseHas('custom_products', [
            'description' => $longDescription
        ]);
    }

    public function test_empty_project_custom_products_endpoint()
    {
        $emptyProject = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $response = $this->getJson("/api/projects/{$emptyProject->id}/custom-products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertEmpty($responseData);
    }

    public function test_update_custom_product_with_different_values()
    {
        $customProduct = CustomProduct::factory()->create([
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Original custom product'
        ]);

        $updateData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Updated with different values'
        ];

        $response = $this->putJson("/api/custom-products/{$customProduct->id}", $updateData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(50, $responseData['quantity']);
        $this->assertEquals(750.25, $responseData['value']);
        $this->assertEquals('Updated with different values', $responseData['description']);

        $this->assertDatabaseHas('custom_products', [
            'id' => $customProduct->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Updated with different values'
        ]);
    }

    public function test_custom_product_crud_workflow()
    {
        // Create
        $createData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'CRUD workflow custom product'
        ];

        $createResponse = $this->postJson('/api/custom-products', $createData);
        $createResponse->assertStatus(201);
        $customProductId = $createResponse->json('data.id');

        // Read
        $getResponse = $this->getJson("/api/custom-products/{$customProductId}");
        $getResponse->assertStatus(200);
        $this->assertEquals('CRUD workflow custom product', $getResponse->json('data.description'));

        // Update
        $updateData = [
            'project_id' => $this->project->id,
            'budget_id' => $this->budget->id,
            'quantity' => 20,
            'value' => 300.75,
            'description' => 'Updated CRUD workflow custom product'
        ];

        $updateResponse = $this->putJson("/api/custom-products/{$customProductId}", $updateData);
        $updateResponse->assertStatus(200);
        $this->assertEquals('Updated CRUD workflow custom product', $updateResponse->json('data.description'));

        // Delete
        $deleteResponse = $this->deleteJson("/api/custom-products/{$customProductId}");
        $deleteResponse->assertStatus(200);

        // Verify deletion
        $this->assertDatabaseMissing('custom_products', [
            'id' => $customProductId
        ]);
    }

    public function test_custom_product_with_mixed_project_and_budget_relationships()
    {
        $project1 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);
        $project2 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $budget1 = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);
        $budget2 = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        // Create custom products with different project-budget combinations
        $customProduct1Data = [
            'project_id' => $project1->id,
            'budget_id' => $budget1->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Project 1 Budget 1'
        ];

        $customProduct2Data = [
            'project_id' => $project2->id,
            'budget_id' => $budget2->id,
            'quantity' => 20,
            'value' => 300.75,
            'description' => 'Project 2 Budget 2'
        ];

        $response1 = $this->postJson('/api/custom-products', $customProduct1Data);
        $response2 = $this->postJson('/api/custom-products', $customProduct2Data);

        $response1->assertStatus(201);
        $response2->assertStatus(201);

        // Verify both custom products exist
        $allResponse = $this->getJson('/api/custom-products');
        $allData = $allResponse->json('data');
        $this->assertEquals(2, $allData['count']);
    }
}
