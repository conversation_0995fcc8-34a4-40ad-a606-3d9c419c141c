<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Department;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DepartmentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_department()
    {
        $departmentData = [
            'name' => 'Test Department',
            'is_active' => true
        ];

        $response = $this->postJson('/api/departments', $departmentData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Department', $responseData['name']);
        $this->assertTrue($responseData['is_active']);

        $this->assertDatabaseHas('departments', [
            'name' => 'Test Department',
            'organization_id' => $this->organization->id,
            'is_active' => true
        ]);
    }

    public function test_can_create_inactive_department()
    {
        $departmentData = [
            'name' => 'Inactive Department',
            'is_active' => false
        ];

        $response = $this->postJson('/api/departments', $departmentData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertFalse($responseData['is_active']);

        $this->assertDatabaseHas('departments', [
            'name' => 'Inactive Department',
            'is_active' => false
        ]);
    }

    public function test_cannot_create_department_without_name()
    {
        $departmentData = [
            'is_active' => true
        ];

        $response = $this->postJson('/api/departments', $departmentData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_can_get_department_by_id()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Get Department',
            'is_active' => true
        ]);

        $response = $this->getJson("/api/departments/{$department->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($department->id, $responseData['id']);
        $this->assertEquals('Get Department', $responseData['name']);
        $this->assertTrue($responseData['is_active']);
    }

    public function test_cannot_get_department_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $department = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/departments/{$department->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This department don't belong to this organization."
                ]);
    }

    public function test_can_update_department()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Department',
            'is_active' => true
        ]);

        $updateData = [
            'name' => 'Updated Department',
            'is_active' => false
        ];

        $response = $this->putJson("/api/departments/{$department->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'Updated Department',
            'is_active' => false,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Department', $responseData['name']);
        $this->assertFalse($responseData['is_active']);
    }

    public function test_can_update_department_partially()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name',
            'is_active' => true
        ]);

        $updateData = [
            'name' => 'Partially Updated'
        ];

        $response = $this->putJson("/api/departments/{$department->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'Partially Updated',
            'is_active' => true // Should remain unchanged
        ]);
    }

    public function test_cannot_update_department_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $department = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Unauthorized Update'
        ];

        $response = $this->putJson("/api/departments/{$department->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error'
                ]);
    }

    public function test_cannot_update_department_without_name()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $updateData = [
            'name' => ''
        ];

        $response = $this->putJson("/api/departments/{$department->id}", $updateData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_can_delete_department()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/departments/{$department->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('departments', [
            'id' => $department->id
        ]);
    }

    public function test_cannot_delete_department_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $department = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/departments/{$department->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This department don't belong to this organization."
                ]);
    }

    public function test_can_get_all_departments_with_pagination()
    {
        Department::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/departments?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'organization_id',
                            'name',
                            'is_active',
                            'created_at',
                            'updated_at'
                        ]
                    ],
                    'meta' => [
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertCount(5, $responseData);

        $meta = $response->json('meta');
        $this->assertEquals(5, $meta['count']);
        $this->assertEquals(15, $meta['total']);
    }

    public function test_can_filter_departments_by_name()
    {
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Department'
        ]);
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Department'
        ]);

        $response = $this->getJson('/api/departments?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Alpha Department', $responseData[0]['name']);
    }

    public function test_can_filter_departments_by_is_active()
    {
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Department',
            'is_active' => true
        ]);
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Department',
            'is_active' => false
        ]);

        $response = $this->getJson('/api/departments?is_active=1');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Active Department', $responseData[0]['name']);
        $this->assertTrue($responseData[0]['is_active']);
    }

    public function test_departments_are_organization_scoped()
    {
        $otherOrganization = Organization::factory()->create();

        // Create departments for current organization
        Department::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);

        // Create departments for other organization
        Department::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson('/api/departments');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        // Verify all returned departments belong to the current organization
        foreach ($responseData as $department) {
            $this->assertEquals($this->organization->id, $department['organization_id']);
        }
    }

    public function test_can_order_departments()
    {
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Zebra Department'
        ]);
        Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Department'
        ]);

        $response = $this->getJson('/api/departments?order=name&by=asc');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals('Alpha Department', $responseData[0]['name']);
        $this->assertEquals('Zebra Department', $responseData[1]['name']);
    }

    public function test_handles_nonexistent_department_gracefully()
    {
        $response = $this->getJson('/api/departments/999');

        $response->assertStatus(404);
    }

    public function test_handles_invalid_department_id_gracefully()
    {
        $response = $this->getJson('/api/departments/invalid');

        $response->assertStatus(404);
    }

    public function test_create_department_assigns_correct_organization()
    {
        $departmentData = [
            'name' => 'Organization Test Department',
            'is_active' => true
        ];

        $response = $this->postJson('/api/departments', $departmentData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);

        // Verify in database
        $this->assertDatabaseHas('departments', [
            'name' => 'Organization Test Department',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_department_maintains_organization()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Department'
        ]);

        $updateData = [
            'name' => 'Updated Department Name'
        ];

        $response = $this->putJson("/api/departments/{$department->id}", $updateData);

        $response->assertStatus(200);

        // Verify organization_id hasn't changed
        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'Updated Department Name',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_api_returns_proper_error_format()
    {
        $response = $this->postJson('/api/departments', []);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'code',
                    'data',
                    'errors' => [
                        'name'
                    ]
                ]);
    }

    public function test_api_returns_proper_success_format()
    {
        $departmentData = [
            'name' => 'Success Format Test',
            'is_active' => true
        ];

        $response = $this->postJson('/api/departments', $departmentData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Department created successfully'
                ]);
    }

    public function test_delete_returns_proper_format()
    {
        $department = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/departments/{$department->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Department deleted successfully'
                ]);
    }
}
