<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\StockEntry;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Shop;
use App\Models\Batch;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class StockEntryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Product $product;
    private Brand $brand;
    private Shop $shop;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        $this->shop = Shop::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_stock_entry()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Test stock entry'
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'user_id',
                        'brand_id',
                        'product_id',
                        'batch_id',
                        'client_id',
                        'project_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals($this->user->id, $responseData['user_id']);
        $this->assertEquals($this->shop->id, $responseData['shop_id']);
        $this->assertEquals($this->brand->id, $responseData['brand_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals(100, $responseData['quantity']);
        $this->assertEquals(1500.50, $responseData['value']);
        $this->assertEquals('Test stock entry', $responseData['description']);

        $this->assertDatabaseHas('stock_entries', [
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Test stock entry'
        ]);
    }

    public function test_can_get_stock_entry_by_id()
    {
        $entry = StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Get entry test'
        ]);

        $response = $this->getJson("/api/stock-entries/{$entry->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'user_id',
                        'brand_id',
                        'product_id',
                        'batch_id',
                        'client_id',
                        'project_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($entry->id, $responseData['id']);
        $this->assertEquals(50, $responseData['quantity']);
        $this->assertEquals(750.25, $responseData['value']);
        $this->assertEquals('Get entry test', $responseData['description']);
    }

    public function test_cannot_get_stock_entry_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $entry = StockEntry::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/stock-entries/{$entry->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_stock_entry()
    {
        $entry = StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Old description'
        ]);

        $updateData = [
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/stock-entries/{$entry->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('stock_entries', [
            'id' => $entry->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'Updated description',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(150, $responseData['quantity']);
        $this->assertEquals(2250.75, $responseData['value']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_cannot_update_stock_entry_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $entry = StockEntry::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'quantity' => 200
        ];

        $response = $this->putJson("/api/stock-entries/{$entry->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This stock entry don't belong to this organization."
                ]);
    }

    public function test_can_delete_stock_entry()
    {
        $entry = StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/stock-entries/{$entry->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('stock_entries', [
            'id' => $entry->id
        ]);
    }

    public function test_cannot_delete_stock_entry_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $entry = StockEntry::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/stock-entries/{$entry->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This stock entry don't belong to this organization."
                ]);
    }

    public function test_can_get_all_stock_entries_with_pagination()
    {
        StockEntry::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/stock-entries?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_stock_entries_by_quantity()
    {
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100
        ]);
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 200
        ]);

        $response = $this->getJson('/api/stock-entries?quantity=100');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals(100, $responseData['data'][0]['quantity']);
    }

    public function test_can_filter_stock_entries_by_value()
    {
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'value' => 1500.50
        ]);
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'value' => 2500.75
        ]);

        $response = $this->getJson('/api/stock-entries?value=1500.50');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals(1500.50, $responseData['data'][0]['value']);
    }

    public function test_can_filter_stock_entries_by_description()
    {
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Alpha entry'
        ]);
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Beta entry'
        ]);

        $response = $this->getJson('/api/stock-entries?description=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha entry', $responseData['data'][0]['description']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/stock-entries', [
            'shop_id' => '', // Empty shop_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['shop_id', 'quantity']);
    }

    public function test_validation_errors_on_update()
    {
        $entry = StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->putJson("/api/stock-entries/{$entry->id}", [
            'quantity' => 'invalid', // Invalid quantity should fail validation
            'value' => 'invalid' // Invalid value should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['quantity', 'value']);
    }

    public function test_stock_entry_soft_delete_behavior()
    {
        $entry = StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Soft delete entry'
        ]);

        // Delete the entry
        $response = $this->deleteJson("/api/stock-entries/{$entry->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/stock-entries');
        $responseData = $response->json('data.data');
        $entryIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($entry->id, $entryIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/stock-entries/{$entry->id}");
        $response->assertStatus(404);
    }

    public function test_stock_entry_ordering_and_filtering_combinations()
    {
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'description' => 'Alpha entry',
            'created_at' => now()->subDays(3)
        ]);
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 200,
            'description' => 'Beta entry',
            'created_at' => now()->subDays(1)
        ]);
        StockEntry::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 150,
            'description' => 'Charlie entry',
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by quantity ascending
        $response = $this->getJson('/api/stock-entries?order=quantity&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(100, $responseData[0]['quantity']);
        $this->assertEquals(150, $responseData[1]['quantity']);
        $this->assertEquals(200, $responseData[2]['quantity']);

        // Test ordering by quantity descending
        $response = $this->getJson('/api/stock-entries?order=quantity&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals(200, $responseData[0]['quantity']);
        $this->assertEquals(150, $responseData[1]['quantity']);
        $this->assertEquals(100, $responseData[2]['quantity']);

        // Test filtering with ordering
        $response = $this->getJson('/api/stock-entries?description=a&order=quantity&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals(100, $responseData[0]['quantity']); // Alpha
        $this->assertEquals(150, $responseData[1]['quantity']); // Charlie
    }

    public function test_stock_entry_with_special_characters()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Entry with special chars: @#$%^&*()'
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Entry with special chars: @#$%^&*()', $responseData['description']);

        $this->assertDatabaseHas('stock_entries', [
            'description' => 'Entry with special chars: @#$%^&*()'
        ]);
    }

    public function test_stock_entry_with_unicode_characters()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 75,
            'value' => 1125.75,
            'description' => 'Entrada con caracteres especiales: ñáéíóú 中文'
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Entrada con caracteres especiales: ñáéíóú 中文', $responseData['description']);

        $this->assertDatabaseHas('stock_entries', [
            'description' => 'Entrada con caracteres especiales: ñáéíóú 中文'
        ]);
    }

    public function test_stock_entry_with_null_description()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 25,
            'value' => 375.25,
            'description' => null
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertNull($responseData['description']);
    }

    public function test_stock_entry_with_zero_quantity()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 0,
            'value' => 0.0,
            'description' => 'Zero quantity entry'
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_stock_entry_with_high_quantity()
    {
        $entryData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 999999,
            'value' => 99999999.99,
            'description' => 'High quantity entry'
        ];

        $response = $this->postJson('/api/stock-entries', $entryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }
}
