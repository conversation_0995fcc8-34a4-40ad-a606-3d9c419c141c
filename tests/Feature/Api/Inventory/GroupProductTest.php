<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\GroupProduct;
use App\Models\Group;
use App\Models\Product;
use App\Models\Organization;
use App\Models\User;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class GroupProductTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Group $group;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->group = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_group_product()
    {
        $groupProductData = [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'group_id',
                        'product_id',
                        'created_at',
                        'updated_at',
                        'group',
                        'product'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->group->id, $responseData['group_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);

        $this->assertDatabaseHas('group_products', [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);
    }

    public function test_cannot_create_group_product_without_group_id()
    {
        $groupProductData = [
            'product_id' => $this->product->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['group_id']);
    }

    public function test_cannot_create_group_product_without_product_id()
    {
        $groupProductData = [
            'group_id' => $this->group->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['product_id']);
    }

    public function test_can_get_group_product_by_id()
    {
        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'group_id',
                        'product_id',
                        'created_at',
                        'updated_at',
                        'group',
                        'product'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($groupProduct->id, $responseData['id']);
        $this->assertEquals($this->group->id, $responseData['group_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
    }

    public function test_cannot_get_group_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherGroup = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $otherGroup->id,
            'product_id' => $otherProduct->id
        ]);

        $response = $this->getJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This group product assignment doesn't belong to this organization."
                ]);
    }

    public function test_can_update_group_product()
    {
        $group2 = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $brand2 = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand2->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $updateData = [
            'group_id' => $group2->id,
            'product_id' => $product2->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('group_products', [
            'id' => $groupProduct->id,
            'group_id' => $group2->id,
            'product_id' => $product2->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($group2->id, $responseData['group_id']);
        $this->assertEquals($product2->id, $responseData['product_id']);
    }

    public function test_can_update_group_product_partially()
    {
        $group2 = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $updateData = [
            'group_id' => $group2->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('group_products', [
            'id' => $groupProduct->id,
            'group_id' => $group2->id,
            'product_id' => $this->product->id // Should remain unchanged
        ]);
    }

    public function test_cannot_update_group_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherGroup = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $otherGroup->id,
            'product_id' => $otherProduct->id
        ]);

        $updateData = [
            'group_id' => $this->group->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error'
                ]);
    }

    public function test_cannot_update_group_product_with_group_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherGroup = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $updateData = [
            'group_id' => $otherGroup->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "The specified group doesn't belong to this organization."
                ]);
    }

    public function test_cannot_update_group_product_with_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $updateData = [
            'product_id' => $otherProduct->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "The specified product doesn't belong to this organization."
                ]);
    }

    public function test_can_delete_group_product()
    {
        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('group_products', [
            'id' => $groupProduct->id
        ]);
    }

    public function test_cannot_delete_group_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherGroup = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $otherGroup->id,
            'product_id' => $otherProduct->id
        ]);

        $response = $this->deleteJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This group product assignment doesn't belong to this organization."
                ]);
    }

    public function test_can_get_all_group_products_with_pagination()
    {
        $groups = Group::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        $brands = Brand::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);
        $products = Product::factory()->count(5)->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brands->random()->id
        ]);

        foreach ($groups as $group) {
            foreach ($products->take(2) as $product) {
                GroupProduct::factory()->create([
                    'group_id' => $group->id,
                    'product_id' => $product->id
                ]);
            }
        }

        $response = $this->getJson('/api/groups_products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'group_id',
                            'product_id',
                            'created_at',
                            'updated_at',
                            'group',
                            'product'
                        ]
                    ],
                    'meta' => [
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertCount(6, $responseData); // 3 groups * 2 products each

        $meta = $response->json('meta');
        $this->assertEquals(6, $meta['count']);
        $this->assertEquals(6, $meta['total']);
    }

    public function test_group_products_are_organization_scoped()
    {
        $otherOrganization = Organization::factory()->create();

        // Create group products for current organization
        $currentOrgBrand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $currentOrgProducts = Product::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $currentOrgBrand->id
        ]);
        foreach ($currentOrgProducts as $product) {
            GroupProduct::factory()->create([
                'group_id' => $this->group->id,
                'product_id' => $product->id
            ]);
        }

        // Create group products for other organization
        $otherGroup = Group::factory()->create(['organization_id' => $otherOrganization->id]);
        $otherBrand = Brand::factory()->create(['organization_id' => $otherOrganization->id]);
        $otherProducts = Product::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);
        foreach ($otherProducts as $product) {
            GroupProduct::factory()->create([
                'group_id' => $otherGroup->id,
                'product_id' => $product->id
            ]);
        }

        $response = $this->getJson('/api/groups_products');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        // Verify all returned group products belong to the current organization (through product)
        foreach ($responseData as $groupProduct) {
            $this->assertEquals($this->group->id, $groupProduct['group_id']);
        }
    }

    public function test_handles_nonexistent_group_product_gracefully()
    {
        $response = $this->getJson('/api/groups_products/999');

        $response->assertStatus(404);
    }

    public function test_handles_invalid_group_product_id_gracefully()
    {
        $response = $this->getJson('/api/groups_products/invalid');

        $response->assertStatus(404);
    }

    public function test_api_returns_proper_error_format()
    {
        $response = $this->postJson('/api/groups_products', []);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'code',
                    'data',
                    'errors' => [
                        'group_id',
                        'product_id'
                    ]
                ]);
    }

    public function test_api_returns_proper_success_format()
    {
        $groupProductData = [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'GroupProduct created successfully'
                ]);
    }

    public function test_update_returns_proper_format()
    {
        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $updateData = [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ];

        $response = $this->putJson("/api/groups_products/{$groupProduct->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'GroupProduct updated successfully'
                ]);
    }

    public function test_delete_returns_proper_format()
    {
        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'GroupProduct deleted successfully'
                ]);
    }

    public function test_can_assign_same_product_to_multiple_groups()
    {
        $group2 = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Assign product to first group
        $groupProductData1 = [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ];
        $response1 = $this->postJson('/api/groups_products', $groupProductData1);
        $response1->assertStatus(200);

        // Assign same product to second group
        $groupProductData2 = [
            'group_id' => $group2->id,
            'product_id' => $this->product->id
        ];
        $response2 = $this->postJson('/api/groups_products', $groupProductData2);
        $response2->assertStatus(200);

        // Verify both assignments exist
        $this->assertDatabaseHas('group_products', [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);
        $this->assertDatabaseHas('group_products', [
            'group_id' => $group2->id,
            'product_id' => $this->product->id
        ]);
    }

    public function test_can_assign_multiple_products_to_same_group()
    {
        $brand2 = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand2->id
        ]);

        // Assign first product to group
        $groupProductData1 = [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ];
        $response1 = $this->postJson('/api/groups_products', $groupProductData1);
        $response1->assertStatus(200);

        // Assign second product to same group
        $groupProductData2 = [
            'group_id' => $this->group->id,
            'product_id' => $product2->id
        ];
        $response2 = $this->postJson('/api/groups_products', $groupProductData2);
        $response2->assertStatus(200);

        // Verify both assignments exist
        $this->assertDatabaseHas('group_products', [
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);
        $this->assertDatabaseHas('group_products', [
            'group_id' => $this->group->id,
            'product_id' => $product2->id
        ]);
    }

    public function test_cannot_create_group_product_with_group_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherGroup = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $groupProductData = [
            'group_id' => $otherGroup->id,
            'product_id' => $this->product->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(403);
    }

    public function test_cannot_create_group_product_with_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        $groupProductData = [
            'group_id' => $this->group->id,
            'product_id' => $otherProduct->id
        ];

        $response = $this->postJson('/api/groups_products', $groupProductData);

        $response->assertStatus(403);
    }

    public function test_pagination_works_correctly()
    {
        $brands = Brand::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        $products = Product::factory()->count(7)->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brands->random()->id
        ]);

        foreach ($products as $product) {
            GroupProduct::factory()->create([
                'group_id' => $this->group->id,
                'product_id' => $product->id
            ]);
        }

        $response = $this->getJson('/api/groups_products');

        $response->assertStatus(200);

        $meta = $response->json('meta');
        $this->assertEquals(7, $meta['count']);
        $this->assertEquals(7, $meta['total']);
        $this->assertEquals(1, $meta['currentPage']);
        $this->assertEquals(1, $meta['lastPage']);
    }

    public function test_empty_results_handled_correctly()
    {
        $response = $this->getJson('/api/groups_products');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEmpty($responseData);

        $meta = $response->json('meta');
        $this->assertEquals(0, $meta['count']);
        $this->assertEquals(0, $meta['total']);
    }

    public function test_hard_delete_behavior()
    {
        $groupProduct = GroupProduct::factory()->create([
            'group_id' => $this->group->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/groups_products/{$groupProduct->id}");

        $response->assertStatus(200);

        // Verify it was hard deleted (not soft deleted)
        $this->assertDatabaseMissing('group_products', [
            'id' => $groupProduct->id
        ]);

        // Verify it's not in the database at all (including deleted records)
        $this->assertNull(GroupProduct::withTrashed()->find($groupProduct->id));
    }
}
