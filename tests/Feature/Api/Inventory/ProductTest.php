<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Brand;
use App\Models\Organization;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ProductTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_product()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);

        $productData = [
            'brand_id' => $brand->id,
            'name' => 'Test Product',
            'barcode' => '1234567890123',
            'description' => 'Test product description',
            'price' => 99.99,
            'unity' => 1
        ];

        $response = $this->postJson('/api/products', $productData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'brand_id',
                        'name',
                        'barcode',
                        'description',
                        'price',
                        'unity',
                        'last_priced_at',
                        'created_at',
                        'updated_at',
                        'brand'
                    ]
                ]);

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id,
            'barcode' => '1234567890123',
            'price' => 99.99,
            'unity' => 1
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Product', $responseData['name']);
        $this->assertEquals($brand->id, $responseData['brand_id']);
        $this->assertNotNull($responseData['last_priced_at']);
    }

    public function test_can_create_minimal_product()
    {
        $productData = [
            'name' => 'Minimal Product'
        ];

        $response = $this->postJson('/api/products', $productData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('products', [
            'name' => 'Minimal Product',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Minimal Product', $responseData['name']);
        $this->assertNull($responseData['brand_id']);
        $this->assertNull($responseData['barcode']);
        $this->assertNull($responseData['price']);
        $this->assertNull($responseData['unity']);
        $this->assertNotNull($responseData['last_priced_at']); // Should be set automatically
    }

    public function test_can_get_product_by_id()
    {
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Product'
        ]);

        $response = $this->getJson("/api/products/{$product->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'barcode',
                        'description',
                        'price',
                        'unity',
                        'last_priced_at',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($product->id, $responseData['id']);
        $this->assertEquals('Test Product', $responseData['name']);
    }

    public function test_cannot_get_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $product = Product::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/products/{$product->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_product()
    {
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'price' => 149.99,
            'unity' => 2
        ];

        $response = $this->putJson("/api/products/{$product->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'price' => 149.99,
            'unity' => 2,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Name', $responseData['name']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_cannot_update_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $product = Product::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Updated Name'
        ];

        $response = $this->putJson("/api/products/{$product->id}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_delete_product()
    {
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/products/{$product->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('products', [
            'id' => $product->id
        ]);
    }

    public function test_cannot_delete_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $product = Product::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/products/{$product->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This product don't belong to this organization."
                ]);
    }

    public function test_can_get_all_products_with_pagination()
    {
        Product::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/products?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_products_by_name()
    {
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Product'
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Product'
        ]);

        $response = $this->getJson('/api/products?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Product', $responseData['data'][0]['name']);
    }

    public function test_can_filter_products_by_barcode()
    {
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'barcode' => '1234567890123'
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'barcode' => '9876543210987'
        ]);

        $response = $this->getJson('/api/products?barcode=1234567890123');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('1234567890123', $responseData['data'][0]['barcode']);
    }

    public function test_can_get_products_with_brand_relationship()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $response = $this->getJson('/api/products?with_brand=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['brand']);
        $this->assertEquals($brand->name, $responseData[0]['brand']['name']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/products', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_validation_errors_on_update()
    {
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/products/{$product->id}", [
            'name' => '' // Empty name should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_product_creation_with_zero_price()
    {
        $productData = [
            'name' => 'Free Product',
            'description' => 'Product with zero price',
            'price' => 0.0,
            'unity' => 1
        ];

        $response = $this->postJson('/api/products', $productData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('products', [
            'name' => 'Free Product',
            'price' => 0.0
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(0.0, $responseData['price']);
    }

    public function test_product_creation_with_high_price()
    {
        $productData = [
            'name' => 'Expensive Product',
            'description' => 'Product with high price',
            'price' => 999999.99,
            'unity' => 1
        ];

        $response = $this->postJson('/api/products', $productData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('products', [
            'name' => 'Expensive Product',
            'price' => 999999.99
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(999999.99, $responseData['price']);
    }

    public function test_product_soft_delete_behavior()
    {
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete the product
        $response = $this->deleteJson("/api/products/{$product->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/products');
        $responseData = $response->json('data.data');
        $productNames = collect($responseData)->pluck('name')->toArray();
        $this->assertNotContains('To Be Deleted', $productNames);

        // Should not be accessible by ID
        $response = $this->getJson("/api/products/{$product->id}");
        $response->assertStatus(404);
    }

    public function test_product_ordering_and_filtering_combinations()
    {
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Product',
            'price' => 100.00,
            'created_at' => now()->subDays(3)
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Product',
            'price' => 200.00,
            'created_at' => now()->subDays(1)
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Product',
            'price' => 300.00,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/products?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alpha Product', $responseData[0]['name']);
        $this->assertEquals('Beta Product', $responseData[1]['name']);
        $this->assertEquals('Charlie Product', $responseData[2]['name']);

        // Test ordering by price descending
        $response = $this->getJson('/api/products?order=price&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Charlie Product', $responseData[0]['name']); // Highest price

        // Test filtering with ordering
        $response = $this->getJson('/api/products?name=a&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals('Alpha Product', $responseData[0]['name']);
        $this->assertEquals('Charlie Product', $responseData[1]['name']);
    }

    public function test_product_data_consistency_across_operations()
    {
        // Create product
        $productData = [
            'name' => 'Consistency Test',
            'description' => 'Test product consistency',
            'price' => 199.99,
            'unity' => 2
        ];

        $createResponse = $this->postJson('/api/products', $productData);
        $createResponse->assertStatus(201);
        $productId = $createResponse->json('data.id');

        // Get product and verify data
        $getResponse = $this->getJson("/api/products/{$productId}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals($productData['name'], $getData['name']);
        $this->assertEquals($productData['description'], $getData['description']);
        $this->assertEquals($productData['price'], $getData['price']);
        $this->assertEquals($productData['unity'], $getData['unity']);

        // Update product
        $updateData = [
            'name' => 'Updated Consistency Test',
            'description' => 'Updated description',
            'price' => 299.99
        ];

        $updateResponse = $this->putJson("/api/products/{$productId}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/products/{$productId}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals($updateData['name'], $getUpdatedData['name']);
        $this->assertEquals($updateData['description'], $getUpdatedData['description']);
        $this->assertEquals($updateData['price'], $getUpdatedData['price']);
        // Unity should remain unchanged
        $this->assertEquals($productData['unity'], $getUpdatedData['unity']);
    }

    public function test_product_with_different_unity_values()
    {
        $unityValues = [1, 2, 3];

        foreach ($unityValues as $unity) {
            $productData = [
                'name' => "Product Unity $unity",
                'description' => "Product with unity $unity",
                'price' => 99.99,
                'unity' => $unity
            ];

            $response = $this->postJson('/api/products', $productData);
            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($unity, $responseData['unity']);

            $this->assertDatabaseHas('products', [
                'name' => "Product Unity $unity",
                'unity' => $unity
            ]);
        }
    }

    public function test_product_barcode_variations()
    {
        $barcodes = [
            '123456789012',      // 12 digits
            '1234567890123',     // 13 digits (EAN-13)
            '12345678901234',    // 14 digits
            null                 // No barcode
        ];

        foreach ($barcodes as $index => $barcode) {
            $productData = [
                'name' => "Product Barcode $index",
                'barcode' => $barcode,
                'price' => 99.99,
                'unity' => 1
            ];

            $response = $this->postJson('/api/products', $productData);
            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($barcode, $responseData['barcode']);

            $this->assertDatabaseHas('products', [
                'name' => "Product Barcode $index",
                'barcode' => $barcode
            ]);
        }
    }

    public function test_product_price_range_filtering()
    {
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Cheap Product',
            'price' => 10.00
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Medium Product',
            'price' => 50.00
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Expensive Product',
            'price' => 100.00
        ]);

        // Test minimum price filter
        $response = $this->getJson('/api/products?price_min=25');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Medium and Expensive

        // Test maximum price filter
        $response = $this->getJson('/api/products?price_max=75');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Cheap and Medium

        // Test price range filter
        $response = $this->getJson('/api/products?price_min=25&price_max=75');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only Medium
        $this->assertEquals('Medium Product', $responseData[0]['name']);
    }

    public function test_product_brand_filtering()
    {
        $brand1 = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $brand2 = Brand::factory()->create(['organization_id' => $this->organization->id]);

        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand1->id,
            'name' => 'Brand 1 Product'
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand2->id,
            'name' => 'Brand 2 Product'
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => null,
            'name' => 'No Brand Product'
        ]);

        // Filter by brand 1
        $response = $this->getJson("/api/products?brand_id={$brand1->id}");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Brand 1 Product', $responseData[0]['name']);

        // Filter by brand 2
        $response = $this->getJson("/api/products?brand_id={$brand2->id}");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Brand 2 Product', $responseData[0]['name']);
    }

    public function test_product_unity_filtering()
    {
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Unity 1 Product',
            'unity' => 1
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Unity 2 Product',
            'unity' => 2
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Unity 3 Product',
            'unity' => 3
        ]);

        // Filter by unity 2
        $response = $this->getJson('/api/products?unity=2');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Unity 2 Product', $responseData[0]['name']);
        $this->assertEquals(2, $responseData[0]['unity']);
    }

    public function test_product_complex_filtering_combinations()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);

        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id,
            'name' => 'Alpha Expensive Product',
            'price' => 200.00,
            'unity' => 1
        ]);
        Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id,
            'name' => 'Beta Cheap Product',
            'price' => 50.00,
            'unity' => 2
        ]);

        // Complex filter: brand + name + price range + unity
        $response = $this->getJson("/api/products?brand_id={$brand->id}&name=Alpha&price_min=100&unity=1");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Alpha Expensive Product', $responseData[0]['name']);
    }
}
