<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Brand;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Shop;
use App\Models\Stock;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class StockTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_get_stock_by_id()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100,
            'value' => 1000.0
        ]);

        $response = $this->getJson("/api/stocks/{$stock->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'brand_id',
                        'product_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($stock->id, $responseData['id']);
        $this->assertEquals(100, $responseData['quantity']);
        $this->assertEquals(1000.0, $responseData['value']);
    }

    public function test_cannot_get_stock_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $stock = Stock::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/stocks/{$stock->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_stock()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50,
            'value' => 500.0
        ]);

        $updateData = [
            'quantity' => 150,
            'value' => 1500.0,
            'description' => 'Updated stock'
        ];

        $response = $this->putJson("/api/stocks/{$stock->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('stocks', [
            'id' => $stock->id,
            'quantity' => 150,
            'value' => 1500.0,
            'description' => 'Updated stock',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(150, $responseData['quantity']);
        $this->assertEquals(1500.0, $responseData['value']);
        $this->assertEquals('Updated stock', $responseData['description']);
    }

    public function test_cannot_update_stock_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $stock = Stock::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'quantity' => 100
        ];

        $response = $this->putJson("/api/stocks/{$stock->id}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_delete_stock()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/stocks/{$stock->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('stocks', [
            'id' => $stock->id
        ]);
    }

    public function test_cannot_delete_stock_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $stock = Stock::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/stocks/{$stock->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This stock don't belong to this organization."
                ]);
    }

    public function test_can_get_all_stocks_with_pagination()
    {
        Stock::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/stocks?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_stocks_by_product()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'quantity' => 100
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'quantity' => 50
        ]);

        $response = $this->getJson("/api/stocks?product_id={$product1->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($product1->id, $responseData['data'][0]['product_id']);
        $this->assertEquals(100, $responseData['data'][0]['quantity']);
    }

    public function test_can_filter_stocks_by_shop()
    {
        $shop1 = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $shop2 = Shop::factory()->create(['organization_id' => $this->organization->id]);

        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop1->id,
            'quantity' => 75
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop2->id,
            'quantity' => 25
        ]);

        $response = $this->getJson("/api/stocks?shop_id={$shop1->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($shop1->id, $responseData['data'][0]['shop_id']);
        $this->assertEquals(75, $responseData['data'][0]['quantity']);
    }

    public function test_can_get_stocks_with_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $response = $this->getJson('/api/stocks?with_shop=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['shop']);
        $this->assertEquals($shop->name, $responseData[0]['shop']['name']);
    }

    public function test_validation_errors_on_update()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/stocks/{$stock->id}", [
            'quantity' => -1 // Negative quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['quantity']);
    }

    public function test_stock_update_with_zero_quantity()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);

        $updateData = [
            'quantity' => 0,
            'value' => 0.0,
            'description' => 'Empty stock'
        ];

        $response = $this->putJson("/api/stocks/{$stock->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('stocks', [
            'id' => $stock->id,
            'quantity' => 0,
            'value' => 0.0
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_stock_update_with_high_quantity()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);

        $updateData = [
            'quantity' => 999999,
            'value' => 9999999.99,
            'description' => 'Large stock'
        ];

        $response = $this->putJson("/api/stocks/{$stock->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('stocks', [
            'id' => $stock->id,
            'quantity' => 999999,
            'value' => 9999999.99
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(9999999.99, $responseData['value']);
    }

    public function test_stock_soft_delete_behavior()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50
        ]);

        // Delete the stock
        $response = $this->deleteJson("/api/stocks/{$stock->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/stocks');
        $responseData = $response->json('data.data');
        $stockIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($stock->id, $stockIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/stocks/{$stock->id}");
        $response->assertStatus(404);
    }

    public function test_stock_ordering_and_filtering_combinations()
    {
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100,
            'value' => 1000.0,
            'created_at' => now()->subDays(3)
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50,
            'value' => 500.0,
            'created_at' => now()->subDays(1)
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200,
            'value' => 2000.0,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by quantity ascending
        $response = $this->getJson('/api/stocks?order=quantity&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(50, $responseData[0]['quantity']);
        $this->assertEquals(100, $responseData[1]['quantity']);
        $this->assertEquals(200, $responseData[2]['quantity']);

        // Test ordering by value descending
        $response = $this->getJson('/api/stocks?order=value&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2000.0, $responseData[0]['value']); // Highest value
    }

    public function test_stock_data_consistency_across_operations()
    {
        $stock = Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100,
            'value' => 1000.0,
            'description' => 'Original stock'
        ]);

        // Get stock and verify data
        $getResponse = $this->getJson("/api/stocks/{$stock->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals(100, $getData['quantity']);
        $this->assertEquals(1000.0, $getData['value']);
        $this->assertEquals('Original stock', $getData['description']);

        // Update stock
        $updateData = [
            'quantity' => 150,
            'value' => 1500.0,
            'description' => 'Updated stock'
        ];

        $updateResponse = $this->putJson("/api/stocks/{$stock->id}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/stocks/{$stock->id}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals(150, $getUpdatedData['quantity']);
        $this->assertEquals(1500.0, $getUpdatedData['value']);
        $this->assertEquals('Updated stock', $getUpdatedData['description']);
    }

    public function test_stock_quantity_range_filtering()
    {
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 10
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);

        // Test minimum quantity filter
        $response = $this->getJson('/api/stocks?quantity_min=25');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // 50 and 100

        // Test maximum quantity filter
        $response = $this->getJson('/api/stocks?quantity_max=75');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // 10 and 50

        // Test quantity range filter
        $response = $this->getJson('/api/stocks?quantity_min=25&quantity_max=75');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only 50
        $this->assertEquals(50, $responseData[0]['quantity']);
    }

    public function test_stock_value_range_filtering()
    {
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 100.0
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 500.0
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 1000.0
        ]);

        // Test minimum value filter
        $response = $this->getJson('/api/stocks?value_min=250');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // 500 and 1000

        // Test maximum value filter
        $response = $this->getJson('/api/stocks?value_max=750');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // 100 and 500

        // Test value range filter
        $response = $this->getJson('/api/stocks?value_min=250&value_max=750');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only 500
        $this->assertEquals(500.0, $responseData[0]['value']);
    }

    public function test_stock_brand_filtering()
    {
        $brand1 = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $brand2 = Brand::factory()->create(['organization_id' => $this->organization->id]);

        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand1->id,
            'quantity' => 100
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand2->id,
            'quantity' => 50
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => null,
            'quantity' => 25
        ]);

        // Filter by brand 1
        $response = $this->getJson("/api/stocks?brand_id={$brand1->id}");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($brand1->id, $responseData[0]['brand_id']);
        $this->assertEquals(100, $responseData[0]['quantity']);

        // Filter by brand 2
        $response = $this->getJson("/api/stocks?brand_id={$brand2->id}");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($brand2->id, $responseData[0]['brand_id']);
        $this->assertEquals(50, $responseData[0]['quantity']);
    }

    public function test_stock_complex_filtering_combinations()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);

        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id,
            'shop_id' => $shop->id,
            'brand_id' => $brand->id,
            'quantity' => 100,
            'value' => 1000.0
        ]);
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id,
            'shop_id' => $shop->id,
            'brand_id' => $brand->id,
            'quantity' => 50,
            'value' => 500.0
        ]);

        // Complex filter: product + shop + brand + quantity range
        $response = $this->getJson("/api/stocks?product_id={$product->id}&shop_id={$shop->id}&brand_id={$brand->id}&quantity_min=75");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals(100, $responseData[0]['quantity']);
    }

    public function test_stock_with_null_shop_and_brand()
    {
        Stock::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => null,
            'brand_id' => null,
            'quantity' => 75
        ]);

        $response = $this->getJson('/api/stocks');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertNull($responseData[0]['shop_id']);
        $this->assertNull($responseData[0]['brand_id']);
        $this->assertEquals(75, $responseData[0]['quantity']);
    }
}
