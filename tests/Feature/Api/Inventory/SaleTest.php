<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Sale;
use App\Models\Organization;
use App\Models\User;
use App\Models\Shop;
use App\Models\Client;
use App\Models\Item;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SaleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Shop $shop;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->shop = Shop::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_sale()
    {
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 150.50
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'user_id',
                        'shop_id',
                        'client_id',
                        'total_value',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals($this->user->id, $responseData['user_id']);
        $this->assertEquals($this->shop->id, $responseData['shop_id']);
        $this->assertEquals($this->client->id, $responseData['client_id']);
        $this->assertEquals(150.50, $responseData['total_value']);

        $this->assertDatabaseHas('sales', [
            'user_id' => $this->user->id,
            'organization_id' => $this->organization->id,
            'total_value' => 150.50
        ]);
    }

    public function test_can_get_sale_by_id()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 200.75
        ]);

        // Create items for the sale
        Item::factory()->count(2)->create(['sale_id' => $sale->id]);

        $response = $this->getJson("/api/sales/{$sale->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'user_id',
                        'shop_id',
                        'client_id',
                        'total_value',
                        'created_at',
                        'updated_at',
                        'user',
                        'shop',
                        'client',
                        'items'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($sale->id, $responseData['id']);
        $this->assertEquals(200.75, $responseData['total_value']);
        $this->assertNotNull($responseData['user']);
        $this->assertNotNull($responseData['shop']);
        $this->assertNotNull($responseData['client']);
        $this->assertIsArray($responseData['items']);
        $this->assertCount(2, $responseData['items']);
    }

    public function test_cannot_get_sale_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create(['organization_id' => $otherOrganization->id]);
        $sale = Sale::factory()->create([
            'organization_id' => $otherOrganization->id,
            'user_id' => $otherUser->id
        ]);

        $response = $this->getJson("/api/sales/{$sale->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_sale()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 100.00
        ]);

        $updateData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 250.75
        ];

        $response = $this->putJson("/api/sales/{$sale->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('sales', [
            'id' => $sale->id,
            'total_value' => 250.75,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(250.75, $responseData['total_value']);
    }

    public function test_cannot_update_sale_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create(['organization_id' => $otherOrganization->id]);
        $sale = Sale::factory()->create([
            'organization_id' => $otherOrganization->id,
            'user_id' => $otherUser->id
        ]);

        $updateData = [
            'total_value' => 999.99
        ];

        $response = $this->putJson("/api/sales/{$sale->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This sale don't belong to this organization."
                ]);
    }

    public function test_can_delete_sale()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        $response = $this->deleteJson("/api/sales/{$sale->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('sales', [
            'id' => $sale->id
        ]);
    }

    public function test_cannot_delete_sale_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create(['organization_id' => $otherOrganization->id]);
        $sale = Sale::factory()->create([
            'organization_id' => $otherOrganization->id,
            'user_id' => $otherUser->id
        ]);

        $response = $this->deleteJson("/api/sales/{$sale->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This sale don't belong to this organization."
                ]);
    }

    public function test_can_get_all_sales_with_pagination()
    {
        Sale::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        $response = $this->getJson('/api/sales?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_sales_by_user_id()
    {
        $user2 = User::factory()->create(['organization_id' => $this->organization->id]);

        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $user2->id
        ]);

        $response = $this->getJson("/api/sales?user_id={$this->user->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($this->user->id, $responseData['data'][0]['user_id']);
    }

    public function test_can_filter_sales_by_shop_id()
    {
        $shop2 = Shop::factory()->create(['organization_id' => $this->organization->id]);

        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $shop2->id
        ]);

        $response = $this->getJson("/api/sales?shop_id={$this->shop->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($this->shop->id, $responseData['data'][0]['shop_id']);
    }

    public function test_can_filter_sales_by_client_id()
    {
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'client_id' => $this->client->id
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'client_id' => $client2->id
        ]);

        $response = $this->getJson("/api/sales?client_id={$this->client->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($this->client->id, $responseData['data'][0]['client_id']);
    }

    public function test_can_filter_sales_by_total_value_range()
    {
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 50.00
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 150.00
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 250.00
        ]);

        $response = $this->getJson('/api/sales?min_total_value=100&max_total_value=200');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals(150.00, $responseData['data'][0]['total_value']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/sales', [
            'user_id' => '', // Empty user_id should fail validation
            'total_value' => 'invalid' // Invalid total_value should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id', 'total_value']);
    }

    public function test_validation_errors_on_update()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        $response = $this->putJson("/api/sales/{$sale->id}", [
            'user_id' => '', // Empty user_id should fail validation
            'total_value' => 'invalid' // Invalid total_value should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id', 'total_value']);
    }

    public function test_sale_soft_delete_behavior()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00
        ]);

        // Delete the sale
        $response = $this->deleteJson("/api/sales/{$sale->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/sales');
        $responseData = $response->json('data.data');
        $saleIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($sale->id, $saleIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/sales/{$sale->id}");
        $response->assertStatus(404);
    }

    public function test_sale_ordering_and_filtering_combinations()
    {
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00,
            'created_at' => now()->subDays(3)
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 200.00,
            'created_at' => now()->subDays(1)
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 300.00,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by total_value ascending
        $response = $this->getJson('/api/sales?order=total_value&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(100.00, $responseData[0]['total_value']);
        $this->assertEquals(200.00, $responseData[1]['total_value']);
        $this->assertEquals(300.00, $responseData[2]['total_value']);

        // Test ordering by total_value descending
        $response = $this->getJson('/api/sales?order=total_value&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals(300.00, $responseData[0]['total_value']);
        $this->assertEquals(200.00, $responseData[1]['total_value']);
        $this->assertEquals(100.00, $responseData[2]['total_value']);

        // Test filtering with ordering
        $response = $this->getJson('/api/sales?min_total_value=150&order=total_value&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData));
        $this->assertEquals(200.00, $responseData[0]['total_value']);
        $this->assertEquals(300.00, $responseData[1]['total_value']);
    }

    public function test_sale_with_zero_total_value()
    {
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 0.00
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0.00, $responseData['total_value']);

        $this->assertDatabaseHas('sales', [
            'user_id' => $this->user->id,
            'total_value' => 0.00
        ]);
    }

    public function test_sale_with_high_total_value()
    {
        $highValue = 999999.99;
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => $highValue
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($highValue, $responseData['total_value']);

        $this->assertDatabaseHas('sales', [
            'user_id' => $this->user->id,
            'total_value' => $highValue
        ]);
    }

    public function test_sale_with_decimal_total_value()
    {
        $decimalValue = 123.456;
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => $decimalValue
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($decimalValue, $responseData['total_value']);
    }

    public function test_sale_with_null_optional_fields()
    {
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => null,
            'client_id' => null,
            'total_value' => 100.00
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($this->user->id, $responseData['user_id']);
        $this->assertNull($responseData['shop_id']);
        $this->assertNull($responseData['client_id']);
        $this->assertEquals(100.00, $responseData['total_value']);
    }

    public function test_sale_data_consistency_across_operations()
    {
        $sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 150.00
        ]);

        // Get sale and verify data
        $getResponse = $this->getJson("/api/sales/{$sale->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals($this->user->id, $getData['user_id']);
        $this->assertEquals(150.00, $getData['total_value']);

        // Update sale
        $updateData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 250.75
        ];

        $updateResponse = $this->putJson("/api/sales/{$sale->id}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/sales/{$sale->id}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals($this->user->id, $getUpdatedData['user_id']);
        $this->assertEquals(250.75, $getUpdatedData['total_value']);

        // Verify data consistency in list view
        $listResponse = $this->getJson('/api/sales');
        $listData = $listResponse->json('data.data');

        $saleInList = collect($listData)->firstWhere('id', $sale->id);
        $this->assertNotNull($saleInList);
        $this->assertEquals($getUpdatedData['user_id'], $saleInList['user_id']);
        $this->assertEquals($getUpdatedData['total_value'], $saleInList['total_value']);
    }

    public function test_sale_organization_isolation()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $sale1 = Sale::factory()->create(['organization_id' => $org1->id, 'user_id' => $user1->id]);
        $sale2 = Sale::factory()->create(['organization_id' => $org2->id, 'user_id' => $user2->id]);

        // User 1 should only see sale 1
        Sanctum::actingAs($user1);
        $response = $this->getJson('/api/sales');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($sale1->id, $responseData[0]['id']);

        // User 2 should only see sale 2
        Sanctum::actingAs($user2);
        $response = $this->getJson('/api/sales');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($sale2->id, $responseData[0]['id']);
    }

    public function test_sale_complex_filtering_combinations()
    {
        $user2 = User::factory()->create(['organization_id' => $this->organization->id]);
        $shop2 = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 150.00
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $user2->id,
            'shop_id' => $shop2->id,
            'client_id' => $client2->id,
            'total_value' => 250.00
        ]);

        // Complex filter: user + shop + client + total_value range
        $response = $this->getJson("/api/sales?user_id={$this->user->id}&shop_id={$this->shop->id}&client_id={$this->client->id}&min_total_value=100&max_total_value=200");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($this->user->id, $responseData[0]['user_id']);
        $this->assertEquals($this->shop->id, $responseData[0]['shop_id']);
        $this->assertEquals($this->client->id, $responseData[0]['client_id']);
        $this->assertEquals(150.00, $responseData[0]['total_value']);
    }

    public function test_sale_with_date_range_filtering()
    {
        $yesterday = now()->subDay();
        $today = now();
        $tomorrow = now()->addDay();

        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $yesterday
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $today
        ]);
        Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $tomorrow
        ]);

        $response = $this->getJson('/api/sales?start_date=' . $today->format('Y-m-d') . '&end_date=' . $tomorrow->format('Y-m-d'));
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Today and tomorrow
    }

    public function test_sale_with_asaas_integration_flag()
    {
        $saleData = [
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id,
            'total_value' => 150.50,
            'enable_asaas_integration' => true
        ];

        $response = $this->postJson('/api/sales', $saleData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(150.50, $responseData['total_value']);

        $this->assertDatabaseHas('sales', [
            'user_id' => $this->user->id,
            'total_value' => 150.50
        ]);
    }
}
