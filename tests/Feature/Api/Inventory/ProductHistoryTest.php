<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\ProductHistory;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ProductHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Product $product;
    private Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_product_history()
    {
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'product_id',
                        'field',
                        'alias',
                        'old',
                        'new',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->user->id, $responseData['user_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals('price', $responseData['field']);
        $this->assertEquals('Preço', $responseData['alias']);
        $this->assertEquals('10.50', $responseData['old']);
        $this->assertEquals('15.75', $responseData['new']);

        $this->assertDatabaseHas('products_histories', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ]);
    }

    public function test_can_get_product_history_by_id()
    {
        $productHistory = ProductHistory::factory()->create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ]);

        $response = $this->getJson("/api/product-histories/{$productHistory->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'product_id',
                        'field',
                        'alias',
                        'old',
                        'new',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($productHistory->id, $responseData['id']);
        $this->assertEquals('price', $responseData['field']);
        $this->assertEquals('Preço', $responseData['alias']);
        $this->assertEquals('10.50', $responseData['old']);
        $this->assertEquals('15.75', $responseData['new']);
    }

    public function test_can_delete_product_history()
    {
        $productHistory = ProductHistory::factory()->create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/product-histories/{$productHistory->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('products_histories', [
            'id' => $productHistory->id
        ]);
    }

    public function test_cannot_get_product_history_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);
        
        $productHistory = ProductHistory::factory()->create([
            'product_id' => $otherProduct->id
        ]);

        $response = $this->getJson("/api/product-histories/{$productHistory->id}");

        $response->assertStatus(404);
    }

    public function test_can_get_all_product_histories_with_pagination()
    {
        ProductHistory::factory()->count(15)->create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/product-histories');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/product-histories', [
            'user_id' => '', // Empty user_id should fail validation
            'product_id' => '', // Empty product_id should fail validation
            'field' => '', // Empty field should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id', 'product_id', 'field']);
    }

    public function test_product_history_with_different_fields()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
            ['field' => 'unity', 'alias' => 'Unidade', 'old' => '1', 'new' => '2'],
        ];

        foreach ($fields as $fieldData) {
            $productHistoryData = [
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'field' => $fieldData['field'],
                'alias' => $fieldData['alias'],
                'old' => $fieldData['old'],
                'new' => $fieldData['new']
            ];

            $response = $this->postJson('/api/product-histories', $productHistoryData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($fieldData['field'], $responseData['field']);
            $this->assertEquals($fieldData['alias'], $responseData['alias']);
            $this->assertEquals($fieldData['old'], $responseData['old']);
            $this->assertEquals($fieldData['new'], $responseData['new']);

            $this->assertDatabaseHas('products_histories', [
                'field' => $fieldData['field'],
                'alias' => $fieldData['alias'],
                'old' => $fieldData['old'],
                'new' => $fieldData['new']
            ]);
        }
    }

    public function test_product_history_with_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '0.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $priceChange) {
            $productHistoryData = [
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'field' => 'price',
                'alias' => 'Preço',
                'old' => $priceChange['old'],
                'new' => $priceChange['new']
            ];

            $response = $this->postJson('/api/product-histories', $productHistoryData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($priceChange['old'], $responseData['old']);
            $this->assertEquals($priceChange['new'], $responseData['new']);

            $this->assertDatabaseHas('products_histories', [
                'old' => $priceChange['old'],
                'new' => $priceChange['new']
            ]);
        }
    }

    public function test_product_history_with_long_values()
    {
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => $longOldValue,
            'new' => $longNewValue
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($longOldValue, $responseData['old']);
        $this->assertEquals($longNewValue, $responseData['new']);

        $this->assertDatabaseHas('products_histories', [
            'old' => $longOldValue,
            'new' => $longNewValue
        ]);
    }

    public function test_product_history_with_null_values()
    {
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => null,
            'new' => null
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertNull($responseData['old']);
        $this->assertNull($responseData['new']);

        $this->assertDatabaseHas('products_histories', [
            'old' => null,
            'new' => null
        ]);
    }

    public function test_product_histories_organization_isolation()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create product histories for both organizations
        ProductHistory::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id
        ]);

        $otherBrand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $otherBrand->id
        ]);

        ProductHistory::factory()->count(2)->create([
            'user_id' => $otherUser->id,
            'product_id' => $otherProduct->id
        ]);

        // Current user should only see their organization's product histories
        $response = $this->getJson('/api/product-histories');
        $responseData = $response->json('data');
        $this->assertEquals(3, $responseData['count']);

        // Switch to other user
        Sanctum::actingAs($otherUser);

        // Other user should only see their organization's product histories
        $response = $this->getJson('/api/product-histories');
        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);
    }

    public function test_product_history_with_decimal_values()
    {
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '123.456789',
            'new' => '987.654321'
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('123.456789', $responseData['old']);
        $this->assertEquals('987.654321', $responseData['new']);
    }

    public function test_product_history_crud_workflow()
    {
        // Create
        $createData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ];

        $createResponse = $this->postJson('/api/product-histories', $createData);
        $createResponse->assertStatus(201);
        $productHistoryId = $createResponse->json('data.id');

        // Read
        $getResponse = $this->getJson("/api/product-histories/{$productHistoryId}");
        $getResponse->assertStatus(200);
        $this->assertEquals('price', $getResponse->json('data.field'));
        $this->assertEquals('Preço', $getResponse->json('data.alias'));

        // Delete
        $deleteResponse = $this->deleteJson("/api/product-histories/{$productHistoryId}");
        $deleteResponse->assertStatus(200);

        // Verify deletion
        $this->assertDatabaseMissing('products_histories', [
            'id' => $productHistoryId
        ]);
    }

    public function test_product_history_with_multiple_products()
    {
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        // Create product histories for different products
        $productHistory1Data = [
            'user_id' => $this->user->id,
            'product_id' => $product1->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ];

        $productHistory2Data = [
            'user_id' => $this->user->id,
            'product_id' => $product2->id,
            'field' => 'name',
            'alias' => 'Nome',
            'old' => 'Old Product 2',
            'new' => 'New Product 2'
        ];

        $response1 = $this->postJson('/api/product-histories', $productHistory1Data);
        $response2 = $this->postJson('/api/product-histories', $productHistory2Data);

        $response1->assertStatus(201);
        $response2->assertStatus(201);

        // Verify both product histories exist
        $allResponse = $this->getJson('/api/product-histories');
        $allData = $allResponse->json('data');
        $this->assertEquals(2, $allData['count']);

        $productIds = collect($allData['data'])->pluck('product_id')->toArray();
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }

    public function test_product_history_with_multiple_users()
    {
        $user2 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Create product histories for different users
        $productHistory1Data = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ];

        $productHistory2Data = [
            'user_id' => $user2->id,
            'product_id' => $this->product->id,
            'field' => 'name',
            'alias' => 'Nome',
            'old' => 'Old Product',
            'new' => 'New Product'
        ];

        $response1 = $this->postJson('/api/product-histories', $productHistory1Data);
        $response2 = $this->postJson('/api/product-histories', $productHistory2Data);

        $response1->assertStatus(201);
        $response2->assertStatus(201);

        // Verify both product histories exist
        $allResponse = $this->getJson('/api/product-histories');
        $allData = $allResponse->json('data');
        $this->assertEquals(2, $allData['count']);

        $userIds = collect($allData['data'])->pluck('user_id')->toArray();
        $this->assertContains($this->user->id, $userIds);
        $this->assertContains($user2->id, $userIds);
    }

    public function test_product_history_with_same_field_multiple_changes()
    {
        $priceChanges = [
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '25.50'],
        ];

        foreach ($priceChanges as $priceChange) {
            $productHistoryData = [
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'field' => 'price',
                'alias' => 'Preço',
                'old' => $priceChange['old'],
                'new' => $priceChange['new']
            ];

            $response = $this->postJson('/api/product-histories', $productHistoryData);
            $response->assertStatus(201);
        }

        // Verify all price changes are recorded
        $allResponse = $this->getJson('/api/product-histories');
        $allData = $allResponse->json('data');
        $this->assertEquals(3, $allData['count']);

        // All should be price field changes
        foreach ($allData['data'] as $productHistory) {
            $this->assertEquals('price', $productHistory['field']);
            $this->assertEquals('Preço', $productHistory['alias']);
        }
    }

    public function test_product_history_with_empty_values()
    {
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => '',
            'new' => ''
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('', $responseData['old']);
        $this->assertEquals('', $responseData['new']);
    }

    public function test_product_history_tracks_complete_product_lifecycle()
    {
        // Simulate a complete product lifecycle with multiple changes
        $changes = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Initial Product', 'new' => 'Updated Product'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '0.00', 'new' => '10.50'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Initial description', 'new' => 'Updated description'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '15.75', 'new' => '20.00'],
        ];

        foreach ($changes as $change) {
            $productHistoryData = [
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'field' => $change['field'],
                'alias' => $change['alias'],
                'old' => $change['old'],
                'new' => $change['new']
            ];

            $response = $this->postJson('/api/product-histories', $productHistoryData);
            $response->assertStatus(201);
        }

        // Verify all changes are recorded
        $allResponse = $this->getJson('/api/product-histories');
        $allData = $allResponse->json('data');
        $this->assertEquals(5, $allData['count']);

        // Verify we have the expected field types
        $fields = collect($allData['data'])->pluck('field')->toArray();
        $this->assertContains('name', $fields);
        $this->assertContains('price', $fields);
        $this->assertContains('description', $fields);

        // Count price changes (should be 3)
        $priceChanges = collect($allData['data'])->where('field', 'price')->count();
        $this->assertEquals(3, $priceChanges);
    }

    public function test_product_history_with_special_characters()
    {
        $productHistoryData = [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => 'Produto com acentos: ção, ã, é, í, ó, ú',
            'new' => 'Produto atualizado: ção, ã, é, í, ó, ú & símbolos @#$%'
        ];

        $response = $this->postJson('/api/product-histories', $productHistoryData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Produto com acentos: ção, ã, é, í, ó, ú', $responseData['old']);
        $this->assertEquals('Produto atualizado: ção, ã, é, í, ó, ú & símbolos @#$%', $responseData['new']);
    }
}
