<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\StockExit;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Shop;
use App\Models\Batch;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class StockExitTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Product $product;
    private Brand $brand;
    private Shop $shop;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        $this->shop = Shop::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_stock_exit()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Test stock exit'
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'user_id',
                        'brand_id',
                        'product_id',
                        'batch_id',
                        'client_id',
                        'project_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals($this->user->id, $responseData['user_id']);
        $this->assertEquals($this->shop->id, $responseData['shop_id']);
        $this->assertEquals($this->brand->id, $responseData['brand_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals(100, $responseData['quantity']);
        $this->assertEquals(1500.50, $responseData['value']);
        $this->assertEquals('Test stock exit', $responseData['description']);

        $this->assertDatabaseHas('stock_exits', [
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Test stock exit'
        ]);
    }

    public function test_can_get_stock_exit_by_id()
    {
        $exit = StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Get exit test'
        ]);

        $response = $this->getJson("/api/stock-exits/{$exit->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'user_id',
                        'brand_id',
                        'product_id',
                        'batch_id',
                        'client_id',
                        'project_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($exit->id, $responseData['id']);
        $this->assertEquals(50, $responseData['quantity']);
        $this->assertEquals(750.25, $responseData['value']);
        $this->assertEquals('Get exit test', $responseData['description']);
    }

    public function test_cannot_get_stock_exit_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $exit = StockExit::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/stock-exits/{$exit->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_stock_exit()
    {
        $exit = StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Old description'
        ]);

        $updateData = [
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/stock-exits/{$exit->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('stock_exits', [
            'id' => $exit->id,
            'quantity' => 150,
            'value' => 2250.75,
            'description' => 'Updated description',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(150, $responseData['quantity']);
        $this->assertEquals(2250.75, $responseData['value']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_cannot_update_stock_exit_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $exit = StockExit::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'quantity' => 200
        ];

        $response = $this->putJson("/api/stock-exits/{$exit->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This stock exit don't belong to this organization."
                ]);
    }

    public function test_can_delete_stock_exit()
    {
        $exit = StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/stock-exits/{$exit->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('stock_exits', [
            'id' => $exit->id
        ]);
    }

    public function test_cannot_delete_stock_exit_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $exit = StockExit::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/stock-exits/{$exit->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This stock exit don't belong to this organization."
                ]);
    }

    public function test_can_get_all_stock_exits_with_pagination()
    {
        StockExit::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/stock-exits?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_stock_exits_by_quantity()
    {
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100
        ]);
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 200
        ]);

        $response = $this->getJson('/api/stock-exits?quantity=100');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals(100, $responseData['data'][0]['quantity']);
    }

    public function test_can_filter_stock_exits_by_value()
    {
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'value' => 1500.50
        ]);
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'value' => 2500.75
        ]);

        $response = $this->getJson('/api/stock-exits?value=1500.50');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals(1500.50, $responseData['data'][0]['value']);
    }

    public function test_can_filter_stock_exits_by_description()
    {
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Alpha exit'
        ]);
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Beta exit'
        ]);

        $response = $this->getJson('/api/stock-exits?description=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha exit', $responseData['data'][0]['description']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/stock-exits', [
            'shop_id' => '', // Empty shop_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['shop_id', 'quantity']);
    }

    public function test_validation_errors_on_update()
    {
        $exit = StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->putJson("/api/stock-exits/{$exit->id}", [
            'quantity' => 'invalid', // Invalid quantity should fail validation
            'value' => 'invalid' // Invalid value should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['quantity', 'value']);
    }

    public function test_stock_exit_soft_delete_behavior()
    {
        $exit = StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'description' => 'Soft delete exit'
        ]);

        // Delete the exit
        $response = $this->deleteJson("/api/stock-exits/{$exit->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/stock-exits');
        $responseData = $response->json('data.data');
        $exitIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($exit->id, $exitIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/stock-exits/{$exit->id}");
        $response->assertStatus(404);
    }

    public function test_stock_exit_ordering_and_filtering_combinations()
    {
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'description' => 'Alpha exit',
            'created_at' => now()->subDays(3)
        ]);
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 200,
            'description' => 'Beta exit',
            'created_at' => now()->subDays(1)
        ]);
        StockExit::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 150,
            'description' => 'Charlie exit',
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by quantity ascending
        $response = $this->getJson('/api/stock-exits?order=quantity&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(100, $responseData[0]['quantity']);
        $this->assertEquals(150, $responseData[1]['quantity']);
        $this->assertEquals(200, $responseData[2]['quantity']);

        // Test ordering by quantity descending
        $response = $this->getJson('/api/stock-exits?order=quantity&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals(200, $responseData[0]['quantity']);
        $this->assertEquals(150, $responseData[1]['quantity']);
        $this->assertEquals(100, $responseData[2]['quantity']);

        // Test filtering with ordering
        $response = $this->getJson('/api/stock-exits?description=a&order=quantity&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals(100, $responseData[0]['quantity']); // Alpha
        $this->assertEquals(150, $responseData[1]['quantity']); // Charlie
    }

    public function test_stock_exit_with_special_characters()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 50,
            'value' => 750.25,
            'description' => 'Exit with special chars: @#$%^&*()'
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Exit with special chars: @#$%^&*()', $responseData['description']);

        $this->assertDatabaseHas('stock_exits', [
            'description' => 'Exit with special chars: @#$%^&*()'
        ]);
    }

    public function test_stock_exit_with_unicode_characters()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 75,
            'value' => 1125.75,
            'description' => 'Salida con caracteres especiales: ñáéíóú 中文'
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Salida con caracteres especiales: ñáéíóú 中文', $responseData['description']);

        $this->assertDatabaseHas('stock_exits', [
            'description' => 'Salida con caracteres especiales: ñáéíóú 中文'
        ]);
    }

    public function test_stock_exit_with_null_description()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 25,
            'value' => 375.25,
            'description' => null
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertNull($responseData['description']);
    }

    public function test_stock_exit_with_zero_quantity()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 0,
            'value' => 0.0,
            'description' => 'Zero quantity exit'
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_stock_exit_with_high_quantity()
    {
        $exitData = [
            'shop_id' => $this->shop->id,
            'brand_id' => $this->brand->id,
            'product_id' => $this->product->id,
            'quantity' => 999999,
            'value' => 99999999.99,
            'description' => 'High quantity exit'
        ];

        $response = $this->postJson('/api/stock-exits', $exitData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }
}
