<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Shop;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ShopTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_shop()
    {
        $shopData = [
            'name' => 'Test Shop',
            'description' => 'Test shop description',
            'is_active' => true
        ];

        $response = $this->postJson('/api/shops', $shopData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Shop', $responseData['name']);
        $this->assertEquals('Test shop description', $responseData['description']);
        $this->assertTrue($responseData['is_active']);

        $this->assertDatabaseHas('shops', [
            'name' => 'Test Shop',
            'organization_id' => $this->organization->id,
            'description' => 'Test shop description',
            'is_active' => true
        ]);
    }

    public function test_can_get_shop_by_id()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Get Shop',
            'description' => 'Shop for get test',
            'is_active' => true
        ]);

        $response = $this->getJson("/api/shops/{$shop->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($shop->id, $responseData['id']);
        $this->assertEquals('Get Shop', $responseData['name']);
        $this->assertEquals('Shop for get test', $responseData['description']);
        $this->assertTrue($responseData['is_active']);
    }

    public function test_cannot_get_shop_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $shop = Shop::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/shops/{$shop->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_shop()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Shop',
            'description' => 'Old description',
            'is_active' => true
        ]);

        $updateData = [
            'name' => 'Updated Shop',
            'description' => 'Updated description',
            'is_active' => false
        ];

        $response = $this->putJson("/api/shops/{$shop->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('shops', [
            'id' => $shop->id,
            'name' => 'Updated Shop',
            'description' => 'Updated description',
            'is_active' => false,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Shop', $responseData['name']);
        $this->assertEquals('Updated description', $responseData['description']);
        $this->assertFalse($responseData['is_active']);
    }

    public function test_cannot_update_shop_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $shop = Shop::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Unauthorized Update'
        ];

        $response = $this->putJson("/api/shops/{$shop->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This shop don't belong to this organization."
                ]);
    }

    public function test_can_delete_shop()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/shops/{$shop->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('shops', [
            'id' => $shop->id
        ]);
    }

    public function test_cannot_delete_shop_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $shop = Shop::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/shops/{$shop->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This shop don't belong to this organization."
                ]);
    }

    public function test_can_get_all_shops_with_pagination()
    {
        Shop::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/shops?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_shops_by_name()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Shop'
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Shop'
        ]);

        $response = $this->getJson('/api/shops?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Shop', $responseData['data'][0]['name']);
    }

    public function test_can_filter_shops_by_description()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Shop 1',
            'description' => 'Alpha description'
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Shop 2',
            'description' => 'Beta description'
        ]);

        $response = $this->getJson('/api/shops?description=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha description', $responseData['data'][0]['description']);
    }

    public function test_can_filter_shops_by_active_status()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Shop',
            'is_active' => true
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Shop',
            'is_active' => false
        ]);

        $response = $this->getJson('/api/shops?is_active=1');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Active Shop', $responseData['data'][0]['name']);
        $this->assertTrue($responseData['data'][0]['is_active']);
    }

    public function test_can_filter_shops_by_inactive_status()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Shop',
            'is_active' => true
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Shop',
            'is_active' => false
        ]);

        $response = $this->getJson('/api/shops?is_active=0');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Inactive Shop', $responseData['data'][0]['name']);
        $this->assertFalse($responseData['data'][0]['is_active']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/shops', [
            'name' => '', // Empty name should fail validation
            'is_active' => 'invalid' // Invalid is_active should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_validation_errors_on_update()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/shops/{$shop->id}", [
            'name' => '', // Empty name should fail validation
            'is_active' => 'invalid' // Invalid is_active should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_shop_soft_delete_behavior()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Soft Delete Shop'
        ]);

        // Delete the shop
        $response = $this->deleteJson("/api/shops/{$shop->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/shops');
        $responseData = $response->json('data.data');
        $shopIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($shop->id, $shopIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/shops/{$shop->id}");
        $response->assertStatus(404);
    }

    public function test_shop_ordering_and_filtering_combinations()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Shop',
            'description' => 'Alpha description',
            'created_at' => now()->subDays(3)
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Shop',
            'description' => 'Beta description',
            'created_at' => now()->subDays(1)
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Shop',
            'description' => 'Charlie description',
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/shops?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alpha Shop', $responseData[0]['name']);
        $this->assertEquals('Beta Shop', $responseData[1]['name']);
        $this->assertEquals('Charlie Shop', $responseData[2]['name']);

        // Test ordering by name descending
        $response = $this->getJson('/api/shops?order=name&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Charlie Shop', $responseData[0]['name']);
        $this->assertEquals('Beta Shop', $responseData[1]['name']);
        $this->assertEquals('Alpha Shop', $responseData[2]['name']);

        // Test filtering with ordering
        $response = $this->getJson('/api/shops?name=a&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals('Alpha Shop', $responseData[0]['name']);
        $this->assertEquals('Charlie Shop', $responseData[1]['name']);
    }

    public function test_shop_with_special_characters()
    {
        $shopData = [
            'name' => 'Shop & Co. (™)',
            'description' => 'Description with special chars: @#$%^&*()',
            'is_active' => true
        ];

        $response = $this->postJson('/api/shops', $shopData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Shop & Co. (™)', $responseData['name']);
        $this->assertEquals('Description with special chars: @#$%^&*()', $responseData['description']);

        $this->assertDatabaseHas('shops', [
            'name' => 'Shop & Co. (™)',
            'description' => 'Description with special chars: @#$%^&*()'
        ]);
    }

    public function test_shop_with_unicode_characters()
    {
        $shopData = [
            'name' => 'Shöp Ñamé 中文',
            'description' => 'Descripción con caracteres especiales: ñáéíóú',
            'is_active' => true
        ];

        $response = $this->postJson('/api/shops', $shopData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Shöp Ñamé 中文', $responseData['name']);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $responseData['description']);

        $this->assertDatabaseHas('shops', [
            'name' => 'Shöp Ñamé 中文',
            'description' => 'Descripción con caracteres especiales: ñáéíóú'
        ]);
    }

    public function test_shop_with_empty_description()
    {
        $shopData = [
            'name' => 'No Description Shop',
            'description' => '',
            'is_active' => true
        ];

        $response = $this->postJson('/api/shops', $shopData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('No Description Shop', $responseData['name']);
        $this->assertEquals('', $responseData['description']);
    }

    public function test_shop_with_long_name_and_description()
    {
        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);

        $shopData = [
            'name' => $longName,
            'description' => $longDescription,
            'is_active' => true
        ];

        $response = $this->postJson('/api/shops', $shopData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($longName, $responseData['name']);
        $this->assertEquals($longDescription, $responseData['description']);
    }

    public function test_shop_data_consistency_across_operations()
    {
        $shop = Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Consistency Shop',
            'description' => 'Shop for consistency test',
            'is_active' => true
        ]);

        // Get shop and verify data
        $getResponse = $this->getJson("/api/shops/{$shop->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals('Consistency Shop', $getData['name']);
        $this->assertTrue($getData['is_active']);

        // Update shop
        $updateData = [
            'name' => 'Updated Consistency Shop',
            'description' => 'Updated description',
            'is_active' => false
        ];

        $updateResponse = $this->putJson("/api/shops/{$shop->id}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/shops/{$shop->id}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals('Updated Consistency Shop', $getUpdatedData['name']);
        $this->assertEquals('Updated description', $getUpdatedData['description']);
        $this->assertFalse($getUpdatedData['is_active']);

        // Verify data consistency in list view
        $listResponse = $this->getJson('/api/shops');
        $listData = $listResponse->json('data.data');

        $shopInList = collect($listData)->firstWhere('id', $shop->id);
        $this->assertNotNull($shopInList);
        $this->assertEquals($getUpdatedData['name'], $shopInList['name']);
        $this->assertEquals($getUpdatedData['description'], $shopInList['description']);
        $this->assertEquals($getUpdatedData['is_active'], $shopInList['is_active']);
    }

    public function test_shop_organization_isolation()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $shop1 = Shop::factory()->create(['organization_id' => $org1->id]);
        $shop2 = Shop::factory()->create(['organization_id' => $org2->id]);

        // User 1 should only see shop 1
        Sanctum::actingAs($user1);
        $response = $this->getJson('/api/shops');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($shop1->id, $responseData[0]['id']);

        // User 2 should only see shop 2
        Sanctum::actingAs($user2);
        $response = $this->getJson('/api/shops');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($shop2->id, $responseData[0]['id']);
    }

    public function test_shop_complex_filtering_combinations()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Alpha Shop',
            'description' => 'Alpha description',
            'is_active' => true
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Inactive Alpha Shop',
            'description' => 'Alpha description',
            'is_active' => false
        ]);
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Active Beta Shop',
            'description' => 'Beta description',
            'is_active' => true
        ]);

        // Complex filter: name + description + is_active
        $response = $this->getJson('/api/shops?name=Alpha&description=Alpha&is_active=1');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Active Alpha Shop', $responseData[0]['name']);
        $this->assertEquals('Alpha description', $responseData[0]['description']);
        $this->assertTrue($responseData[0]['is_active']);
    }

    public function test_shop_case_insensitive_filtering()
    {
        Shop::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Shop',
            'description' => 'Alpha Description'
        ]);

        // Test case insensitive name filtering
        $response = $this->getJson('/api/shops?name=alpha');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Alpha Shop', $responseData[0]['name']);

        // Test case insensitive description filtering
        $response = $this->getJson('/api/shops?description=alpha');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Alpha Description', $responseData[0]['description']);
    }
}
