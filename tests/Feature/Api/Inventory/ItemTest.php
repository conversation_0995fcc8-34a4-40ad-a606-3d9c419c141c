<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Item;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Sale;
use App\Models\Client;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ItemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Product $product;
    private Brand $brand;
    private Sale $sale;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'user_id' => $this->user->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_item()
    {
        $itemData = [
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50
        ];

        $response = $this->postJson('/api/items', $itemData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'sale_id',
                        'product_id',
                        'quantity',
                        'value',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals($this->sale->id, $responseData['sale_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals(10, $responseData['quantity']);
        $this->assertEquals(150.50, $responseData['value']);

        $this->assertDatabaseHas('items', [
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50
        ]);
    }

    public function test_can_get_item_by_id()
    {
        $item = Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $response = $this->getJson("/api/items/{$item->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'sale_id',
                        'product_id',
                        'quantity',
                        'value',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($item->id, $responseData['id']);
        $this->assertEquals(5, $responseData['quantity']);
        $this->assertEquals(75.25, $responseData['value']);
    }

    public function test_cannot_get_item_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $item = Item::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/items/{$item->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_item()
    {
        $item = Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $updateData = [
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 15,
            'value' => 225.75
        ];

        $response = $this->putJson("/api/items/{$item->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('items', [
            'id' => $item->id,
            'quantity' => 15,
            'value' => 225.75,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['quantity']);
        $this->assertEquals(225.75, $responseData['value']);
    }

    public function test_cannot_update_item_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $item = Item::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'quantity' => 20
        ];

        $response = $this->putJson("/api/items/{$item->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This item don't belong to this organization."
                ]);
    }

    public function test_can_delete_item()
    {
        $item = Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/items/{$item->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('items', [
            'id' => $item->id
        ]);
    }

    public function test_cannot_delete_item_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $item = Item::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/items/{$item->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This item don't belong to this organization."
                ]);
    }

    public function test_can_get_all_items_with_pagination()
    {
        Item::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/items');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
    }

    public function test_can_get_items_from_specific_sale()
    {
        $sale1 = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'user_id' => $this->user->id
        ]);
        $sale2 = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'user_id' => $this->user->id
        ]);

        Item::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $sale1->id,
            'product_id' => $this->product->id
        ]);
        Item::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $sale2->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson("/api/sales/{$sale1->id}/items");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        foreach ($responseData as $item) {
            $this->assertEquals($sale1->id, $item['sale_id']);
        }
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/items', [
            'sale_id' => '', // Empty sale_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['sale_id', 'quantity']);
    }

    public function test_validation_errors_on_update()
    {
        $item = Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->putJson("/api/items/{$item->id}", [
            'quantity' => 'invalid', // Invalid quantity should fail validation
            'value' => 'invalid' // Invalid value should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['quantity', 'value']);
    }

    public function test_item_soft_delete_behavior()
    {
        $item = Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id
        ]);

        // Delete the item
        $response = $this->deleteJson("/api/items/{$item->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/items');
        $responseData = $response->json('data.data');
        $itemIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($item->id, $itemIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/items/{$item->id}");
        $response->assertStatus(404);
    }

    public function test_item_with_zero_quantity()
    {
        $itemData = [
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 0,
            'value' => 0.0
        ];

        $response = $this->postJson('/api/items', $itemData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_item_with_high_quantity()
    {
        $itemData = [
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 999999,
            'value' => 99999999.99
        ];

        $response = $this->postJson('/api/items', $itemData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }

    public function test_item_with_decimal_values()
    {
        $itemData = [
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 33,
            'value' => 123.456789
        ];

        $response = $this->postJson('/api/items', $itemData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(33, $responseData['quantity']);
        $this->assertEquals(123.456789, $responseData['value']);
    }

    public function test_items_organization_isolation()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create items for both organizations
        Item::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id
        ]);

        $otherSale = Sale::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        Item::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id,
            'sale_id' => $otherSale->id,
            'product_id' => $otherProduct->id
        ]);

        // Current user should only see their organization's items
        $response = $this->getJson('/api/items');
        $responseData = $response->json('data');
        $this->assertEquals(3, $responseData['count']);

        // Switch to other user
        Sanctum::actingAs($otherUser);

        // Other user should only see their organization's items
        $response = $this->getJson('/api/items');
        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);
    }

    public function test_items_from_sale_with_multiple_products()
    {
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $product1->id,
            'quantity' => 10,
            'value' => 150.50
        ]);
        Item::factory()->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $this->sale->id,
            'product_id' => $product2->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $response = $this->getJson("/api/sales/{$this->sale->id}/items");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);

        // Verify different products
        $productIds = collect($responseData)->pluck('product_id')->toArray();
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }

    public function test_empty_sale_items_endpoint()
    {
        $emptySale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'user_id' => $this->user->id
        ]);

        $response = $this->getJson("/api/sales/{$emptySale->id}/items");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertEmpty($responseData);
    }
}
