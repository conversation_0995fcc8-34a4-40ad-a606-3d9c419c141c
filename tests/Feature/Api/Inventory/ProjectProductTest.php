<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\ProjectProduct;
use App\Models\Organization;
use App\Models\User;
use App\Models\Project;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Client;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ProjectProductTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Project $project;
    private Product $product;
    private Brand $brand;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_project_product()
    {
        $projectProductData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test project product'
        ];

        $response = $this->postJson('/api/project-products', $projectProductData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'product_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->project->id, $responseData['project_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals(10, $responseData['quantity']);
        $this->assertEquals(150.50, $responseData['value']);
        $this->assertEquals('Test project product', $responseData['description']);

        $this->assertDatabaseHas('project_products', [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test project product'
        ]);
    }

    public function test_can_create_custom_project_product()
    {
        $customProjectProductData = [
            'project_id' => $this->project->id,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Test custom project product'
        ];

        $response = $this->postJson('/api/project-products/custom', $customProjectProductData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'product_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->project->id, $responseData['project_id']);
        $this->assertNull($responseData['product_id']); // Custom products have null product_id
        $this->assertEquals(15, $responseData['quantity']);
        $this->assertEquals(225.75, $responseData['value']);
        $this->assertEquals('Test custom project product', $responseData['description']);

        $this->assertDatabaseHas('project_products', [
            'project_id' => $this->project->id,
            'product_id' => null,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Test custom project product'
        ]);
    }

    public function test_can_get_project_product_by_id()
    {
        $projectProduct = ProjectProduct::factory()->create([
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Test project product'
        ]);

        $response = $this->getJson("/api/project-products/{$projectProduct->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'project_id',
                        'product_id',
                        'quantity',
                        'value',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($projectProduct->id, $responseData['id']);
        $this->assertEquals(5, $responseData['quantity']);
        $this->assertEquals(75.25, $responseData['value']);
        $this->assertEquals('Test project product', $responseData['description']);
    }

    public function test_can_delete_project_product()
    {
        $projectProduct = ProjectProduct::factory()->create([
            'project_id' => $this->project->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/project-products/{$projectProduct->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('project_products', [
            'id' => $projectProduct->id
        ]);
    }

    public function test_cannot_get_project_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherProject = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        
        $projectProduct = ProjectProduct::factory()->create([
            'project_id' => $otherProject->id
        ]);

        $response = $this->getJson("/api/project-products/{$projectProduct->id}");

        $response->assertStatus(404);
    }

    public function test_can_get_all_project_products_with_pagination()
    {
        ProjectProduct::factory()->count(15)->create([
            'project_id' => $this->project->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/project-products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
    }

    public function test_can_get_project_products_from_specific_project()
    {
        $project1 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);
        $project2 = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        ProjectProduct::factory()->count(3)->create(['project_id' => $project1->id]);
        ProjectProduct::factory()->count(2)->create(['project_id' => $project2->id]);

        $response = $this->getJson("/api/projects/{$project1->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        foreach ($responseData as $projectProduct) {
            $this->assertEquals($project1->id, $projectProduct['project_id']);
        }
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/project-products', [
            'project_id' => '', // Empty project_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['project_id', 'quantity']);
    }

    public function test_project_product_with_zero_quantity()
    {
        $projectProductData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 0,
            'value' => 0.0,
            'description' => 'Zero quantity project product'
        ];

        $response = $this->postJson('/api/project-products', $projectProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_project_product_with_high_quantity()
    {
        $projectProductData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 999999,
            'value' => 99999999.99,
            'description' => 'High quantity project product'
        ];

        $response = $this->postJson('/api/project-products', $projectProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }

    public function test_project_product_with_decimal_values()
    {
        $projectProductData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 33,
            'value' => 123.456789,
            'description' => 'Decimal value project product'
        ];

        $response = $this->postJson('/api/project-products', $projectProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(33, $responseData['quantity']);
        $this->assertEquals(123.456789, $responseData['value']);
    }

    public function test_project_products_organization_isolation()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create project products for both organizations
        ProjectProduct::factory()->count(3)->create([
            'project_id' => $this->project->id,
            'product_id' => $this->product->id
        ]);

        $otherProject = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        ProjectProduct::factory()->count(2)->create([
            'project_id' => $otherProject->id
        ]);

        // Current user should only see their organization's project products
        $response = $this->getJson('/api/project-products');
        $responseData = $response->json('data');
        $this->assertEquals(3, $responseData['count']);

        // Switch to other user
        Sanctum::actingAs($otherUser);

        // Other user should only see their organization's project products
        $response = $this->getJson('/api/project-products');
        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);
    }

    public function test_project_products_from_project_with_mixed_regular_and_custom()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        // Create regular project product
        ProjectProduct::factory()->create([
            'project_id' => $project->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Regular project product'
        ]);

        // Create custom project product
        ProjectProduct::factory()->create([
            'project_id' => $project->id,
            'product_id' => null, // Custom
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Custom project product'
        ]);

        $response = $this->getJson("/api/projects/{$project->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);

        // Verify mixed regular and custom products
        $productIds = collect($responseData)->pluck('product_id')->toArray();
        $this->assertContains($this->product->id, $productIds); // Regular
        $this->assertContains(null, $productIds); // Custom
    }

    public function test_project_products_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            $projectProductData = [
                'project_id' => $this->project->id,
                'product_id' => $this->product->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value'],
                'description' => "Project product with quantity {$testCase['quantity']}"
            ];

            $response = $this->postJson('/api/project-products', $projectProductData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($testCase['quantity'], $responseData['quantity']);
            $this->assertEquals($testCase['value'], $responseData['value']);

            $this->assertDatabaseHas('project_products', [
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }
    }

    public function test_custom_project_products_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 2, 'value' => 20.75],
            ['quantity' => 10, 'value' => 105.50],
            ['quantity' => 50, 'value' => 525.25],
            ['quantity' => 500, 'value' => 5250.00],
        ];

        foreach ($testCases as $testCase) {
            $customProjectProductData = [
                'project_id' => $this->project->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value'],
                'description' => "Custom project product with quantity {$testCase['quantity']}"
            ];

            $response = $this->postJson('/api/project-products/custom', $customProjectProductData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertNull($responseData['product_id']); // Custom products
            $this->assertEquals($testCase['quantity'], $responseData['quantity']);
            $this->assertEquals($testCase['value'], $responseData['value']);

            $this->assertDatabaseHas('project_products', [
                'product_id' => null,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }
    }

    public function test_project_product_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description for a project product. ', 10);

        $projectProductData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 1,
            'value' => 100.00,
            'description' => $longDescription
        ];

        $response = $this->postJson('/api/project-products', $projectProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($longDescription, $responseData['description']);

        $this->assertDatabaseHas('project_products', [
            'description' => $longDescription
        ]);
    }

    public function test_custom_project_product_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description for a custom project product. ', 10);

        $customProjectProductData = [
            'project_id' => $this->project->id,
            'quantity' => 1,
            'value' => 100.00,
            'description' => $longDescription
        ];

        $response = $this->postJson('/api/project-products/custom', $customProjectProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertNull($responseData['product_id']);
        $this->assertEquals($longDescription, $responseData['description']);

        $this->assertDatabaseHas('project_products', [
            'product_id' => null,
            'description' => $longDescription
        ]);
    }

    public function test_empty_project_products_endpoint()
    {
        $emptyProject = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $response = $this->getJson("/api/projects/{$emptyProject->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertEmpty($responseData);
    }

    public function test_project_product_crud_workflow()
    {
        // Create regular project product
        $createData = [
            'project_id' => $this->project->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'CRUD workflow project product'
        ];

        $createResponse = $this->postJson('/api/project-products', $createData);
        $createResponse->assertStatus(201);
        $projectProductId = $createResponse->json('data.id');

        // Read
        $getResponse = $this->getJson("/api/project-products/{$projectProductId}");
        $getResponse->assertStatus(200);
        $this->assertEquals('CRUD workflow project product', $getResponse->json('data.description'));

        // Delete
        $deleteResponse = $this->deleteJson("/api/project-products/{$projectProductId}");
        $deleteResponse->assertStatus(200);

        // Verify deletion
        $this->assertDatabaseMissing('project_products', [
            'id' => $projectProductId
        ]);
    }

    public function test_custom_project_product_crud_workflow()
    {
        // Create custom project product
        $createData = [
            'project_id' => $this->project->id,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'CRUD workflow custom project product'
        ];

        $createResponse = $this->postJson('/api/project-products/custom', $createData);
        $createResponse->assertStatus(201);
        $customProjectProductId = $createResponse->json('data.id');

        // Read
        $getResponse = $this->getJson("/api/project-products/{$customProjectProductId}");
        $getResponse->assertStatus(200);
        $this->assertEquals('CRUD workflow custom project product', $getResponse->json('data.description'));
        $this->assertNull($getResponse->json('data.product_id'));

        // Delete
        $deleteResponse = $this->deleteJson("/api/project-products/{$customProjectProductId}");
        $deleteResponse->assertStatus(200);

        // Verify deletion
        $this->assertDatabaseMissing('project_products', [
            'id' => $customProjectProductId
        ]);
    }

    public function test_project_product_with_multiple_products_from_same_project()
    {
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        // Create project products with different products
        $projectProduct1Data = [
            'project_id' => $this->project->id,
            'product_id' => $product1->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Project Product 1'
        ];

        $projectProduct2Data = [
            'project_id' => $this->project->id,
            'product_id' => $product2->id,
            'quantity' => 20,
            'value' => 300.75,
            'description' => 'Project Product 2'
        ];

        $response1 = $this->postJson('/api/project-products', $projectProduct1Data);
        $response2 = $this->postJson('/api/project-products', $projectProduct2Data);

        $response1->assertStatus(201);
        $response2->assertStatus(201);

        // Verify both project products exist for the same project
        $projectResponse = $this->getJson("/api/projects/{$this->project->id}/products");
        $projectData = $projectResponse->json('data');
        $this->assertCount(2, $projectData);

        $productIds = collect($projectData)->pluck('product_id')->toArray();
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }
}
