<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\DepartmentUser;
use App\Models\Department;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DepartmentUserTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_department_user()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUserData = [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ];

        $response = $this->postJson('/api/department_users', $departmentUserData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'department_id',
                        'created_at',
                        'updated_at',
                        'user',
                        'department'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($targetUser->id, $responseData['user_id']);
        $this->assertEquals($this->department->id, $responseData['department_id']);

        $this->assertDatabaseHas('department_users', [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);
    }

    public function test_cannot_create_department_user_without_user_id()
    {
        $departmentUserData = [
            'department_id' => $this->department->id
        ];

        $response = $this->postJson('/api/department_users', $departmentUserData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id']);
    }

    public function test_cannot_create_department_user_without_department_id()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUserData = [
            'user_id' => $targetUser->id
        ];

        $response = $this->postJson('/api/department_users', $departmentUserData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['department_id']);
    }

    public function test_can_get_department_user_by_id()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);

        $response = $this->getJson("/api/department_users/{$departmentUser->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'department_id',
                        'created_at',
                        'updated_at',
                        'user',
                        'department'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($departmentUser->id, $responseData['id']);
        $this->assertEquals($targetUser->id, $responseData['user_id']);
        $this->assertEquals($this->department->id, $responseData['department_id']);
    }

    public function test_cannot_get_department_user_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $otherUser->id,
            'department_id' => $otherDepartment->id
        ]);

        $response = $this->getJson("/api/department_users/{$departmentUser->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This department user assignment doesn't belong to this organization."
                ]);
    }

    public function test_can_update_department_user()
    {
        $targetUser1 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $targetUser2 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $department2 = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser1->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'user_id' => $targetUser2->id,
            'department_id' => $department2->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('department_users', [
            'id' => $departmentUser->id,
            'user_id' => $targetUser2->id,
            'department_id' => $department2->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($targetUser2->id, $responseData['user_id']);
        $this->assertEquals($department2->id, $responseData['department_id']);
    }

    public function test_can_update_department_user_partially()
    {
        $targetUser1 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $targetUser2 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser1->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'user_id' => $targetUser2->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('department_users', [
            'id' => $departmentUser->id,
            'user_id' => $targetUser2->id,
            'department_id' => $this->department->id // Should remain unchanged
        ]);
    }

    public function test_cannot_update_department_user_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $otherUser->id,
            'department_id' => $otherDepartment->id
        ]);

        $updateData = [
            'user_id' => $this->user->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error'
                ]);
    }

    public function test_cannot_update_department_user_with_user_from_different_organization()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'user_id' => $otherUser->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "The specified user doesn't belong to this organization."
                ]);
    }

    public function test_cannot_update_department_user_with_department_from_different_organization()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $otherOrganization = Organization::factory()->create();
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'department_id' => $otherDepartment->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "The specified department doesn't belong to this organization."
                ]);
    }

    public function test_can_delete_department_user()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);

        $response = $this->deleteJson("/api/department_users/{$departmentUser->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('department_users', [
            'id' => $departmentUser->id
        ]);
    }

    public function test_cannot_delete_department_user_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $otherUser->id,
            'department_id' => $otherDepartment->id
        ]);

        $response = $this->deleteJson("/api/department_users/{$departmentUser->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This department user assignment doesn't belong to this organization."
                ]);
    }

    public function test_can_get_all_department_users_with_pagination()
    {
        $targetUsers = User::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        foreach ($targetUsers as $targetUser) {
            DepartmentUser::factory()->create([
                'user_id' => $targetUser->id,
                'department_id' => $this->department->id
            ]);
        }

        $response = $this->getJson('/api/department_users');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'user_id',
                            'department_id',
                            'created_at',
                            'updated_at',
                            'user',
                            'department'
                        ]
                    ],
                    'meta' => [
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertCount(15, $responseData);

        $meta = $response->json('meta');
        $this->assertEquals(15, $meta['count']);
        $this->assertEquals(15, $meta['total']);
    }

    public function test_department_users_are_organization_scoped()
    {
        $otherOrganization = Organization::factory()->create();

        // Create department users for current organization
        $currentOrgUsers = User::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        foreach ($currentOrgUsers as $user) {
            DepartmentUser::factory()->create([
                'user_id' => $user->id,
                'department_id' => $this->department->id
            ]);
        }

        // Create department users for other organization
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherOrgUsers = User::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);
        foreach ($otherOrgUsers as $user) {
            DepartmentUser::factory()->create([
                'user_id' => $user->id,
                'department_id' => $otherDepartment->id
            ]);
        }

        $response = $this->getJson('/api/department_users');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        // Verify all returned department users belong to the current organization
        foreach ($responseData as $departmentUser) {
            $this->assertEquals($this->organization->id, $departmentUser['department']['organization_id']);
        }
    }

    public function test_handles_nonexistent_department_user_gracefully()
    {
        $response = $this->getJson('/api/department_users/999');

        $response->assertStatus(404);
    }

    public function test_handles_invalid_department_user_id_gracefully()
    {
        $response = $this->getJson('/api/department_users/invalid');

        $response->assertStatus(404);
    }

    public function test_create_department_user_with_relationships_loaded()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'first_name' => 'Test User'
        ]);

        $departmentUserData = [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ];

        $response = $this->postJson('/api/department_users', $departmentUserData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData['user']);
        $this->assertIsArray($responseData['department']);
        $this->assertEquals('Test User', $responseData['user']['first_name']);
        $this->assertEquals($this->department->name, $responseData['department']['name']);
    }

    public function test_api_returns_proper_error_format()
    {
        $response = $this->postJson('/api/department_users', []);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'code',
                    'data',
                    'errors' => [
                        'user_id',
                        'department_id'
                    ]
                ]);
    }

    public function test_api_returns_proper_success_format()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUserData = [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ];

        $response = $this->postJson('/api/department_users', $departmentUserData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Department created successfully'
                ]);
    }

    public function test_update_returns_proper_format()
    {
        $targetUser1 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $targetUser2 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser1->id,
            'department_id' => $this->department->id
        ]);

        $updateData = [
            'user_id' => $targetUser2->id
        ];

        $response = $this->putJson("/api/department_users/{$departmentUser->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Department user updated successfully'
                ]);
    }

    public function test_delete_returns_proper_format()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $departmentUser = DepartmentUser::factory()->create([
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);

        $response = $this->deleteJson("/api/department_users/{$departmentUser->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Department deleted successfully'
                ]);
    }

    public function test_can_assign_same_user_to_multiple_departments()
    {
        $targetUser = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $department2 = Department::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Assign user to first department
        $departmentUserData1 = [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ];
        $response1 = $this->postJson('/api/department_users', $departmentUserData1);
        $response1->assertStatus(200);

        // Assign same user to second department
        $departmentUserData2 = [
            'user_id' => $targetUser->id,
            'department_id' => $department2->id
        ];
        $response2 = $this->postJson('/api/department_users', $departmentUserData2);
        $response2->assertStatus(200);

        // Verify both assignments exist
        $this->assertDatabaseHas('department_users', [
            'user_id' => $targetUser->id,
            'department_id' => $this->department->id
        ]);
        $this->assertDatabaseHas('department_users', [
            'user_id' => $targetUser->id,
            'department_id' => $department2->id
        ]);
    }

    public function test_can_assign_multiple_users_to_same_department()
    {
        $targetUser1 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $targetUser2 = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Assign first user to department
        $departmentUserData1 = [
            'user_id' => $targetUser1->id,
            'department_id' => $this->department->id
        ];
        $response1 = $this->postJson('/api/department_users', $departmentUserData1);
        $response1->assertStatus(200);

        // Assign second user to same department
        $departmentUserData2 = [
            'user_id' => $targetUser2->id,
            'department_id' => $this->department->id
        ];
        $response2 = $this->postJson('/api/department_users', $departmentUserData2);
        $response2->assertStatus(200);

        // Verify both assignments exist
        $this->assertDatabaseHas('department_users', [
            'user_id' => $targetUser1->id,
            'department_id' => $this->department->id
        ]);
        $this->assertDatabaseHas('department_users', [
            'user_id' => $targetUser2->id,
            'department_id' => $this->department->id
        ]);
    }
}
