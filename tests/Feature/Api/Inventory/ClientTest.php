<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Campaign;
use App\Models\Client;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ClientTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_individual_client()
    {
        $clientData = [
            'name' => 'John Doe',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>',
            'profession' => 'Software Engineer',
            'birthdate' => '1990-01-15',
            'cpf' => '***********',
            'service' => 'Web Development',
            'address' => '123 Main Street',
            'number' => '456',
            'neighborhood' => 'Downtown',
            'cep' => '01234-567',
            'complement' => 'Apt 10',
            'civil_state' => 'single',
            'description' => 'Individual client for testing'
        ];

        $response = $this->postJson('/api/clients', $clientData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'phone',
                        'email',
                        'profession',
                        'birthdate',
                        'cpf',
                        'cnpj',
                        'service',
                        'address',
                        'number',
                        'neighborhood',
                        'cep',
                        'complement',
                        'civil_state',
                        'description',
                        'created_at',
                        'updated_at',
                        'international_transformed_phone'
                    ]
                ]);

        $this->assertDatabaseHas('clients', [
            'name' => 'John Doe',
            'organization_id' => $this->organization->id,
            'cpf' => '***********',
            'email' => '<EMAIL>'
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('John Doe', $responseData['name']);
        $this->assertEquals('***********', $responseData['cpf']);
        $this->assertNull($responseData['cnpj']);
    }

    public function test_can_create_company_client()
    {
        $clientData = [
            'name' => 'ACME Corporation',
            'phone' => '+5511888888888',
            'email' => '<EMAIL>',
            'cnpj' => '12345678000195',
            'service' => 'Technology Solutions',
            'address' => '456 Business Avenue',
            'number' => '789',
            'neighborhood' => 'Business District',
            'cep' => '98765-432',
            'complement' => 'Suite 100',
            'description' => 'Company client for testing'
        ];

        $response = $this->postJson('/api/clients', $clientData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('clients', [
            'name' => 'ACME Corporation',
            'organization_id' => $this->organization->id,
            'cnpj' => '12345678000195',
            'email' => '<EMAIL>'
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('ACME Corporation', $responseData['name']);
        $this->assertEquals('12345678000195', $responseData['cnpj']);
        $this->assertNull($responseData['cpf']);
    }

    public function test_can_create_client_with_asaas_integration()
    {
        $clientData = [
            'name' => 'John Doe',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>',
            'cpf' => '***********',
            'enable_asaas_integration' => true
        ];

        // Mock ASAAS service to avoid external API calls
        $this->mock(\App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer::class, function ($mock) {
            $mock->shouldReceive('perform')->once();
        });

        $response = $this->postJson('/api/clients', $clientData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('clients', [
            'name' => 'John Doe',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_get_client_by_id()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Client'
        ]);

        $response = $this->getJson("/api/clients/{$client->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'phone',
                        'email',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($client->id, $responseData['id']);
        $this->assertEquals('Test Client', $responseData['name']);
    }

    public function test_cannot_get_client_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $client = Client::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/clients/{$client->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_client()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '+5511777777777'
        ];

        $response = $this->putJson("/api/clients/{$client->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('clients', [
            'id' => $client->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Name', $responseData['name']);
        $this->assertEquals('<EMAIL>', $responseData['email']);
    }

    public function test_cannot_update_client_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $client = Client::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Updated Name'
        ];

        $response = $this->putJson("/api/clients/{$client->id}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_delete_client()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/clients/{$client->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('clients', [
            'id' => $client->id
        ]);
    }

    public function test_cannot_delete_client_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $client = Client::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/clients/{$client->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This client don't belong to this organization."
                ]);
    }

    public function test_can_get_all_clients_with_pagination()
    {
        Client::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/clients?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_clients_by_name()
    {
        Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'John Doe'
        ]);
        Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Jane Smith'
        ]);

        $response = $this->getJson('/api/clients?name=John');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('John Doe', $responseData['data'][0]['name']);
    }

    public function test_can_get_clients_by_campaign()
    {
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $client1 = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $client2 = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $client3 = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Attach clients to campaign
        $campaign->clients()->attach([$client1->id, $client2->id]);

        $response = $this->getJson("/api/campaigns/{$campaign->id}/clients");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);

        $clientIds = collect($responseData['data'])->pluck('id')->toArray();
        $this->assertContains($client1->id, $clientIds);
        $this->assertContains($client2->id, $clientIds);
        $this->assertNotContains($client3->id, $clientIds);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/clients', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_validation_errors_on_update()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/clients/{$client->id}", [
            'name' => '' // Empty name should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_client_creation_with_database_transaction_rollback()
    {
        // Mock repository to throw exception after client creation
        $this->mock(\App\Repositories\ClientRepository::class, function ($mock) {
            $mock->shouldReceive('store')
                 ->once()
                 ->andThrow(new \Exception('Database error'));
        });

        $clientData = [
            'name' => 'Test Client',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>'
        ];

        $response = $this->postJson('/api/clients', $clientData);

        $response->assertStatus(500);

        // Should not have created the client due to rollback
        $this->assertDatabaseMissing('clients', [
            'name' => 'Test Client',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_client_creation_handles_asaas_failure_gracefully()
    {
        // Mock ASAAS service to throw exception
        $this->mock(\App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer::class, function ($mock) {
            $mock->shouldReceive('perform')
                 ->once()
                 ->andThrow(new \App\Services\ASAAS\Exceptions\AsaasException('ASAAS API Error'));
        });

        $clientData = [
            'name' => 'Test Client',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>',
            'cpf' => '***********',
            'enable_asaas_integration' => true
        ];

        $response = $this->postJson('/api/clients', $clientData);

        // Should still succeed even if ASAAS fails
        $response->assertStatus(201);

        // Client should be created despite ASAAS failure
        $this->assertDatabaseHas('clients', [
            'name' => 'Test Client',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_client_international_phone_transformation()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'phone' => '11999999999' // Brazilian phone without country code
        ]);

        $response = $this->getJson("/api/clients/{$client->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertArrayHasKey('international_transformed_phone', $responseData);
        $this->assertStringStartsWith('+55', $responseData['international_transformed_phone']);
    }

    public function test_client_soft_delete_behavior()
    {
        $client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete the client
        $response = $this->deleteJson("/api/clients/{$client->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/clients');
        $responseData = $response->json('data.data');
        $clientNames = collect($responseData)->pluck('name')->toArray();
        $this->assertNotContains('To Be Deleted', $clientNames);

        // Should not be accessible by ID
        $response = $this->getJson("/api/clients/{$client->id}");
        $response->assertStatus(404);
    }

    public function test_client_ordering_and_filtering_combinations()
    {
        Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alice Johnson',
            'email' => '<EMAIL>',
            'created_at' => now()->subDays(3)
        ]);
        Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Bob Smith',
            'email' => '<EMAIL>',
            'created_at' => now()->subDays(1)
        ]);
        Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Brown',
            'email' => '<EMAIL>',
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/clients?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alice Johnson', $responseData[0]['name']);
        $this->assertEquals('Bob Smith', $responseData[1]['name']);
        $this->assertEquals('Charlie Brown', $responseData[2]['name']);

        // Test ordering by created_at descending (newest first)
        $response = $this->getJson('/api/clients?order=created_at&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Bob Smith', $responseData[0]['name']); // Most recent

        // Test filtering with ordering
        $response = $this->getJson('/api/clients?name=o&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Bob and Charlie contain 'o'
        $this->assertEquals('Bob Smith', $responseData[0]['name']);
        $this->assertEquals('Charlie Brown', $responseData[1]['name']);
    }

    public function test_client_data_consistency_across_operations()
    {
        // Create client
        $clientData = [
            'name' => 'Consistency Test',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>',
            'cpf' => '***********',
            'address' => '123 Test Street',
            'number' => '456',
            'neighborhood' => 'Test District',
            'cep' => '12345-678'
        ];

        $createResponse = $this->postJson('/api/clients', $clientData);
        $createResponse->assertStatus(201);
        $clientId = $createResponse->json('data.id');

        // Get client and verify data
        $getResponse = $this->getJson("/api/clients/{$clientId}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals($clientData['name'], $getData['name']);
        $this->assertEquals($clientData['email'], $getData['email']);
        $this->assertEquals($clientData['cpf'], $getData['cpf']);
        $this->assertEquals($clientData['address'], $getData['address']);

        // Update client
        $updateData = [
            'name' => 'Updated Consistency Test',
            'email' => '<EMAIL>'
        ];

        $updateResponse = $this->putJson("/api/clients/{$clientId}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/clients/{$clientId}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals($updateData['name'], $getUpdatedData['name']);
        $this->assertEquals($updateData['email'], $getUpdatedData['email']);
        // Other fields should remain unchanged
        $this->assertEquals($clientData['cpf'], $getUpdatedData['cpf']);
        $this->assertEquals($clientData['address'], $getUpdatedData['address']);
    }
}
