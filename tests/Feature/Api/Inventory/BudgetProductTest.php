<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\BudgetProduct;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Budget;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BudgetProductTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;
    private Product $product;
    private Brand $brand;
    private Budget $budget;
    private Client $client;
    private Project $project;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id
        ]);

        $this->budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'project_id' => $this->project->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_budget_product()
    {
        $budgetProductData = [
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50
        ];

        $response = $this->postJson('/api/budget-products', $budgetProductData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'budget_id',
                        'product_id',
                        'quantity',
                        'value',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals($this->budget->id, $responseData['budget_id']);
        $this->assertEquals($this->product->id, $responseData['product_id']);
        $this->assertEquals(10, $responseData['quantity']);
        $this->assertEquals(150.50, $responseData['value']);

        $this->assertDatabaseHas('budget_products', [
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'value' => 150.50
        ]);
    }

    public function test_can_get_budget_product_by_id()
    {
        $budgetProduct = BudgetProduct::factory()->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $response = $this->getJson("/api/budget-products/{$budgetProduct->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'budget_id',
                        'product_id',
                        'quantity',
                        'value',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($budgetProduct->id, $responseData['id']);
        $this->assertEquals(5, $responseData['quantity']);
        $this->assertEquals(75.25, $responseData['value']);
    }

    public function test_cannot_get_budget_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $budgetProduct = BudgetProduct::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/budget-products/{$budgetProduct->id}");

        $response->assertStatus(404);
    }

    public function test_can_delete_budget_product()
    {
        $budgetProduct = BudgetProduct::factory()->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->deleteJson("/api/budget-products/{$budgetProduct->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('budget_products', [
            'id' => $budgetProduct->id
        ]);
    }

    public function test_cannot_delete_budget_product_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $budgetProduct = BudgetProduct::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/budget-products/{$budgetProduct->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This budget product don't belong to this organization."
                ]);
    }

    public function test_can_get_all_budget_products_with_pagination()
    {
        BudgetProduct::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson('/api/budget-products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(15, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
    }

    public function test_can_get_budget_products_from_specific_budget()
    {
        $budget1 = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'project_id' => $this->project->id
        ]);
        $budget2 = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'project_id' => $this->project->id
        ]);

        BudgetProduct::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $budget1->id,
            'product_id' => $this->product->id
        ]);
        BudgetProduct::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $budget2->id,
            'product_id' => $this->product->id
        ]);

        $response = $this->getJson("/api/budgets/{$budget1->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        foreach ($responseData as $budgetProduct) {
            $this->assertEquals($budget1->id, $budgetProduct['budget_id']);
        }
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/budget-products', [
            'budget_id' => '', // Empty budget_id should fail validation
            'quantity' => 'invalid' // Invalid quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['budget_id', 'quantity']);
    }

    public function test_budget_product_soft_delete_behavior()
    {
        $budgetProduct = BudgetProduct::factory()->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id
        ]);

        // Delete the budget product
        $response = $this->deleteJson("/api/budget-products/{$budgetProduct->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/budget-products');
        $responseData = $response->json('data.data');
        $budgetProductIds = collect($responseData)->pluck('id')->toArray();
        $this->assertNotContains($budgetProduct->id, $budgetProductIds);

        // Should not be accessible by ID
        $response = $this->getJson("/api/budget-products/{$budgetProduct->id}");
        $response->assertStatus(404);
    }

    public function test_budget_product_with_zero_quantity()
    {
        $budgetProductData = [
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 0,
            'value' => 0.0
        ];

        $response = $this->postJson('/api/budget-products', $budgetProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(0, $responseData['quantity']);
        $this->assertEquals(0.0, $responseData['value']);
    }

    public function test_budget_product_with_high_quantity()
    {
        $budgetProductData = [
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 999999,
            'value' => 99999999.99
        ];

        $response = $this->postJson('/api/budget-products', $budgetProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(999999, $responseData['quantity']);
        $this->assertEquals(99999999.99, $responseData['value']);
    }

    public function test_budget_product_with_decimal_values()
    {
        $budgetProductData = [
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id,
            'quantity' => 33,
            'value' => 123.456789
        ];

        $response = $this->postJson('/api/budget-products', $budgetProductData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals(33, $responseData['quantity']);
        $this->assertEquals(123.456789, $responseData['value']);
    }

    public function test_budget_products_organization_isolation()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        // Create budget products for both organizations
        BudgetProduct::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $this->product->id
        ]);

        $otherBudget = Budget::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);
        $otherProduct = Product::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        BudgetProduct::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id,
            'budget_id' => $otherBudget->id,
            'product_id' => $otherProduct->id
        ]);

        // Current user should only see their organization's budget products
        $response = $this->getJson('/api/budget-products');
        $responseData = $response->json('data');
        $this->assertEquals(3, $responseData['count']);

        // Switch to other user
        Sanctum::actingAs($otherUser);

        // Other user should only see their organization's budget products
        $response = $this->getJson('/api/budget-products');
        $responseData = $response->json('data');
        $this->assertEquals(2, $responseData['count']);
    }

    public function test_budget_products_from_budget_with_multiple_products()
    {
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $this->brand->id
        ]);

        BudgetProduct::factory()->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $product1->id,
            'quantity' => 10,
            'value' => 150.50
        ]);
        BudgetProduct::factory()->create([
            'organization_id' => $this->organization->id,
            'budget_id' => $this->budget->id,
            'product_id' => $product2->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $response = $this->getJson("/api/budgets/{$this->budget->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);

        // Verify different products
        $productIds = collect($responseData)->pluck('product_id')->toArray();
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }

    public function test_budget_products_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            $budgetProductData = [
                'budget_id' => $this->budget->id,
                'product_id' => $this->product->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ];

            $response = $this->postJson('/api/budget-products', $budgetProductData);

            $response->assertStatus(201);

            $responseData = $response->json('data');
            $this->assertEquals($testCase['quantity'], $responseData['quantity']);
            $this->assertEquals($testCase['value'], $responseData['value']);

            $this->assertDatabaseHas('budget_products', [
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }
    }

    public function test_empty_budget_products_endpoint()
    {
        $emptyBudget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'project_id' => $this->project->id
        ]);

        $response = $this->getJson("/api/budgets/{$emptyBudget->id}/products");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertEmpty($responseData);
    }
}
