<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Message;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage;
use Illuminate\Support\Facades\Config;

class WhatsAppWebhookApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_webhook_verification_succeeds_with_correct_token()
    {
        // Arrange
        Config::set('whatsapp.webhook_verify_token', 'test_verify_token');

        // Act
        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => 'test_verify_token',
            'hub_challenge' => '12345'
        ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(12345, $response->json());
    }

    public function test_webhook_verification_fails_with_incorrect_token()
    {
        // Arrange
        Config::set('whatsapp.webhook_verify_token', 'correct_token');

        // Act
        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => 'wrong_token',
            'hub_challenge' => '12345'
        ]));

        // Assert
        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);
    }

    public function test_webhook_handles_status_update_successfully()
    {
        // Arrange - Create required data
        $message = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage = WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.HBgMNTU5...',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        $webhookData = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.HBgMNTU5...',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999',
                                        'conversation' => [
                                            'id' => 'conversation_id',
                                            'origin' => ['type' => 'user_initiated']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ])
            ->assertJsonStructure([
                'status',
                'processed_messages',
                'processed_statuses',
                'message_results',
                'status_results'
            ]);

        // Verify webhook entry was created
        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage->id,
            'external_wam_id' => 'wamid.HBgMNTU5...',
            'status' => 'delivered',
            'timestamp' => '1659463500',
            'recipient_id' => '559999999999',
            'conversation_id' => 'conversation_id',
            'conversation_origin_type' => 'user_initiated'
        ]);
    }

    public function test_webhook_handles_nonexistent_message_gracefully()
    {
        // Arrange
        $webhookData = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.NONEXISTENT',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ])
            ->assertJsonStructure([
                'status',
                'processed_messages',
                'processed_statuses',
                'message_results',
                'status_results'
            ]);

        // Verify no webhook entry was created
        $this->assertDatabaseMissing('whatsapp_webhook_entries', [
            'external_wam_id' => 'wamid.NONEXISTENT'
        ]);
    }

    public function test_webhook_handles_multiple_status_updates()
    {
        // Arrange - Create required data
        $message1 = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message 1',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $message2 = Message::create([
            'organization_id' => 1,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message 2',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage1 = WhatsAppMessage::create([
            'message_id' => $message1->id,
            'whatsapp_message_id' => 'wamid.FIRST',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data1"}'
        ]);

        $whatsappMessage2 = WhatsAppMessage::create([
            'message_id' => $message2->id,
            'whatsapp_message_id' => 'wamid.SECOND',
            'message_status' => 'sent',
            'wa_id' => '559999999999',
            'input_phone' => '+559999999999',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data2"}'
        ]);

        $webhookData = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                'statuses' => [
                                    [
                                        'id' => 'wamid.FIRST',
                                        'status' => 'delivered',
                                        'timestamp' => '1659463500',
                                        'recipient_id' => '559999999999'
                                    ],
                                    [
                                        'id' => 'wamid.SECOND',
                                        'status' => 'read',
                                        'timestamp' => '1659463600',
                                        'recipient_id' => '559999999999'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ])
            ->assertJsonStructure([
                'status',
                'processed_messages',
                'processed_statuses',
                'message_results',
                'status_results'
            ]);

        // Verify both webhook entries were created
        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage1->id,
            'external_wam_id' => 'wamid.FIRST',
            'status' => 'delivered'
        ]);

        $this->assertDatabaseHas('whatsapp_webhook_entries', [
            'whatsapp_message_id' => $whatsappMessage2->id,
            'external_wam_id' => 'wamid.SECOND',
            'status' => 'read'
        ]);
    }

    public function test_webhook_handles_empty_entry_gracefully()
    {
        // Arrange
        $webhookData = [
            'entry' => []
        ];

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ])
            ->assertJsonStructure([
                'status',
                'processed_messages',
                'processed_statuses',
                'message_results',
                'status_results'
            ]);
    }

    public function test_webhook_handles_malformed_data_gracefully()
    {
        // Arrange
        $webhookData = [
            'entry' => [
                [
                    'changes' => [
                        [
                            'value' => [
                                // Missing statuses array
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success'
            ])
            ->assertJsonStructure([
                'status',
                'processed_messages',
                'processed_statuses',
                'message_results',
                'status_results'
            ]);
    }
}
