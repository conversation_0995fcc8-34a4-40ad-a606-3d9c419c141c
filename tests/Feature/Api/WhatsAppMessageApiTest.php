<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Message;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage;

class WhatsAppMessageApiTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>'
        ]);

        $this->user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_create_whatsapp_message()
    {
        // Arrange
        $message = Message::create([
            'organization_id' => $this->organization->id,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $data = [
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/whatsapp_messages', $data);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'WhatsApp message created successfully'
            ]);

        $this->assertDatabaseHas('whatsapp_messages', [
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp'
        ]);
    }

    public function test_can_list_whatsapp_messages()
    {
        // Arrange
        $message = Message::create([
            'organization_id' => $this->organization->id,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/whatsapp_messages');

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'WhatsApp messages retrieved successfully'
            ])
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'message_id',
                        'whatsapp_message_id',
                        'message_status',
                        'wa_id',
                        'input_phone',
                        'messaging_product',
                        'json',
                        'created_at',
                        'updated_at'
                    ]
                ]
            ]);
    }

    public function test_can_show_specific_whatsapp_message()
    {
        // Arrange
        $message = Message::create([
            'organization_id' => $this->organization->id,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage = WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/whatsapp_messages/{$whatsappMessage->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'WhatsApp message retrieved successfully',
                'data' => [
                    'id' => $whatsappMessage->id,
                    'message_id' => $message->id,
                    'whatsapp_message_id' => 'wamid.123456789',
                    'message_status' => 'accepted',
                    'wa_id' => '557991514957',
                    'input_phone' => '+5579991514957',
                    'messaging_product' => 'whatsapp'
                ]
            ]);
    }

    public function test_can_update_whatsapp_message()
    {
        // Arrange
        $message = Message::create([
            'organization_id' => $this->organization->id,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage = WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        $updateData = [
            'message_status' => 'delivered'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/whatsapp_messages/{$whatsappMessage->id}", $updateData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'WhatsApp message updated successfully',
                'data' => [
                    'message_status' => 'delivered'
                ]
            ]);

        $this->assertDatabaseHas('whatsapp_messages', [
            'id' => $whatsappMessage->id,
            'message_status' => 'delivered'
        ]);
    }

    public function test_can_delete_whatsapp_message()
    {
        // Arrange
        $message = Message::create([
            'organization_id' => $this->organization->id,
            'campaign_id' => null,
            'template_id' => null,
            'client_id' => null,
            'message' => 'Test message',
            'status' => 1,
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
        ]);

        $whatsappMessage = WhatsAppMessage::create([
            'message_id' => $message->id,
            'whatsapp_message_id' => 'wamid.123456789',
            'message_status' => 'accepted',
            'wa_id' => '557991514957',
            'input_phone' => '+5579991514957',
            'messaging_product' => 'whatsapp',
            'json' => '{"test": "data"}'
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/whatsapp_messages/{$whatsappMessage->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'WhatsApp message deleted successfully'
            ]);

        $this->assertSoftDeleted('whatsapp_messages', [
            'id' => $whatsappMessage->id
        ]);
    }
}
