<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\WhatsAppWebhookLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WhatsAppWebhookControllerSignatureTest extends TestCase
{
    use RefreshDatabase;

    public function test_handle_succeeds_with_valid_signature()
    {
        $secret = 'test_webhook_secret_123';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'test_phone_123',
            'is_active' => true,
        ]);

        $payload = json_encode([
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_123',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'test_phone_123'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Hello']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]);

        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true), [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'processed',
            'results'
        ]);
    }

    public function test_handle_fails_with_invalid_signature()
    {
        $secret = 'test_webhook_secret_456';
        config(['whatsapp.webhook_secret' => $secret]);

        $payload = json_encode([
            'object' => 'whatsapp_business_account',
            'entry' => []
        ]);

        $invalidSignature = 'sha256=invalid_signature_hash';

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true), [
            'X-Hub-Signature-256' => $invalidSignature
        ]);

        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);

        // Verify security log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'security',
            'processing_status' => 'failed',
            'error_message' => 'Invalid signature',
        ]);

        $log = WhatsAppWebhookLog::where('event_type', 'security')->first();
        $this->assertStringStartsWith('sha256=invalid_signa...', $log->webhook_payload['signature']);
    }

    public function test_handle_fails_with_missing_signature()
    {
        $secret = 'test_webhook_secret_789';
        config(['whatsapp.webhook_secret' => $secret]);

        $payload = json_encode([
            'object' => 'whatsapp_business_account',
            'entry' => []
        ]);

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true));

        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);

        // Verify security log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'security',
            'processing_status' => 'failed',
            'error_message' => 'Invalid signature',
        ]);

        $log = WhatsAppWebhookLog::where('event_type', 'security')->first();
        $this->assertEquals('missing', $log->webhook_payload['signature']);
    }

    public function test_handle_fails_with_missing_secret_config()
    {
        config(['whatsapp.webhook_secret' => null]);

        $payload = json_encode([
            'object' => 'whatsapp_business_account',
            'entry' => []
        ]);

        $signature = 'sha256=some_signature_hash';

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true), [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);

        // Verify security log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'event_type' => 'security',
            'processing_status' => 'failed',
            'error_message' => 'Invalid signature',
        ]);
    }

    public function test_handle_validates_signature_before_payload()
    {
        $secret = 'test_webhook_secret_order';
        config(['whatsapp.webhook_secret' => $secret]);

        // Invalid payload structure but valid signature
        $payload = json_encode(['invalid' => 'structure']);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true), [
            'X-Hub-Signature-256' => $signature
        ]);

        // Should fail at payload validation (400), not signature validation (403)
        $response->assertStatus(400);
        $response->assertJson(['error' => 'Invalid webhook data']);

        // Should have payload validation log, not security log
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'event_type' => 'other',
            'processing_status' => 'failed',
            'error_message' => 'Invalid webhook data',
        ]);

        // Should NOT have security log
        $this->assertDatabaseMissing('whatsapp_webhook_logs', [
            'event_type' => 'security',
        ]);
    }

    public function test_handle_logs_signature_attempts_with_masked_values()
    {
        $secret = 'test_webhook_secret_logging';
        config(['whatsapp.webhook_secret' => $secret]);

        $payload = json_encode(['test' => 'data']);
        $longSignature = 'sha256=very_long_signature_hash_that_should_be_truncated_for_security_logging_purposes';

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($payload, true), [
            'X-Hub-Signature-256' => $longSignature
        ]);

        $response->assertStatus(403);

        $log = WhatsAppWebhookLog::where('event_type', 'security')->first();

        // Verify signature is truncated to 20 chars + "..."
        $this->assertEquals('sha256=very_long_sig...', $log->webhook_payload['signature']);
        $this->assertStringNotContainsString('truncated_for_security', $log->webhook_payload['signature']);
    }

    public function test_handle_works_with_complex_payload_and_signature()
    {
        $secret = 'complex_webhook_secret_test';
        config(['whatsapp.webhook_secret' => $secret]);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'complex_phone_456',
            'is_active' => true,
        ]);

        $complexPayload = json_encode([
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_456',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'complex_phone_456',
                                    'display_phone_number' => '+55 11 99999-9999'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'complex_msg_789',
                                        'from' => '+*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Complex message with special chars: áéíóú 🌍'],
                                        'context' => ['id' => 'reply_to_msg_123']
                                    ]
                                ],
                                'contacts' => [
                                    [
                                        'profile' => ['name' => 'João Silva'],
                                        'wa_id' => '+*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]);

        $signature = 'sha256=' . hash_hmac('sha256', $complexPayload, $secret);

        $response = $this->postJson('/api/whatsapp/webhook', json_decode($complexPayload, true), [
            'X-Hub-Signature-256' => $signature
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'processed',
            'results'
        ]);

        // Verify no security logs (signature was valid)
        $this->assertDatabaseMissing('whatsapp_webhook_logs', [
            'event_type' => 'security',
        ]);
    }
}
