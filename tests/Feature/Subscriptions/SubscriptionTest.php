<?php

namespace Tests\Feature\Subscriptions;

use App\Domains\Subscription;
use App\Factories\SubscriptionFactory;
use App\Models\Organization;
use App\Models\Subscription as SubscriptionModel;
use App\Repositories\SubscriptionRepository;
use App\UseCases\Subscriptions\DeleteSubscription;
use App\UseCases\Subscriptions\GetAllSubscriptions;
use App\UseCases\Subscriptions\GetSubscription;
use App\UseCases\Subscriptions\StoreSubscription;
use App\UseCases\Subscriptions\UpdateSubscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionTest extends TestCase
{
    use RefreshDatabase;

    private SubscriptionFactory $factory;
    private SubscriptionRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new SubscriptionFactory();
        $this->repository = new SubscriptionRepository($this->factory);
    }

    /** @test */
    public function it_can_create_subscription_model()
    {
        $organization = Organization::factory()->create();
        
        $subscription = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->create();

        $this->assertDatabaseHas('subscriptions', [
            'id' => $subscription->id,
            'organization_id' => $organization->id,
        ]);
    }

    /** @test */
    public function it_can_build_domain_from_model()
    {
        $organization = Organization::factory()->create();
        $model = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->active()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(Subscription::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->status, $domain->status);
        $this->assertTrue($domain->isActive());
    }

    /** @test */
    public function it_can_create_courtesy_subscription()
    {
        $organization = Organization::factory()->create();
        $model = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->courtesy()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->is_courtesy);
        $this->assertTrue($domain->isInCourtesy());
        $this->assertEquals(0.0, $domain->value);
        $this->assertEquals('UNDEFINED', $domain->billing_type);
    }

    /** @test */
    public function it_can_create_trial_subscription()
    {
        $organization = Organization::factory()->create();
        $model = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->trial()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertTrue($domain->is_trial);
        $this->assertTrue($domain->isInTrial());
        $this->assertEquals(0.0, $domain->value);
        $this->assertEquals('UNDEFINED', $domain->billing_type);
    }

    /** @test */
    public function it_can_create_paid_subscription()
    {
        $organization = Organization::factory()->create();
        $model = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->paid()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertFalse($domain->is_courtesy);
        $this->assertFalse($domain->is_trial);
        $this->assertGreaterThan(0, $domain->value);
        $this->assertNotEquals('UNDEFINED', $domain->billing_type);
    }

    /** @test */
    public function it_can_store_subscription_through_use_case()
    {
        $organization = Organization::factory()->create();
        $storeUseCase = new StoreSubscription($this->repository, $this->factory);

        $data = [
            'organization_id' => $organization->id,
            'status' => 'ACTIVE',
            'billing_type' => 'BOLETO',
            'cycle' => 'MONTHLY',
            'value' => 99.90,
        ];

        $subscription = $storeUseCase->perform($data);

        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals($organization->id, $subscription->organization_id);
        $this->assertEquals('ACTIVE', $subscription->status);
        $this->assertEquals(99.90, $subscription->value);
        
        $this->assertDatabaseHas('subscriptions', [
            'organization_id' => $organization->id,
            'status' => 'ACTIVE',
            'value' => 99.90,
        ]);
    }

    /** @test */
    public function it_can_store_courtesy_subscription()
    {
        $organization = Organization::factory()->create();
        $storeUseCase = new StoreSubscription($this->repository, $this->factory);

        $data = [
            'organization_id' => $organization->id,
            'is_courtesy' => true,
            'courtesy_expires_at' => now()->addDays(30),
            'courtesy_reason' => 'Cliente especial',
        ];

        $subscription = $storeUseCase->perform($data);

        $this->assertTrue($subscription->is_courtesy);
        $this->assertTrue($subscription->isInCourtesy());
        $this->assertEquals('Cliente especial', $subscription->courtesy_reason);
        $this->assertEquals(0.0, $subscription->value);
    }

    /** @test */
    public function it_can_update_subscription()
    {
        $model = SubscriptionModel::factory()->create(['value' => 50.0]);
        $updateUseCase = new UpdateSubscription($this->repository, $this->factory);

        $updatedSubscription = $updateUseCase->perform($model->id, [
            'value' => 99.90,
            'status' => 'SUSPENDED',
        ]);

        $this->assertEquals(99.90, $updatedSubscription->value);
        $this->assertEquals('SUSPENDED', $updatedSubscription->status);
        $this->assertTrue($updatedSubscription->isSuspended());
    }

    /** @test */
    public function it_can_get_subscription()
    {
        $model = SubscriptionModel::factory()->create();
        $getUseCase = new GetSubscription($this->repository);

        $subscription = $getUseCase->perform($model->id);

        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals($model->id, $subscription->id);
    }

    /** @test */
    public function it_can_get_subscription_by_organization()
    {
        $organization = Organization::factory()->create();
        $model = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->create();
        
        $getUseCase = new GetSubscription($this->repository);

        $subscription = $getUseCase->performByOrganization($organization->id);

        $this->assertEquals($organization->id, $subscription->organization_id);
    }

    /** @test */
    public function it_can_delete_subscription()
    {
        $model = SubscriptionModel::factory()->inactive()->create();
        $deleteUseCase = new DeleteSubscription($this->repository);

        $result = $deleteUseCase->perform($model->id);

        $this->assertTrue($result);
        $this->assertSoftDeleted('subscriptions', ['id' => $model->id]);
    }

    /** @test */
    public function it_can_cancel_subscription()
    {
        $model = SubscriptionModel::factory()->active()->create();
        $deleteUseCase = new DeleteSubscription($this->repository);

        $result = $deleteUseCase->cancel($model->id, 'Cliente solicitou cancelamento');

        $this->assertTrue($result);
        $this->assertDatabaseHas('subscriptions', [
            'id' => $model->id,
            'status' => 'CANCELLED',
        ]);
    }

    /** @test */
    public function it_can_get_all_subscriptions()
    {
        SubscriptionModel::factory()->count(5)->create();
        $getAllUseCase = new GetAllSubscriptions($this->repository);

        $result = $getAllUseCase->perform();

        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('meta', $result);
        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['meta']['total']);
    }

    /** @test */
    public function it_can_get_subscription_statistics()
    {
        SubscriptionModel::factory()->active()->count(3)->create();
        SubscriptionModel::factory()->expired()->count(2)->create();
        SubscriptionModel::factory()->courtesy()->count(1)->create();
        
        $getAllUseCase = new GetAllSubscriptions($this->repository);

        $stats = $getAllUseCase->getStatistics();

        $this->assertArrayHasKey('totals', $stats);
        $this->assertArrayHasKey('percentages', $stats);
        $this->assertEquals(6, $stats['totals']['total']);
        $this->assertEquals(3, $stats['totals']['active']);
        $this->assertEquals(2, $stats['totals']['expired']);
        $this->assertEquals(1, $stats['totals']['courtesy']);
    }

    /** @test */
    public function organization_model_has_subscription_relationship()
    {
        $organization = Organization::factory()->create();
        $subscription = SubscriptionModel::factory()
            ->forOrganization($organization)
            ->create();

        $organization->refresh();

        $this->assertInstanceOf(SubscriptionModel::class, $organization->subscription);
        $this->assertEquals($subscription->id, $organization->subscription->id);
        $this->assertTrue($organization->hasActiveSubscription());
    }

    /** @test */
    public function it_validates_subscription_access_logic()
    {
        // Active subscription
        $activeModel = SubscriptionModel::factory()->active()->create();
        $activeDomain = $this->factory->buildFromModel($activeModel);
        $this->assertTrue($activeDomain->canAccessSystem());

        // Expired subscription
        $expiredModel = SubscriptionModel::factory()->expired()->create();
        $expiredDomain = $this->factory->buildFromModel($expiredModel);
        $this->assertFalse($expiredDomain->canAccessSystem());

        // Courtesy subscription
        $courtesyModel = SubscriptionModel::factory()->courtesy()->create();
        $courtesyDomain = $this->factory->buildFromModel($courtesyModel);
        $this->assertTrue($courtesyDomain->canAccessSystem());

        // Trial subscription
        $trialModel = SubscriptionModel::factory()->trial()->create();
        $trialDomain = $this->factory->buildFromModel($trialModel);
        $this->assertTrue($trialDomain->canAccessSystem());
    }

    /** @test */
    public function it_calculates_effective_value_with_discount()
    {
        // Percentage discount
        $model = SubscriptionModel::factory()->create([
            'value' => 100.0,
            'discount_value' => 10.0,
            'discount_type' => 'PERCENTAGE',
        ]);
        $domain = $this->factory->buildFromModel($model);
        $this->assertEquals(90.0, $domain->getEffectiveValue());

        // Fixed discount
        $model2 = SubscriptionModel::factory()->create([
            'value' => 100.0,
            'discount_value' => 15.0,
            'discount_type' => 'FIXED',
        ]);
        $domain2 = $this->factory->buildFromModel($model2);
        $this->assertEquals(85.0, $domain2->getEffectiveValue());
    }
}
