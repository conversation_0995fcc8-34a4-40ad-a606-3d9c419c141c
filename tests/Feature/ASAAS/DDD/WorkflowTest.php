<?php

namespace Tests\Feature\ASAAS\DDD;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Organization as OrganizationModel;
use App\Models\Client as ClientModel;
use App\Models\Sale as SaleModel;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasSale;
use App\Factories\ASAAS\OrganizationFactory as AsaasOrganizationFactory;
use App\Factories\ASAAS\ClientFactory;
use App\Factories\ASAAS\SaleFactory;
use App\Factories\UserFactory;
use App\Factories\OrganizationFactory;
use App\Factories\Inventory\ShopFactory;
use App\Enums\AsaasEnvironment;
use App\Enums\SubscriptionStatus;
use App\Enums\PaymentStatus;

class WorkflowTest extends TestCase
{
    use RefreshDatabase;

    private AsaasOrganizationFactory $organizationFactory;
    private ClientFactory $clientFactory;
    private SaleFactory $saleFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organizationFactory = new AsaasOrganizationFactory();
        $this->clientFactory = new ClientFactory();
        $this->saleFactory = new SaleFactory(
            new UserFactory(new OrganizationFactory()),
            new ShopFactory(),
            $this->clientFactory
        );
    }

    public function test_complete_asaas_workflow_with_domains()
    {
        // 1. Create organization with ASAAS integration
        $organizationModel = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $asaasOrganizationModel = AsaasOrganization::factory()->create([
            'organization_id' => $organizationModel->id,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'api_key_123',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 99.90,
            'is_courtesy' => false,
        ]);

        // Load relationship and build domain
        $organizationModel->load('asaas');
        $organization = $this->organizationFactory->buildFromModel($organizationModel);

        // 2. Verify organization domain
        $this->assertNotNull($organization);
        $this->assertTrue($organization->hasAsaasIntegration());
        $this->assertTrue($organization->canAccessSystem());
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertEquals('acc_123456', $organization->asaas_account_id);
        $this->assertEquals(AsaasEnvironment::SANDBOX, $organization->asaas_environment);

        // 3. Create client with ASAAS integration
        $clientModel = ClientModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'phone' => '***********',
            'cpf' => '***********',
        ]);

        $asaasClientModel = AsaasClient::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
            'asaas_customer_id' => 'cus_123456',
        ]);

        // Load relationship and build domain
        $clientModel->load('asaas');
        $client = $this->clientFactory->buildFromModel($clientModel);

        // 4. Verify client domain
        $this->assertNotNull($client);
        $this->assertTrue($client->hasAsaasIntegration());
        $this->assertTrue($client->isValidForAsaas());
        $this->assertEquals('Test Client', $client->name);
        $this->assertEquals('cus_123456', $client->asaas_customer_id);
        $this->assertEquals('***********', $client->getDocument());

        // 5. Create sale with ASAAS payment
        $saleModel = SaleModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
            'total_value' => 150.75,
        ]);

        $asaasSaleModel = AsaasSale::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
            'sale_id' => $saleModel->id,
            'asaas_payment_id' => 'pay_123456',
            'payment_status' => PaymentStatus::RECEIVED,
            'billing_type' => 'BOLETO',
            'net_value' => 145.75,
            'original_value' => 150.75,
        ]);

        // Load relationship and build domain
        $saleModel->load('asaas');
        $sale = $this->saleFactory->buildFromModel($saleModel);

        // 6. Verify sale domain
        $this->assertNotNull($sale);
        $this->assertTrue($sale->hasAsaasPayment());
        $this->assertTrue($sale->isPaid());
        $this->assertFalse($sale->isPending());
        $this->assertFalse($sale->isOverdue());
        $this->assertEquals(150.75, $sale->total_value);
        $this->assertEquals('pay_123456', $sale->asaas_payment_id);
        $this->assertEquals(PaymentStatus::RECEIVED, $sale->payment_status);

        // 7. Test domain business logic
        $organizationSummary = $organization->getSubscriptionSummary();
        $this->assertTrue($organizationSummary['has_integration']);
        $this->assertTrue($organizationSummary['can_access']);
        $this->assertEquals('active', $organizationSummary['status']);

        $clientData = $client->getAsaasCustomerData();
        $this->assertEquals('Test Client', $clientData['name']);
        $this->assertEquals('<EMAIL>', $clientData['email']);
        $this->assertEquals('+55***********', $clientData['phone']);
        $this->assertEquals('***********', $clientData['cpfCnpj']);

        $paymentSummary = $sale->getPaymentSummary();
        $this->assertTrue($paymentSummary['has_payment']);
        $this->assertTrue($paymentSummary['is_paid']);
        $this->assertEquals('received', $paymentSummary['status']);
        $this->assertEquals('BOLETO', $paymentSummary['billing_type']);
    }

    public function test_organization_without_asaas_integration()
    {
        $organizationModel = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);

        $this->assertNotNull($organization);
        $this->assertFalse($organization->hasAsaasIntegration());
        $this->assertFalse($organization->canAccessSystem());
        $this->assertNull($organization->asaas_account_id);
        $this->assertNull($organization->asaas_environment);

        $summary = $organization->getSubscriptionSummary();
        $this->assertFalse($summary['has_integration']);
        $this->assertFalse($summary['can_access']);
    }

    public function test_client_without_asaas_integration()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $clientModel = ClientModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpf' => '***********',
        ]);

        $client = $this->clientFactory->buildFromModel($clientModel);

        $this->assertNotNull($client);
        $this->assertFalse($client->hasAsaasIntegration());
        $this->assertTrue($client->isValidForAsaas());
        $this->assertTrue($client->needsAsaasSync());
        $this->assertNull($client->asaas_customer_id);
    }

    public function test_sale_without_asaas_payment()
    {
        $organizationModel = OrganizationModel::factory()->create();
        $clientModel = ClientModel::factory()->create([
            'organization_id' => $organizationModel->id,
        ]);
        $saleModel = SaleModel::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
            'total_value' => 100.00,
        ]);

        $sale = $this->saleFactory->buildFromModel($saleModel);

        $this->assertNotNull($sale);
        $this->assertFalse($sale->hasAsaasPayment());
        $this->assertFalse($sale->isPaid());
        $this->assertFalse($sale->isPending());
        $this->assertFalse($sale->isOverdue());
        $this->assertTrue($sale->needsAsaasSync());
        $this->assertNull($sale->asaas_payment_id);

        $summary = $sale->getPaymentSummary();
        $this->assertFalse($summary['has_payment']);
        $this->assertFalse($summary['is_paid']);
    }

    public function test_factory_aggregator_usage()
    {
        $organizationModel = OrganizationModel::factory()->create();

        // Test that factories can be used independently
        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $this->assertNotNull($organization);

        // Test store array building
        $storeData = [
            'name' => 'New Organization',
            'description' => 'New Description',
        ];
        $newOrganization = $this->organizationFactory->buildFromStoreArray($storeData);
        $this->assertNotNull($newOrganization);
        $this->assertEquals('New Organization', $newOrganization->name);
        $this->assertNull($newOrganization->id);
    }
}
