<?php

namespace Tests\Feature\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use App\Factories\ChatBot\ButtonFactory;
use Tests\TestCase;

class ButtonInternalDataTest extends TestCase
{
    private ButtonFactory $buttonFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->buttonFactory = new ButtonFactory();
    }

    public function test_extract_internal_data_from_url_button_array()
    {
        $buttonArray = [
            'type' => 'URL',
            'text' => '📱 Ver Cardápio',
            'url' => 'https://pizzaexpress.com.br/cardapio'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);

        $this->assertEquals('https://pizzaexpress.com.br/cardapio', $internalData);
    }

    public function test_extract_internal_data_from_phone_number_button_array()
    {
        $buttonArray = [
            'type' => 'PHONE_NUMBER',
            'text' => '📞 Ligar Agora',
            'phone_number' => '+5511999887766'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);

        $this->assertEquals('+5511999887766', $internalData);
    }

    public function test_extract_internal_data_case_insensitive_type()
    {
        // Test lowercase type
        $buttonArray1 = [
            'type' => 'url',
            'text' => 'Visit Website',
            'url' => 'https://example.com'
        ];

        $internalData1 = Button::extractInternalDataFromButtonArray($buttonArray1);
        $this->assertEquals('https://example.com', $internalData1);

        // Test mixed case type
        $buttonArray2 = [
            'type' => 'Phone_Number',
            'text' => 'Call Us',
            'phone_number' => '+1234567890'
        ];

        $internalData2 = Button::extractInternalDataFromButtonArray($buttonArray2);
        $this->assertEquals('+1234567890', $internalData2);
    }

    public function test_extract_internal_data_case_insensitive_keys()
    {
        // Test uppercase URL key
        $buttonArray1 = [
            'type' => 'URL',
            'text' => 'Visit Website',
            'URL' => 'https://example.com'
        ];

        $internalData1 = Button::extractInternalDataFromButtonArray($buttonArray1);
        $this->assertEquals('https://example.com', $internalData1);

        // Test different phone number key variations
        $buttonArray2 = [
            'type' => 'PHONE_NUMBER',
            'text' => 'Call Us',
            'PHONE_NUMBER' => '+1234567890'
        ];

        $internalData2 = Button::extractInternalDataFromButtonArray($buttonArray2);
        $this->assertEquals('+1234567890', $internalData2);

        $buttonArray3 = [
            'type' => 'PHONE_NUMBER',
            'text' => 'Call Us',
            'phoneNumber' => '+0987654321'
        ];

        $internalData3 = Button::extractInternalDataFromButtonArray($buttonArray3);
        $this->assertEquals('+0987654321', $internalData3);
    }

    public function test_extract_internal_data_quick_reply_returns_null()
    {
        $buttonArray = [
            'type' => 'QUICK_REPLY',
            'text' => 'Yes',
            'callback_data' => 'yes_response'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);

        $this->assertNull($internalData);
    }

    public function test_extract_internal_data_unknown_type_returns_null()
    {
        $buttonArray = [
            'type' => 'UNKNOWN_TYPE',
            'text' => 'Unknown Button',
            'some_data' => 'some_value'
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);

        $this->assertNull($internalData);
    }

    public function test_extract_internal_data_missing_required_key_returns_null()
    {
        $buttonArray = [
            'type' => 'URL',
            'text' => 'Visit Website'
            // Missing 'url' key
        ];

        $internalData = Button::extractInternalDataFromButtonArray($buttonArray);

        $this->assertNull($internalData);
    }

    public function test_build_from_save_full_button_extracts_internal_data()
    {
        $buttonArray = [
            'type' => 'URL',
            'text' => '📱 Ver Cardápio',
            'url' => 'https://pizzaexpress.com.br/cardapio',
            'callback_data' => ['action' => 'view_menu']
        ];

        $button = $this->buttonFactory->buildFromSaveFullButton(
            $buttonArray,
            json_encode($buttonArray),
            1,
            null
        );

        $this->assertEquals('https://pizzaexpress.com.br/cardapio', $button->internal_data);
        $this->assertEquals('URL', $button->type);
        $this->assertEquals('📱 Ver Cardápio', $button->text);
    }

    public function test_build_from_save_full_button_phone_number()
    {
        $buttonArray = [
            'type' => 'PHONE_NUMBER',
            'text' => '📞 Ligar Agora',
            'phone_number' => '+5511999887766',
            'callback_data' => ['action' => 'call_now']
        ];

        $button = $this->buttonFactory->buildFromSaveFullButton(
            $buttonArray,
            json_encode($buttonArray),
            1,
            null
        );

        $this->assertEquals('+5511999887766', $button->internal_data);
        $this->assertEquals('PHONE_NUMBER', $button->type);
        $this->assertEquals('📞 Ligar Agora', $button->text);
    }

    public function test_get_internal_data_for_whatsapp_uses_internal_data_first()
    {
        $button = new Button(
            1,
            1,
            'Test Button',
            'URL',
            null,
            'https://example.com',
            json_encode(['url' => 'https://fallback.com']),
            json_encode(['test' => true])
        );

        $internalData = $button->getInternalDataForWhatsApp();

        $this->assertEquals('https://example.com', $internalData);
    }

    public function test_get_internal_data_for_whatsapp_fallback_to_callback_data()
    {
        $button = new Button(
            1,
            1,
            'Test Button',
            'URL',
            null,
            null, // No internal_data
            json_encode(['url' => 'https://fallback.com']),
            json_encode(['test' => true])
        );

        $internalData = $button->getInternalDataForWhatsApp();

        $this->assertEquals('https://fallback.com', $internalData);
    }

    public function test_to_url_whatsapp_payload_uses_internal_data()
    {
        $button = new Button(
            1,
            1,
            '📱 Ver Cardápio',
            'URL',
            null,
            'https://pizzaexpress.com.br/cardapio',
            json_encode(['url' => 'https://fallback.com']),
            json_encode(['test' => true])
        );

        $payload = $button->toUrlWhatsAppPayload();

        $this->assertEquals([
            'type' => 'URL',
            'text' => '📱 Ver Cardápio',
            'url' => 'https://pizzaexpress.com.br/cardapio'
        ], $payload);
    }

    public function test_to_call_whatsapp_payload_uses_internal_data()
    {
        $button = new Button(
            1,
            1,
            '📞 Ligar Agora',
            'PHONE_NUMBER',
            null,
            '+5511999887766',
            json_encode(['phone_number' => '+5511000000000']),
            json_encode(['test' => true])
        );

        $payload = $button->toCallWhatsAppPayload();

        $this->assertEquals([
            'type' => 'PHONE_NUMBER',
            'text' => '📞 Ligar Agora',
            'phone_number' => '+5511999887766'
        ], $payload);
    }

    public function test_to_array_includes_internal_data()
    {
        $button = new Button(
            1,
            1,
            'Test Button',
            'URL',
            'action',
            'https://example.com',
            json_encode(['test' => 'data']),
            json_encode(['test' => true])
        );

        $array = $button->toArray();

        $this->assertArrayHasKey('internal_data', $array);
        $this->assertEquals('https://example.com', $array['internal_data']);
    }

    public function test_to_store_array_includes_internal_data()
    {
        $button = new Button(
            1,
            1,
            'Test Button',
            'URL',
            'action',
            'https://example.com',
            json_encode(['test' => 'data']),
            json_encode(['test' => true])
        );

        $array = $button->toStoreArray();

        $this->assertArrayHasKey('internal_data', $array);
        $this->assertEquals('https://example.com', $array['internal_data']);
    }

    public function test_to_update_array_includes_internal_data()
    {
        $button = new Button(
            1,
            1,
            'Test Button',
            'URL',
            'action',
            'https://example.com',
            json_encode(['test' => 'data']),
            json_encode(['test' => true])
        );

        $array = $button->toUpdateArray();

        $this->assertArrayHasKey('internal_data', $array);
        $this->assertEquals('https://example.com', $array['internal_data']);
    }
}
