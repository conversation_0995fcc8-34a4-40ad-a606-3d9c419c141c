<?php

namespace Tests\Feature\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use Tests\TestCase;

class ButtonWhatsAppPayloadTest extends TestCase
{
    public function test_url_button_whatsapp_payload_with_internal_data()
    {
        $button = new Button(
            1,
            1,
            '📱 Ver Cardápio',
            'URL',
            'action',
            'https://pizzaexpress.com.br/cardapio',
            json_encode(['action' => 'view_menu']),
            json_encode(['test' => true])
        );

        $payload = $button->toUrlWhatsAppPayload();

        $expectedPayload = [
            'type' => 'URL',
            'text' => '📱 Ver Cardápio',
            'url' => 'https://pizzaexpress.com.br/cardapio'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_phone_number_button_whatsapp_payload_with_internal_data()
    {
        $button = new Button(
            1,
            1,
            '📞 Ligar Agora',
            'PHONE_NUMBER',
            'action',
            '+5511999887766',
            json_encode(['action' => 'call_now']),
            json_encode(['test' => true])
        );

        $payload = $button->toCallWhatsAppPayload();

        $expectedPayload = [
            'type' => 'PHONE_NUMBER',
            'text' => '📞 Ligar Agora',
            'phone_number' => '+5511999887766'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_url_button_fallback_to_callback_data_when_no_internal_data()
    {
        $button = new Button(
            1,
            1,
            '🌐 Website',
            'URL',
            'action',
            null, // No internal_data
            json_encode(['url' => 'https://fallback.com']),
            json_encode(['test' => true])
        );

        $payload = $button->toUrlWhatsAppPayload();

        $expectedPayload = [
            'type' => 'URL',
            'text' => '🌐 Website',
            'url' => 'https://fallback.com'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_phone_number_button_fallback_to_callback_data_when_no_internal_data()
    {
        $button = new Button(
            1,
            1,
            '☎️ Contact',
            'PHONE_NUMBER',
            'action',
            null, // No internal_data
            json_encode(['phone_number' => '+5511000000000']),
            json_encode(['test' => true])
        );

        $payload = $button->toCallWhatsAppPayload();

        $expectedPayload = [
            'type' => 'PHONE_NUMBER',
            'text' => '☎️ Contact',
            'phone_number' => '+5511000000000'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_button_with_empty_internal_data_fallback()
    {
        $button = new Button(
            1,
            1,
            'Empty Data Button',
            'URL',
            'action',
            '', // Empty internal_data
            json_encode(['url' => 'https://backup.com']),
            json_encode(['test' => true])
        );

        $payload = $button->toUrlWhatsAppPayload();

        $expectedPayload = [
            'type' => 'URL',
            'text' => 'Empty Data Button',
            'url' => 'https://backup.com'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_button_with_no_fallback_data()
    {
        $button = new Button(
            1,
            1,
            'No Data Button',
            'URL',
            'action',
            null, // No internal_data
            json_encode(['action' => 'no_url']), // No url in callback_data
            json_encode(['test' => true])
        );

        $payload = $button->toUrlWhatsAppPayload();

        $expectedPayload = [
            'type' => 'URL',
            'text' => 'No Data Button',
            'url' => null
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_quick_reply_button_whatsapp_payload()
    {
        $button = new Button(
            1,
            1,
            'Yes',
            'QUICK_REPLY',
            'action',
            null, // No internal_data for quick reply
            json_encode(['response' => 'yes']),
            json_encode(['test' => true])
        );

        $payload = $button->toReplyWhatsAppPayload();

        $expectedPayload = [
            'type' => 'QUICK_REPLY',
            'text' => 'Yes'
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    public function test_button_to_whatsapp_payload_chooses_correct_method()
    {
        // URL button
        $urlButton = new Button(
            1,
            1,
            'Visit Site',
            'URL',
            'action',
            'https://example.com',
            json_encode(['action' => 'visit']),
            json_encode(['test' => true])
        );

        $urlPayload = $urlButton->toWhatsAppPayload();
        $this->assertEquals('url', $urlPayload['type']);
        $this->assertEquals('https://example.com', $urlPayload['url']);

        // Phone number button
        $phoneButton = new Button(
            2,
            1,
            'Call Now',
            'PHONE_NUMBER',
            'action',
            '+1234567890',
            json_encode(['action' => 'call']),
            json_encode(['test' => true])
        );

        $phonePayload = $phoneButton->toWhatsAppPayload();
        $this->assertEquals('phone_number', $phonePayload['type']);
        $this->assertEquals('+1234567890', $phonePayload['phone_number']);

        // Quick reply button
        $replyButton = new Button(
            3,
            1,
            'Confirm',
            'QUICK_REPLY',
            'action',
            null,
            json_encode(['confirm' => true]),
            json_encode(['test' => true])
        );

        $replyPayload = $replyButton->toWhatsAppPayload();
        $this->assertEquals('reply', $replyPayload['type']);
        $this->assertArrayHasKey('reply', $replyPayload);
        $this->assertEquals('Confirm', $replyPayload['reply']['title']);
    }

    public function test_button_internal_data_priority_over_callback_data()
    {
        // Test that internal_data takes priority over callback_data
        $button = new Button(
            1,
            1,
            'Priority Test',
            'URL',
            'action',
            'https://priority.com', // internal_data
            json_encode(['url' => 'https://fallback.com']), // callback_data
            json_encode(['test' => true])
        );

        $internalData = $button->getInternalDataForWhatsApp();
        $this->assertEquals('https://priority.com', $internalData);

        $payload = $button->toUrlWhatsAppPayload();
        $this->assertEquals('https://priority.com', $payload['url']);
    }
}
