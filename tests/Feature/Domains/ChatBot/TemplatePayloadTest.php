<?php

namespace Tests\Feature\Domains\ChatBot;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\PhoneNumber;
use App\Enums\ChatBot\ComponentFormat;
use Tests\TestCase;

class TemplatePayloadTest extends TestCase
{
    private function createTemplate(): Template
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511987654321',
            name: 'Test Phone',
            description: null,
            is_active: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );

        $headerComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header',
            type: 'HEADER',
            text: 'Welcome {{client.name}}!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $bodyComponent = new Component(
            id: 2,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            text: 'Hello {{client.name}}, your email is {{client.email}}. Thank you for choosing us!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $footerComponent = new Component(
            id: 3,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer',
            type: 'FOOTER',
            text: 'Contact us at {{client.phone}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        return new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'welcome_template',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$headerComponent, $bodyComponent, $footerComponent],
            phone_number: $phoneNumber
        );
    }

    public function test_template_to_whatsapp_payload()
    {
        $template = $this->createTemplate();

        $payload = $template->toWhatsAppPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('welcome_template', $payload['name']);
        $this->assertEquals('UTILITY', $payload['category']);
        $this->assertEquals('en_US', $payload['language']);
        $this->assertArrayHasKey('components', $payload);
        $this->assertCount(3, $payload['components']);

        // Check header component
        $headerPayload = $payload['components'][0];
        $this->assertEquals('HEADER', $headerPayload['type']);
        $this->assertEquals('TEXT', $headerPayload['format']);
        $this->assertEquals('Welcome {{1}}!', $headerPayload['text']);

        // Check body component
        $bodyPayload = $payload['components'][1];
        $this->assertEquals('BODY', $bodyPayload['type']);
        $this->assertEquals('Hello {{1}}, your email is {{2}}. Thank you for choosing us!', $bodyPayload['text']);

        // Check footer component
        $footerPayload = $payload['components'][2];
        $this->assertEquals('FOOTER', $footerPayload['type']);
        $this->assertEquals('Contact us at {{1}}', $footerPayload['text']);
    }

    public function test_template_with_complex_variables()
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511987654321',
            name: 'Test Phone',
            description: null,
            is_active: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );

        $bodyComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            text: 'Hi {{client.name}}, your order {{order.id}} from {{organization.name}} will be delivered to {{client.address}} in {{client.city}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'order_notification',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$bodyComponent],
            phone_number: $phoneNumber
        );

        $payload = $template->toWhatsAppPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('order_notification', $payload['name']);
        $this->assertCount(1, $payload['components']);

        $bodyPayload = $payload['components'][0];
        $this->assertEquals('BODY', $bodyPayload['type']);
        $this->assertEquals('Hi {{1}}, your order {{2}} from {{3}} will be delivered to {{4}} in {{5}}', $bodyPayload['text']);
    }

    public function test_template_with_no_variables()
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511987654321',
            name: 'Test Phone',
            description: null,
            is_active: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );

        $bodyComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            text: 'Welcome to our service! We are glad to have you.',
            format: ComponentFormat::TEXT,
            json: null
        );

        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'static_welcome',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$bodyComponent],
            phone_number: $phoneNumber
        );

        $payload = $template->toWhatsAppPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('static_welcome', $payload['name']);
        $this->assertCount(1, $payload['components']);

        $bodyPayload = $payload['components'][0];
        $this->assertEquals('BODY', $bodyPayload['type']);
        $this->assertEquals('Welcome to our service! We are glad to have you.', $bodyPayload['text']);
    }

    public function test_template_with_mixed_components()
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511987654321',
            name: 'Test Phone',
            description: null,
            is_active: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );

        $headerComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header',
            type: 'HEADER',
            text: 'Order Update',
            format: ComponentFormat::TEXT,
            json: null
        );

        $bodyComponent = new Component(
            id: 2,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            text: 'Hi {{client.name}}, your order is ready!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $footerComponent = new Component(
            id: 3,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer',
            type: 'FOOTER',
            text: 'Thank you for your business',
            format: ComponentFormat::TEXT,
            json: null
        );

        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'mixed_template',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$headerComponent, $bodyComponent, $footerComponent],
            phone_number: $phoneNumber
        );

        $payload = $template->toWhatsAppPayload();

        $this->assertIsArray($payload);
        $this->assertCount(3, $payload['components']);

        // Header with no variables
        $headerPayload = $payload['components'][0];
        $this->assertEquals('HEADER', $headerPayload['type']);
        $this->assertEquals('Order Update', $headerPayload['text']);

        // Body with variables
        $bodyPayload = $payload['components'][1];
        $this->assertEquals('BODY', $bodyPayload['type']);
        $this->assertEquals('Hi {{1}}, your order is ready!', $bodyPayload['text']);

        // Footer with no variables
        $footerPayload = $payload['components'][2];
        $this->assertEquals('FOOTER', $footerPayload['type']);
        $this->assertEquals('Thank you for your business', $footerPayload['text']);
    }
}
