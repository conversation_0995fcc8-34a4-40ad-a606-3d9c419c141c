<?php

return [
    /*
    |--------------------------------------------------------------------------
    | WhatsApp Business API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for WhatsApp Business API integration.
    | These settings are used for sending messages, managing webhooks, and
    | interacting with the Meta WhatsApp Business Platform.
    |
    */

    'phone_number_id' => env('WHATSAPP_PHONE_NUMBER_ID'),
    'business_id' => env('WHATSAPP_BUSINESS_ID'),
    'access_token' => env('WHATSAPP_ACCESS_TOKEN'),
    'base_url' => env('WHATSAPP_API_BASE_URL', 'https://graph.facebook.com/v23.0'),

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | These settings are used for webhook verification and security.
    | The verify_token is used during webhook setup, and the secret is used
    | for validating incoming webhook requests from Meta.
    |
    */

    'webhook_verify_token' => env('WHATSAPP_WEBHOOK_VERIFY_TOKEN', 'your_verify_token'),
    'webhook_secret' => env('WHATSAPP_WEBHOOK_SECRET'),
];
