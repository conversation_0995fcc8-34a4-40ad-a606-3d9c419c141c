<?php

return [
    /*
    |--------------------------------------------------------------------------
    | ChatBot Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the ChatBot system,
    | including timeouts, validation rules, and command settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Timeout Settings
    |--------------------------------------------------------------------------
    |
    | Configure timeout values for different types of interactions and
    | conversation cleanup settings.
    |
    */
    'timeouts' => [
        // Time to wait for user input in input steps (seconds)
        'input_step' => env('CHATBOT_INPUT_TIMEOUT', 300), // 5 minutes

        // Time to wait for user selection in interactive steps (seconds)
        'interactive_step' => env('CHATBOT_INTERACTIVE_TIMEOUT', 600), // 10 minutes

        // Maximum time for a complete conversation (seconds)
        'conversation' => env('CHATBOT_CONVERSATION_TIMEOUT', 3600), // 1 hour

        // Time after which conversations are considered inactive (seconds)
        'inactive_after' => env('CHATBOT_INACTIVE_TIMEOUT', 86400), // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic cleanup of inactive conversations
    | and related data.
    |
    */
    'cleanup' => [
        // How often to run cleanup process (seconds)
        'run_cleanup_every' => env('CHATBOT_CLEANUP_INTERVAL', 3600), // 1 hour

        // Whether to enable automatic cleanup
        'enabled' => env('CHATBOT_CLEANUP_ENABLED', true),

        // Whether to soft delete or hard delete inactive conversations
        'soft_delete' => env('CHATBOT_CLEANUP_SOFT_DELETE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Command Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for command step execution and validation.
    |
    */
    'commands' => [
        // Maximum execution time for commands (seconds)
        'max_execution_time' => env('CHATBOT_COMMAND_TIMEOUT', 30),

        // Whether to retry failed commands
        'retry_on_failure' => env('CHATBOT_COMMAND_RETRY', true),

        // Number of retry attempts
        'max_retries' => env('CHATBOT_COMMAND_MAX_RETRIES', 3),

        // Delay between retries (seconds)
        'retry_delay' => env('CHATBOT_COMMAND_RETRY_DELAY', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Input Validation Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for input validation in input steps.
    |
    */
    'validation' => [
        // Maximum number of validation attempts before giving up
        'max_attempts' => env('CHATBOT_VALIDATION_MAX_ATTEMPTS', 3),

        // Default validation error message
        'default_error_message' => 'Por favor, digite um valor válido.',

        // Custom validation rules for different input types
        'rules' => [
            'email' => 'required|email',
            'phone' => 'required|regex:/^[\d\s\(\)\-\+]+$/',
            'cpf' => 'required|cpf',
            'cnpj' => 'required|cnpj',
            'number' => 'required|numeric',
            'date' => 'required|date_format:d/m/Y',
        ],

        // Custom error messages for validation rules
        'messages' => [
            'email.email' => 'Por favor, digite um email válido.',
            'phone.regex' => 'Por favor, digite um telefone válido.',
            'cpf.cpf' => 'Por favor, digite um CPF válido.',
            'cnpj.cnpj' => 'Por favor, digite um CNPJ válido.',
            'number.numeric' => 'Por favor, digite apenas números.',
            'date.date_format' => 'Por favor, digite uma data no formato DD/MM/AAAA.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for error handling and recovery strategies.
    |
    */
    'error_handling' => [
        // Default error recovery strategy
        'default_strategy' => env('CHATBOT_ERROR_STRATEGY', 'retry'),

        // Available strategies: retry, fallback, escalate, reset
        'strategies' => [
            'retry' => [
                'max_attempts' => 3,
                'delay' => 5, // seconds
            ],
            'fallback' => [
                'fallback_step' => 'error_fallback',
                'fallback_message' => 'Ocorreu um erro. Vamos tentar de outra forma.',
            ],
            'escalate' => [
                'escalation_message' => 'Vou transferir você para um atendente humano.',
                'notify_team' => true,
            ],
            'reset' => [
                'reset_message' => 'Vamos começar novamente.',
                'preserve_client_data' => true,
            ],
        ],

        // Log all errors for debugging
        'log_errors' => env('CHATBOT_LOG_ERRORS', true),

        // Send error notifications to team
        'notify_on_errors' => env('CHATBOT_NOTIFY_ERRORS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Flow Validation Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for flow integrity validation.
    |
    */
    'flow_validation' => [
        // Enable flow validation before execution
        'enabled' => env('CHATBOT_FLOW_VALIDATION', true),

        // Validation rules
        'rules' => [
            'require_initial_step' => true,
            'require_ending_step' => true,
            'check_orphaned_steps' => true,
            'check_infinite_loops' => true,
            'validate_conditional_targets' => true,
        ],

        // Whether to cache validation results
        'cache_results' => env('CHATBOT_CACHE_VALIDATION', true),

        // Cache TTL in seconds
        'cache_ttl' => env('CHATBOT_VALIDATION_CACHE_TTL', 3600),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for ChatBot logging and debugging.
    |
    */
    'logging' => [
        // Log level for ChatBot operations
        'level' => env('CHATBOT_LOG_LEVEL', 'info'),

        // Log channel to use
        'channel' => env('CHATBOT_LOG_CHANNEL', 'daily'),

        // Whether to log all interactions
        'log_interactions' => env('CHATBOT_LOG_INTERACTIONS', true),

        // Whether to log step transitions
        'log_step_transitions' => env('CHATBOT_LOG_STEP_TRANSITIONS', true),

        // Whether to log command executions
        'log_command_executions' => env('CHATBOT_LOG_COMMANDS', true),
    ],
];
