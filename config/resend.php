<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Resend Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Resend email service integration.
    | This follows the same pattern as other service configurations.
    |
    */

    'api_key' => env('RESEND_API_KEY'),
    
    'api_url' => env('RESEND_API_URL', 'https://api.resend.com'),
    
    'from_email' => env('RESEND_FROM_EMAIL', '<EMAIL>'),
    
    'from_name' => env('RESEND_FROM_NAME', 'Obvio'),
    
    'support_email' => env('RESEND_SUPPORT_EMAIL', '<EMAIL>'),
    
    'timeout' => env('RESEND_TIMEOUT', 30),
    
    'retry_attempts' => env('RESEND_RETRY_ATTEMPTS', 3),
    
    'sandbox_mode' => env('RESEND_SANDBOX_MODE', false),
];
