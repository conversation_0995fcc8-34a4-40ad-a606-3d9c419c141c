# WhatsApp Business API Configuration

Este documento descreve como configurar a integração com a WhatsApp Business API no sistema.

## Variáveis de Ambiente

As seguintes variáveis de ambiente devem ser configuradas no arquivo `.env`:

### Configurações Básicas

```env
# ID do número de telefone WhatsApp Business
WHATSAPP_PHONE_NUMBER_ID="569357716260641"

# ID da conta WhatsApp Business
WHATSAPP_BUSINESS_ID="your_business_id"

# Token de acesso da API do WhatsApp
WHATSAPP_ACCESS_TOKEN="your_access_token"

# URL base da API do WhatsApp (opcional)
WHATSAPP_API_BASE_URL="https://graph.facebook.com/v23.0"
```

### Configurações de Webhook

```env
# Token de verificação do webhook
WHATSAPP_WEBHOOK_VERIFY_TOKEN="your_verify_token"

# Segredo do webhook para validação de assinatura
WHATSAPP_WEBHOOK_SECRET="your_webhook_secret"
```

## Arquivo de Configuração

O arquivo `config/whatsapp.php` contém todas as configurações centralizadas:

```php
return [
    'phone_number_id' => env('WHATSAPP_PHONE_NUMBER_ID'),
    'business_id' => env('WHATSAPP_BUSINESS_ID'),
    'access_token' => env('WHATSAPP_ACCESS_TOKEN'),
    'base_url' => env('WHATSAPP_API_BASE_URL', 'https://graph.facebook.com/v23.0'),
    'webhook_verify_token' => env('WHATSAPP_WEBHOOK_VERIFY_TOKEN', 'your_verify_token'),
    'webhook_secret' => env('WHATSAPP_WEBHOOK_SECRET'),
];
```

## Como Obter as Configurações

### 1. Phone Number ID
- Acesse o [Meta for Developers](https://developers.facebook.com/)
- Vá para seu app WhatsApp Business
- Em "WhatsApp" > "Getting Started"
- Copie o Phone Number ID

### 2. Business ID
- No mesmo painel, vá para "WhatsApp" > "Configuration"
- Encontre o Business Account ID

### 3. Access Token
- Em "WhatsApp" > "Getting Started"
- Gere um token de acesso temporário ou permanente
- **Importante**: Use tokens permanentes em produção

### 4. Webhook Verify Token
- Token personalizado que você define
- Usado durante a configuração do webhook no Meta
- Deve ser o mesmo nos dois lugares

### 5. Webhook Secret
- Segredo usado para validar a assinatura dos webhooks
- Configurado no Meta for Developers
- Usado para garantir que os webhooks vêm realmente do WhatsApp

## Configuração do Webhook

1. No Meta for Developers, vá para "WhatsApp" > "Configuration"
2. Configure a URL do webhook: `https://seu-dominio.com/api/whatsapp/webhook`
3. Use o `WHATSAPP_WEBHOOK_VERIFY_TOKEN` como verify token
4. Configure o `WHATSAPP_WEBHOOK_SECRET` para validação de assinatura
5. Subscreva aos eventos necessários (messages, message_status, etc.)

## Uso no Código

```php
// Acessar configurações
$phoneNumberId = config('whatsapp.phone_number_id');
$accessToken = config('whatsapp.access_token');
$webhookSecret = config('whatsapp.webhook_secret');

// Validar webhook
$verifyToken = config('whatsapp.webhook_verify_token');
```

## Segurança

- **Nunca** commite tokens de acesso no repositório
- Use variáveis de ambiente para todas as configurações sensíveis
- Rotacione tokens regularmente
- Valide sempre a assinatura dos webhooks usando o webhook_secret

## Troubleshooting

### Webhook não funciona
1. Verifique se a URL está acessível publicamente
2. Confirme que o verify token está correto
3. Verifique os logs do Laravel para erros

### Mensagens não são enviadas
1. Verifique se o access token é válido
2. Confirme que o phone_number_id está correto
3. Verifique se a conta WhatsApp Business está ativa

### Erro de autenticação
1. Regenere o access token
2. Verifique se o token tem as permissões necessárias
3. Confirme que não expirou
