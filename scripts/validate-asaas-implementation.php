<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Domains\Organization;
use App\Domains\Subscription;
use App\Services\ASAAS\Domains\AsaasSubscription;
use Carbon\Carbon;

echo "🔍 Validando Implementação ASAAS...\n\n";

// 1. Testar instanciação do Subscription Domain
echo "1. Testando instanciação do Subscription Domain...\n";
try {
    $subscription = new Subscription(
        id: 1,
        organization_id: 1,
        type: 'trial',
        status: 'active',
        value: 100.00,
        started_at: Carbon::now(),
        expires_at: Carbon::now()->addMonth(),
        is_courtesy: false,
        courtesy_expires_at: null,
        courtesy_reason: null,
        allowed_modules: ['module1'],
        created_at: Carbon::now(),
        updated_at: Carbon::now()
    );

    echo "✅ Subscription Domain instanciado com sucesso\n";
    echo "   - ID: {$subscription->id}\n";
    echo "   - Type: {$subscription->type}\n";
    echo "   - Status: {$subscription->status}\n";
    echo "   - Is Active: " . ($subscription->isActive() ? 'true' : 'false') . "\n";
    echo "   - Can Access Module1: " . ($subscription->canAccessModule('module1') ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "❌ Erro ao instanciar Subscription Domain: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. Testar instanciação do AsaasSubscription Domain
echo "2. Testando instanciação do AsaasSubscription Domain...\n";
try {
    $organization = new Organization(
        id: 1,
        name: "Test Organization",
        description: "Test Description",
        is_active: true,
        is_suspended: false,
        default_flow_id: null,
        created_at: Carbon::now(),
        updated_at: Carbon::now()
    );

    $asaasSubscription = new AsaasSubscription(
        id: 1,
        subscription_id: 1,
        subscription: $subscription,
        asaas_subscription_id: 'asaas_123',
        asaas_customer_id: 'cus_123',
        sync_status: 'synced',
        asaas_synced_at: Carbon::now(),
        asaas_sync_errors: null,
        asaas_webhook_data: null,
        created_at: Carbon::now(),
        updated_at: Carbon::now()
    );

    echo "✅ AsaasSubscription Domain instanciado com sucesso\n";
    echo "   - ID: {$asaasSubscription->id}\n";
    echo "   - Subscription ID: {$asaasSubscription->subscription_id}\n";
    echo "   - ASAAS Subscription ID: {$asaasSubscription->asaas_subscription_id}\n";
    echo "   - Is Synced: " . ($asaasSubscription->isSynced() ? 'true' : 'false') . "\n";
    echo "   - Has Errors: " . ($asaasSubscription->hasErrors() ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "❌ Erro ao instanciar AsaasSubscription Domain: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. Testar métodos toArray()
echo "3. Testando métodos toArray()...\n";
try {
    $subscriptionArray = $subscription->toArray();
    $asaasSubscriptionArray = $asaasSubscription->toArray();

    echo "✅ Métodos toArray() funcionando\n";
    echo "   - Subscription array keys: " . implode(', ', array_keys($subscriptionArray)) . "\n";
    echo "   - AsaasSubscription array keys: " . implode(', ', array_keys($asaasSubscriptionArray)) . "\n";
} catch (Exception $e) {
    echo "❌ Erro nos métodos toArray(): " . $e->getMessage() . "\n";
}

echo "\n";

// 4. Verificar se classes existem
echo "4. Verificando se todas as classes existem...\n";

$classes = [
    'App\Domains\Subscription',
    'App\Models\Subscription',
    'App\Factories\SubscriptionFactory',
    'App\Repositories\SubscriptionRepository',
    'App\UseCases\Subscription\CreateSubscription',
    'App\UseCases\Subscription\UpdateSubscription',
    'App\UseCases\Subscription\GrantCourtesy',
    'App\UseCases\Subscription\RevokeCourtesy',
    'App\UseCases\Subscription\GetSubscriptionById',
    'App\UseCases\Subscription\GetSubscriptionByOrganization',
    'App\Http\Controllers\SubscriptionController',
    'App\Services\ASAAS\Domains\AsaasSubscription',
    'App\Services\ASAAS\Factories\AsaasSubscriptionFactory',
    'App\Services\ASAAS\Repositories\AsaasSubscriptionRepository',
    'App\Services\ASAAS\UseCases\Deprecated\Subscriptions\CreateAsaasSubscription',
    'App\Services\ASAAS\UseCases\Deprecated\Subscriptions\SyncAsaasSubscription',
    'App\Services\ASAAS\UseCases\Deprecated\Subscriptions\GetAsaasSubscriptionStatus',
    'App\Http\Controllers\ASAAS\OldSubscriptionController',
];

$missing = [];
foreach ($classes as $class) {
    if (!class_exists($class)) {
        $missing[] = $class;
    }
}

if (empty($missing)) {
    echo "✅ Todas as classes existem e são carregáveis\n";
} else {
    echo "❌ Classes faltando:\n";
    foreach ($missing as $class) {
        echo "   - {$class}\n";
    }
}

echo "\n";

// 5. Verificar se tabelas existem
echo "5. Verificando se tabelas existem...\n";
try {
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    $app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

    $tables = ['subscriptions', 'asaas_subscriptions'];
    foreach ($tables as $table) {
        if (\Illuminate\Support\Facades\Schema::hasTable($table)) {
            echo "✅ Tabela '{$table}' existe\n";
        } else {
            echo "❌ Tabela '{$table}' não existe\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar tabelas: " . $e->getMessage() . "\n";
}

echo "\n🎉 Validação completa!\n";
