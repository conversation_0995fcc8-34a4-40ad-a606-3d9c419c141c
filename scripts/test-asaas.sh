#!/bin/bash

# ASAAS Service Test Runner
# This script runs all ASAAS-related tests with proper organization and reporting

echo "🚀 Starting ASAAS Service Test Suite"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "Please run this script from the Laravel project root directory"
    exit 1
fi

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    print_error "Vendor directory not found. Please run 'composer install' first"
    exit 1
fi

# Set test environment
export APP_ENV=testing

print_status "Setting up test environment..."

# Clear and prepare test database
print_status "Preparing test database..."
php artisan config:clear --env=testing
php artisan cache:clear --env=testing

# Run database migrations for testing
print_status "Running test migrations..."
php artisan migrate:fresh --env=testing --seed

echo ""
print_status "Running ASAAS Unit Tests..."
echo "=============================="

# Run Unit Tests for Domains
echo ""
print_status "Testing ASAAS Domains..."
php artisan test tests/Unit/Services/ASAAS/Domains/ --verbose

# Run Unit Tests for Factories
echo ""
print_status "Testing ASAAS Factories..."
php artisan test tests/Unit/Services/ASAAS/Factories/ --verbose

# Run Unit Tests for Repositories
echo ""
print_status "Testing ASAAS Repositories..."
php artisan test tests/Unit/Services/ASAAS/Repositories/ --verbose

# Run Unit Tests for Use Cases
echo ""
print_status "Testing ASAAS Use Cases..."
php artisan test tests/Unit/Services/ASAAS/UseCases/ --verbose

# Run Unit Tests for AsaasService
echo ""
print_status "Testing ASAAS Service..."
php artisan test tests/Unit/Services/ASAAS/AsaasServiceTest.php --verbose

echo ""
print_status "Running ASAAS Feature Tests..."
echo "==============================="

# Run Feature Tests for Service Integration
echo ""
print_status "Testing ASAAS Service Integration..."
php artisan test tests/Feature/Services/ASAAS/AsaasServiceIntegrationTest.php --verbose

# Run Feature Tests for Organization Workflow
echo ""
print_status "Testing Organization Workflow..."
php artisan test tests/Feature/Services/ASAAS/Organizations/OrganizationWorkflowTest.php --verbose

# Run Feature Tests for Commands
echo ""
print_status "Testing ASAAS Commands..."
php artisan test tests/Feature/Services/ASAAS/Commands/ --verbose

# Run Complete Test Suite
echo ""
print_status "Running Complete ASAAS Test Suite..."
php artisan test tests/Feature/Services/ASAAS/AsaasTestSuite.php --verbose

echo ""
print_status "Running All ASAAS Tests Together..."
echo "==================================="

# Run all ASAAS tests at once with coverage (if available)
if command -v phpunit &> /dev/null; then
    print_status "Running with PHPUnit and coverage..."
    vendor/bin/phpunit tests/Unit/Services/ASAAS/ tests/Feature/Services/ASAAS/ \
        --coverage-html storage/app/test-coverage/asaas \
        --coverage-text \
        --colors=always
else
    print_status "Running with Artisan test command..."
    php artisan test tests/Unit/Services/ASAAS/ tests/Feature/Services/ASAAS/ \
        --parallel \
        --verbose
fi

# Check test results
TEST_EXIT_CODE=$?

echo ""
echo "=============================="
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "All ASAAS tests passed! ✅"
    print_status "Test coverage report available at: storage/app/test-coverage/asaas/index.html"
else
    print_error "Some ASAAS tests failed! ❌"
    print_warning "Please check the output above for details"
fi

echo ""
print_status "Test Summary:"
echo "- Unit Tests: Domains, Factories, Repositories, Use Cases, Service"
echo "- Feature Tests: Integration, Workflows, Commands"
echo "- Coverage: HTML report generated (if PHPUnit available)"

echo ""
print_status "To run specific test categories:"
echo "- Unit tests only: php artisan test tests/Unit/Services/ASAAS/"
echo "- Feature tests only: php artisan test tests/Feature/Services/ASAAS/"
echo "- Specific domain: php artisan test tests/Unit/Services/ASAAS/Domains/AsaasClientTest.php"
echo "- With coverage: vendor/bin/phpunit tests/Unit/Services/ASAAS/ --coverage-html storage/coverage"

echo ""
print_status "ASAAS Test Suite completed!"

exit $TEST_EXIT_CODE
