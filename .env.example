APP_NAME=Obvio
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TELEGRAM_DEV_BOT_ID="FlechAiBot"
TELEGRAM_DEV_BOT_TOKEN="**********************************************"
TELEGRAM_DEV_API_URL="https://dev.mob2o.fellipemanzano.com.br/api/telegram/receive-message"

TELEGRAM_BOT_ID="FlechAiBot"
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_API_URL="https://dev.mob2o.fellipemanzano.com.br/api/telegram/receive-message"

TELEGRAM_CUSTOM_API_URL="https://dev.mob2o.fellipemanzano.com.br/api/telegram/{bot_id}/receive-message"

# WhatsApp Business API Configuration
WHATSAPP_PHONE_NUMBER_ID="569357716260641"
WHATSAPP_BUSINESS_ID="your_business_id"
WHATSAPP_ACCESS_TOKEN="EAAYWJMXztCYBO7dpKwNSLFqeCQOQlCbZCpDKR1QnBRuXEbuETnmzk8CCdD0UfMwPskTD6HvsZBFnnwo7BaNsTkHY8ZCab2QUiUNdTMRXS4Fkc708lLd3Cu3MnXNLsExher6BljrwQmiNcvU5O1rZAbGdonTJfQZCoCZCLUdTlqA8WDhBXIlVQlSNE5l244QqS4naLsaCGwiw85HfWYMNTgmPIQoWRn0QIZC5qH86ZCrn"
WHATSAPP_API_BASE_URL="https://graph.facebook.com/v23.0"
WHATSAPP_WEBHOOK_VERIFY_TOKEN="your_verify_token"
WHATSAPP_WEBHOOK_SECRET="your_webhook_secret"

# Resend Email Service Configuration
RESEND_API_KEY="re_Fg6M5CK3_Dy1G2dyapndWdLGv3F6roHFJ"
RESEND_API_URL="https://api.resend.com"
RESEND_FROM_EMAIL="<EMAIL>"
RESEND_FROM_NAME="${APP_NAME}"
RESEND_SUPPORT_EMAIL="<EMAIL>"
RESEND_TIMEOUT=30
RESEND_RETRY_ATTEMPTS=3
RESEND_SANDBOX_MODE=false

# ASAAS Payment Service Configuration
ASAAS_ENV=sandbox
ASAAS_TOKEN_SANDBOX="$aact_hmlg_000MzkwODA2MWY2OGM3MWRlMDU2NWM3MzJlNzZmNGZhZGY6OmVjYmU4MmQxLTk0YmQtNDhkZS05MTc5LTI2N2M4OGY3Zjg0MDo6JGFhY2hfYzExMGQwY2EtOWFkOC00Mzc0LWIzZjgtZDBkMGFmM2E1ZjEz"
ASAAS_TOKEN_PRODUCTION=""
ASAAS_URL_SANDBOX="https://api-sandbox.asaas.com"
ASAAS_URL_PRODUCTION="https://api.asaas.com"
ASAAS_TIMEOUT=30
ASAAS_RETRY_ATTEMPTS=3
ASAAS_RETRY_DELAY=1000
ASAAS_LOG_REQUESTS=true
ASAAS_LOG_RESPONSES=true
ASAAS_LOG_ERRORS_ONLY=false
